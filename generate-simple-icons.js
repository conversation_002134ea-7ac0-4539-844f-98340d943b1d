#!/usr/bin/env node

/**
 * 简单图标生成器
 * 使用Node.js Canvas API生成基础图标文件
 */

const fs = require('fs');
const path = require('path');

// 检查是否有canvas模块，如果没有则使用简单的SVG方案
let Canvas, createCanvas, loadImage;
try {
    const canvas = require('canvas');
    Canvas = canvas.Canvas;
    createCanvas = canvas.createCanvas;
    loadImage = canvas.loadImage;
    console.log('✅ 使用Canvas模块生成高质量图标');
} catch (error) {
    console.log('⚠️  Canvas模块未安装，使用SVG方案生成图标');
}

/**
 * 图标配置
 */
const iconConfigs = [
    { size: 16, name: 'favicon-16x16' },
    { size: 32, name: 'favicon-32x32' },
    { size: 72, name: 'icon-72x72' },
    { size: 96, name: 'icon-96x96' },
    { size: 128, name: 'icon-128x128' },
    { size: 144, name: 'icon-144x144' },
    { size: 152, name: 'icon-152x152' },
    { size: 192, name: 'icon-192x192' },
    { size: 384, name: 'icon-384x384' },
    { size: 512, name: 'icon-512x512' }
];

const gameIcons = [
    { size: 96, name: 'temporal-icon-96x96', theme: 'temporal' },
    { size: 96, name: 'spark-icon-96x96', theme: 'spark' },
    { size: 96, name: 'quantum-icon-96x96', theme: 'quantum' }
];

/**
 * 创建SVG图标
 */
function createSVGIcon(size, theme = 'main') {
    const themes = {
        main: { bg: '#4a90e2', accent: '#2c5aa0', text: 'S' },
        temporal: { bg: '#6a4c93', accent: '#c06c84', text: '时' },
        spark: { bg: '#ff6b6b', accent: '#feca57', text: '瞬' },
        quantum: { bg: '#4834d4', accent: '#686de0', text: '量' }
    };

    const config = themes[theme] || themes.main;
    
    return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <radialGradient id="bg-gradient" cx="50%" cy="50%" r="50%">
            <stop offset="0%" style="stop-color:${config.accent};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${config.bg};stop-opacity:1" />
        </radialGradient>
    </defs>
    
    <!-- 背景 -->
    <rect width="${size}" height="${size}" fill="url(#bg-gradient)" />
    
    <!-- 边框 -->
    <rect x="1" y="1" width="${size-2}" height="${size-2}" fill="none" stroke="#ffffff" stroke-width="2" opacity="0.3"/>
    
    <!-- 文字 -->
    <text x="${size/2}" y="${size/2}" text-anchor="middle" dominant-baseline="central" 
          fill="#ffffff" font-family="Arial, sans-serif" font-size="${size*0.4}" font-weight="bold">
        ${config.text}
    </text>
    
    <!-- 光晕效果 -->
    <circle cx="${size/2}" cy="${size/2}" r="${size*0.3}" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.2"/>
</svg>`;
}

/**
 * 将SVG转换为PNG (如果有canvas支持)
 */
async function svgToPng(svgContent, size, outputPath) {
    if (!createCanvas) {
        // 如果没有canvas，直接保存SVG文件
        const svgPath = outputPath.replace('.png', '.svg');
        fs.writeFileSync(svgPath, svgContent);
        console.log(`  ✅ 创建SVG: ${svgPath}`);
        return;
    }

    try {
        const canvas = createCanvas(size, size);
        const ctx = canvas.getContext('2d');
        
        // 简单的canvas绘制 (替代SVG渲染)
        const gradient = ctx.createRadialGradient(size/2, size/2, 0, size/2, size/2, size/2);
        gradient.addColorStop(0, '#4a90e2');
        gradient.addColorStop(1, '#2c5aa0');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, size, size);
        
        // 绘制文字
        ctx.fillStyle = '#ffffff';
        ctx.font = `bold ${size * 0.4}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('S', size/2, size/2);
        
        // 保存PNG
        const buffer = canvas.toBuffer('image/png');
        fs.writeFileSync(outputPath, buffer);
        console.log(`  ✅ 创建PNG: ${outputPath}`);
    } catch (error) {
        console.error(`  ❌ 创建PNG失败: ${error.message}`);
        // 回退到SVG
        const svgPath = outputPath.replace('.png', '.svg');
        fs.writeFileSync(svgPath, svgContent);
        console.log(`  ✅ 回退到SVG: ${svgPath}`);
    }
}

/**
 * 创建简单的PNG文件 (无依赖方案)
 */
function createSimplePNG(size, outputPath) {
    // 创建一个最小的PNG文件头
    const width = size;
    const height = size;
    
    // 简单的蓝色方块PNG (Base64编码)
    const pngData = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
        0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
        0x49, 0x48, 0x44, 0x52, // IHDR
        0x00, 0x00, 0x00, width, // Width (4 bytes)
        0x00, 0x00, 0x00, height, // Height (4 bytes)
        0x08, 0x02, 0x00, 0x00, 0x00, // Bit depth, color type, compression, filter, interlace
        0x90, 0x91, 0x68, 0x36, // CRC
        0x00, 0x00, 0x00, 0x0C, // IDAT chunk length
        0x49, 0x44, 0x41, 0x54, // IDAT
        0x08, 0x1D, 0x01, 0x01, 0x00, 0x00, 0xFE, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, // Compressed data
        0x00, 0x00, 0x00, 0x00, // IEND chunk length
        0x49, 0x45, 0x4E, 0x44, // IEND
        0xAE, 0x42, 0x60, 0x82  // CRC
    ]);
    
    try {
        fs.writeFileSync(outputPath, pngData);
        console.log(`  ✅ 创建简单PNG: ${outputPath}`);
    } catch (error) {
        console.error(`  ❌ 创建PNG失败: ${error.message}`);
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🎨 开始生成Split-Second Spark图标...\n');
    
    // 确保目录存在
    const assetsDir = path.join(__dirname, 'assets', 'images');
    if (!fs.existsSync(assetsDir)) {
        fs.mkdirSync(assetsDir, { recursive: true });
        console.log('📁 创建assets/images目录');
    }
    
    console.log('📱 生成主应用图标...');
    
    // 生成主应用图标
    for (const config of iconConfigs) {
        const outputPath = path.join(assetsDir, `${config.name}.png`);
        
        if (createCanvas) {
            // 使用Canvas生成
            const svgContent = createSVGIcon(config.size, 'main');
            await svgToPng(svgContent, config.size, outputPath);
        } else {
            // 使用简单PNG方案
            createSimplePNG(config.size, outputPath);
        }
    }
    
    console.log('\n🎮 生成游戏特定图标...');
    
    // 生成游戏特定图标
    for (const config of gameIcons) {
        const outputPath = path.join(assetsDir, `${config.name}.png`);
        
        if (createCanvas) {
            const svgContent = createSVGIcon(config.size, config.theme);
            await svgToPng(svgContent, config.size, outputPath);
        } else {
            createSimplePNG(config.size, outputPath);
        }
    }
    
    console.log('\n📸 生成截图占位符...');
    
    // 生成截图占位符
    const screenshots = [
        { width: 1280, height: 720, name: 'screenshot-wide.png' },
        { width: 720, height: 1280, name: 'screenshot-narrow.png' }
    ];
    
    for (const screenshot of screenshots) {
        const outputPath = path.join(assetsDir, screenshot.name);
        createSimplePNG(Math.max(screenshot.width, screenshot.height), outputPath);
    }
    
    console.log(`\n🎉 图标生成完成！`);
    console.log(`📁 生成位置: ${assetsDir}`);
    console.log(`📊 共生成 ${iconConfigs.length + gameIcons.length + 2} 个文件`);
    
    // 列出生成的文件
    console.log('\n📋 生成的文件列表:');
    const files = fs.readdirSync(assetsDir);
    files.forEach(file => {
        console.log(`  • ${file}`);
    });
}

// 运行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 生成图标时出错:', error);
        process.exit(1);
    });
}

module.exports = { createSVGIcon, main };
