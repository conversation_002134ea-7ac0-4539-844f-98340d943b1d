/**
 * 时空织梦者 - 时间操控引擎
 * 核心时间管理系统，支持时间暂停、倒流、加速等功能
 * 
 * 功能特性:
 * - 时间状态管理 (正常/暂停/倒流/加速)
 * - 时间线记录和回放
 * - 时间片段管理
 * - 时间相关事件处理
 * - 时间同步和插值
 */

class TimeEngine {
    constructor() {
        // 时间状态枚举
        this.TimeState = {
            NORMAL: 'normal',
            PAUSED: 'paused',
            REWIND: 'rewind',
            FAST_FORWARD: 'fast_forward'
        };

        // 当前时间状态
        this.currentState = this.TimeState.NORMAL;
        this.previousState = this.TimeState.NORMAL;

        // 时间控制参数
        this.timeScale = 1.0;           // 时间缩放比例
        this.rewindSpeed = -2.0;        // 倒流速度
        this.fastForwardSpeed = 3.0;    // 快进速度
        this.maxTimelineLength = 1000;  // 最大时间线长度

        // 时间线数据
        this.timeline = [];             // 时间线快照数组
        this.currentTimeIndex = 0;      // 当前时间索引
        this.recordingEnabled = true;   // 是否启用录制

        // 时间相关变量
        this.gameTime = 0;              // 游戏时间（毫秒）
        this.realTime = 0;              // 真实时间（毫秒）
        this.deltaTime = 0;             // 帧间时间差
        this.lastFrameTime = 0;         // 上一帧时间

        // 事件监听器
        this.eventListeners = new Map();

        // 时间片段管理
        this.timeSlices = [];           // 时间片段数组
        this.activeSliceIndex = -1;     // 当前活跃的时间片段

        console.log('⏰ 时间引擎初始化完成');
    }

    /**
     * 初始化时间引擎
     */
    init() {
        this.lastFrameTime = performance.now();
        this.realTime = 0;
        this.gameTime = 0;
        this.timeline = [];
        this.currentTimeIndex = 0;
        
        console.log('✅ 时间引擎已启动');
    }

    /**
     * 更新时间引擎（每帧调用）
     * @param {number} currentTime - 当前真实时间
     */
    update(currentTime) {
        // 计算时间差
        this.deltaTime = currentTime - this.lastFrameTime;
        this.lastFrameTime = currentTime;
        this.realTime = currentTime;

        // 根据时间状态更新游戏时间
        switch (this.currentState) {
            case this.TimeState.NORMAL:
                this.gameTime += this.deltaTime * this.timeScale;
                this._recordTimelineSnapshot();
                break;

            case this.TimeState.PAUSED:
                // 时间暂停，游戏时间不变
                break;

            case this.TimeState.REWIND:
                this._handleRewind();
                break;

            case this.TimeState.FAST_FORWARD:
                this.gameTime += this.deltaTime * this.fastForwardSpeed;
                this._recordTimelineSnapshot();
                break;
        }

        // 更新时间片段
        this._updateTimeSlices();

        // 触发时间更新事件
        this._emitEvent('timeUpdate', {
            gameTime: this.gameTime,
            realTime: this.realTime,
            deltaTime: this.deltaTime,
            timeState: this.currentState,
            timeScale: this.timeScale
        });
    }

    /**
     * 设置时间状态
     * @param {string} newState - 新的时间状态
     */
    setTimeState(newState) {
        if (!Object.values(this.TimeState).includes(newState)) {
            console.warn(`⚠️ 无效的时间状态: ${newState}`);
            return;
        }

        const oldState = this.currentState;
        this.previousState = oldState;
        this.currentState = newState;

        console.log(`⏰ 时间状态变更: ${oldState} → ${newState}`);

        // 触发状态变更事件
        this._emitEvent('stateChanged', {
            oldState,
            newState,
            timeScale: this._getTimeScaleForState(newState)
        });
    }

    /**
     * 获取指定状态的时间缩放比例
     * @param {string} state - 时间状态
     * @returns {number} 时间缩放比例
     */
    _getTimeScaleForState(state) {
        switch (state) {
            case this.TimeState.NORMAL:
                return 1.0;
            case this.TimeState.PAUSED:
                return 0.0;
            case this.TimeState.REWIND:
                return this.rewindSpeed;
            case this.TimeState.FAST_FORWARD:
                return this.fastForwardSpeed;
            default:
                return 1.0;
        }
    }

    /**
     * 暂停时间
     */
    pauseTime() {
        this.setTimeState(this.TimeState.PAUSED);
    }

    /**
     * 恢复时间
     */
    resumeTime() {
        this.setTimeState(this.TimeState.NORMAL);
    }

    /**
     * 开始时间倒流
     */
    startRewind() {
        this.setTimeState(this.TimeState.REWIND);
    }

    /**
     * 开始时间快进
     */
    startFastForward() {
        this.setTimeState(this.TimeState.FAST_FORWARD);
    }

    /**
     * 切换时间暂停状态
     */
    togglePause() {
        if (this.currentState === this.TimeState.PAUSED) {
            this.resumeTime();
        } else {
            this.pauseTime();
        }
    }

    /**
     * 处理时间倒流逻辑
     */
    _handleRewind() {
        if (this.timeline.length === 0 || this.currentTimeIndex <= 0) {
            // 没有可倒流的数据，恢复正常时间
            this.setTimeState(this.TimeState.NORMAL);
            return;
        }

        // 计算倒流步数
        const rewindSteps = Math.floor(Math.abs(this.rewindSpeed) * this.deltaTime / 16.67); // 假设60FPS
        this.currentTimeIndex = Math.max(0, this.currentTimeIndex - rewindSteps);

        // 获取倒流到的时间点数据
        const snapshot = this.timeline[this.currentTimeIndex];
        if (snapshot) {
            this.gameTime = snapshot.gameTime;
            
            // 触发倒流事件，让游戏对象恢复到指定状态
            this._emitEvent('rewindToSnapshot', {
                snapshot,
                timeIndex: this.currentTimeIndex
            });
        }
    }

    /**
     * 记录时间线快照
     */
    _recordTimelineSnapshot() {
        if (!this.recordingEnabled) return;

        // 创建当前状态快照
        const snapshot = {
            gameTime: this.gameTime,
            realTime: this.realTime,
            timeIndex: this.currentTimeIndex,
            timestamp: Date.now()
        };

        // 如果正在倒流后重新开始，清除后续的时间线
        if (this.currentTimeIndex < this.timeline.length - 1) {
            this.timeline = this.timeline.slice(0, this.currentTimeIndex + 1);
        }

        // 添加新快照
        this.timeline.push(snapshot);
        this.currentTimeIndex = this.timeline.length - 1;

        // 限制时间线长度
        if (this.timeline.length > this.maxTimelineLength) {
            this.timeline.shift();
            this.currentTimeIndex--;
        }

        // 触发快照记录事件
        this._emitEvent('snapshotRecorded', { snapshot });
    }

    /**
     * 跳转到指定时间点
     * @param {number} timeIndex - 时间索引
     */
    jumpToTime(timeIndex) {
        if (timeIndex < 0 || timeIndex >= this.timeline.length) {
            console.warn(`⚠️ 无效的时间索引: ${timeIndex}`);
            return;
        }

        this.currentTimeIndex = timeIndex;
        const snapshot = this.timeline[timeIndex];
        
        if (snapshot) {
            this.gameTime = snapshot.gameTime;
            
            // 触发时间跳转事件
            this._emitEvent('timeJump', {
                snapshot,
                timeIndex
            });
        }
    }

    /**
     * 创建时间片段
     * @param {number} startTime - 开始时间
     * @param {number} endTime - 结束时间
     * @param {string} name - 片段名称
     * @returns {object} 时间片段对象
     */
    createTimeSlice(startTime, endTime, name = '') {
        const slice = {
            id: Date.now() + Math.random(),
            name,
            startTime,
            endTime,
            duration: endTime - startTime,
            isActive: false,
            createdAt: Date.now()
        };

        this.timeSlices.push(slice);
        console.log(`⏰ 创建时间片段: ${name} (${startTime}ms - ${endTime}ms)`);
        
        return slice;
    }

    /**
     * 激活时间片段
     * @param {string|number} sliceId - 片段ID或索引
     */
    activateTimeSlice(sliceId) {
        let slice;
        
        if (typeof sliceId === 'number') {
            slice = this.timeSlices[sliceId];
            this.activeSliceIndex = sliceId;
        } else {
            const index = this.timeSlices.findIndex(s => s.id === sliceId);
            if (index !== -1) {
                slice = this.timeSlices[index];
                this.activeSliceIndex = index;
            }
        }

        if (slice) {
            // 停用其他片段
            this.timeSlices.forEach(s => s.isActive = false);
            
            // 激活指定片段
            slice.isActive = true;
            
            console.log(`⏰ 激活时间片段: ${slice.name}`);
            
            // 触发片段激活事件
            this._emitEvent('sliceActivated', { slice });
        }
    }

    /**
     * 更新时间片段状态
     */
    _updateTimeSlices() {
        this.timeSlices.forEach((slice, index) => {
            if (slice.isActive) {
                const progress = MathUtils.clamp(
                    (this.gameTime - slice.startTime) / slice.duration,
                    0, 1
                );

                // 触发片段更新事件
                this._emitEvent('sliceUpdate', {
                    slice,
                    progress,
                    currentTime: this.gameTime
                });

                // 检查片段是否结束
                if (this.gameTime >= slice.endTime) {
                    slice.isActive = false;
                    this.activeSliceIndex = -1;
                    
                    this._emitEvent('sliceCompleted', { slice });
                }
            }
        });
    }

    /**
     * 添加事件监听器
     * @param {string} eventType - 事件类型
     * @param {function} callback - 回调函数
     */
    addEventListener(eventType, callback) {
        if (!this.eventListeners.has(eventType)) {
            this.eventListeners.set(eventType, []);
        }
        this.eventListeners.get(eventType).push(callback);
    }

    /**
     * 移除事件监听器
     * @param {string} eventType - 事件类型
     * @param {function} callback - 回调函数
     */
    removeEventListener(eventType, callback) {
        if (this.eventListeners.has(eventType)) {
            const listeners = this.eventListeners.get(eventType);
            const index = listeners.indexOf(callback);
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * 触发事件
     * @param {string} eventType - 事件类型
     * @param {object} data - 事件数据
     */
    _emitEvent(eventType, data) {
        if (this.eventListeners.has(eventType)) {
            this.eventListeners.get(eventType).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`⚠️ 时间引擎事件处理错误 (${eventType}):`, error);
                }
            });
        }
    }

    /**
     * 获取当前时间状态信息
     * @returns {object} 时间状态信息
     */
    getTimeInfo() {
        return {
            currentState: this.currentState,
            previousState: this.previousState,
            gameTime: this.gameTime,
            realTime: this.realTime,
            deltaTime: this.deltaTime,
            timeScale: this.timeScale,
            timelineLength: this.timeline.length,
            currentTimeIndex: this.currentTimeIndex,
            activeSliceIndex: this.activeSliceIndex,
            recordingEnabled: this.recordingEnabled
        };
    }

    /**
     * 重置时间引擎
     */
    reset() {
        this.currentState = this.TimeState.NORMAL;
        this.previousState = this.TimeState.NORMAL;
        this.timeScale = 1.0;
        this.gameTime = 0;
        this.realTime = 0;
        this.deltaTime = 0;
        this.timeline = [];
        this.currentTimeIndex = 0;
        this.timeSlices = [];
        this.activeSliceIndex = -1;
        
        console.log('🔄 时间引擎已重置');
        
        this._emitEvent('engineReset', {});
    }

    /**
     * 启用/禁用时间线录制
     * @param {boolean} enabled - 是否启用
     */
    setRecordingEnabled(enabled) {
        this.recordingEnabled = enabled;
        console.log(`⏰ 时间线录制: ${enabled ? '启用' : '禁用'}`);
    }

    /**
     * 获取时间线进度 (0-1)
     * @returns {number} 进度值
     */
    getTimelineProgress() {
        if (this.timeline.length === 0) return 0;
        return this.currentTimeIndex / (this.timeline.length - 1);
    }

    /**
     * 设置时间线进度
     * @param {number} progress - 进度值 (0-1)
     */
    setTimelineProgress(progress) {
        if (this.timeline.length === 0) return;
        
        const targetIndex = Math.floor(progress * (this.timeline.length - 1));
        this.jumpToTime(targetIndex);
    }
}

// 导出时间引擎类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { TimeEngine };
} else {
    window.TimeEngine = TimeEngine;
}
