/**
 * 时空织梦者 - 数学工具函数库
 * 提供游戏中需要的各种数学计算功能
 * 
 * 功能特性:
 * - 向量运算 (Vector2D)
 * - 几何计算 (距离、角度、碰撞检测)
 * - 插值和缓动函数
 * - 随机数生成
 * - 时间相关计算
 */

/**
 * 二维向量类
 * 用于处理位置、速度、方向等二维向量运算
 */
class Vector2D {
    constructor(x = 0, y = 0) {
        this.x = x;
        this.y = y;
    }

    /**
     * 向量加法
     * @param {Vector2D} other - 另一个向量
     * @returns {Vector2D} 新的向量
     */
    add(other) {
        return new Vector2D(this.x + other.x, this.y + other.y);
    }

    /**
     * 向量减法
     * @param {Vector2D} other - 另一个向量
     * @returns {Vector2D} 新的向量
     */
    subtract(other) {
        return new Vector2D(this.x - other.x, this.y - other.y);
    }

    /**
     * 向量标量乘法
     * @param {number} scalar - 标量值
     * @returns {Vector2D} 新的向量
     */
    multiply(scalar) {
        return new Vector2D(this.x * scalar, this.y * scalar);
    }

    /**
     * 向量标量除法
     * @param {number} scalar - 标量值
     * @returns {Vector2D} 新的向量
     */
    divide(scalar) {
        if (scalar === 0) {
            console.warn('⚠️ 向量除零操作');
            return new Vector2D(0, 0);
        }
        return new Vector2D(this.x / scalar, this.y / scalar);
    }

    /**
     * 计算向量长度
     * @returns {number} 向量长度
     */
    magnitude() {
        return Math.sqrt(this.x * this.x + this.y * this.y);
    }

    /**
     * 计算向量长度的平方（避免开方运算）
     * @returns {number} 向量长度的平方
     */
    magnitudeSquared() {
        return this.x * this.x + this.y * this.y;
    }

    /**
     * 向量归一化
     * @returns {Vector2D} 单位向量
     */
    normalize() {
        const mag = this.magnitude();
        if (mag === 0) {
            return new Vector2D(0, 0);
        }
        return this.divide(mag);
    }

    /**
     * 计算两向量的点积
     * @param {Vector2D} other - 另一个向量
     * @returns {number} 点积值
     */
    dot(other) {
        return this.x * other.x + this.y * other.y;
    }

    /**
     * 计算两向量的叉积（2D中返回标量）
     * @param {Vector2D} other - 另一个向量
     * @returns {number} 叉积值
     */
    cross(other) {
        return this.x * other.y - this.y * other.x;
    }

    /**
     * 计算向量角度（弧度）
     * @returns {number} 角度（弧度）
     */
    angle() {
        return Math.atan2(this.y, this.x);
    }

    /**
     * 向量旋转
     * @param {number} angle - 旋转角度（弧度）
     * @returns {Vector2D} 旋转后的向量
     */
    rotate(angle) {
        const cos = Math.cos(angle);
        const sin = Math.sin(angle);
        return new Vector2D(
            this.x * cos - this.y * sin,
            this.x * sin + this.y * cos
        );
    }

    /**
     * 向量线性插值
     * @param {Vector2D} target - 目标向量
     * @param {number} t - 插值参数 (0-1)
     * @returns {Vector2D} 插值结果
     */
    lerp(target, t) {
        return new Vector2D(
            MathUtils.lerp(this.x, target.x, t),
            MathUtils.lerp(this.y, target.y, t)
        );
    }

    /**
     * 复制向量
     * @returns {Vector2D} 新的向量副本
     */
    clone() {
        return new Vector2D(this.x, this.y);
    }

    /**
     * 向量转字符串
     * @returns {string} 字符串表示
     */
    toString() {
        return `Vector2D(${this.x.toFixed(2)}, ${this.y.toFixed(2)})`;
    }

    /**
     * 从角度创建单位向量
     * @param {number} angle - 角度（弧度）
     * @returns {Vector2D} 单位向量
     */
    static fromAngle(angle) {
        return new Vector2D(Math.cos(angle), Math.sin(angle));
    }

    /**
     * 计算两点间距离
     * @param {Vector2D} a - 点A
     * @param {Vector2D} b - 点B
     * @returns {number} 距离
     */
    static distance(a, b) {
        return a.subtract(b).magnitude();
    }

    /**
     * 计算两点间距离的平方
     * @param {Vector2D} a - 点A
     * @param {Vector2D} b - 点B
     * @returns {number} 距离的平方
     */
    static distanceSquared(a, b) {
        return a.subtract(b).magnitudeSquared();
    }
}

/**
 * 数学工具函数集合
 */
class MathUtils {
    /**
     * 将角度转换为弧度
     * @param {number} degrees - 角度
     * @returns {number} 弧度
     */
    static degToRad(degrees) {
        return degrees * Math.PI / 180;
    }

    /**
     * 将弧度转换为角度
     * @param {number} radians - 弧度
     * @returns {number} 角度
     */
    static radToDeg(radians) {
        return radians * 180 / Math.PI;
    }

    /**
     * 限制数值在指定范围内
     * @param {number} value - 输入值
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number} 限制后的值
     */
    static clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    }

    /**
     * 线性插值
     * @param {number} start - 起始值
     * @param {number} end - 结束值
     * @param {number} t - 插值参数 (0-1)
     * @returns {number} 插值结果
     */
    static lerp(start, end, t) {
        return start + (end - start) * this.clamp(t, 0, 1);
    }

    /**
     * 平滑插值（使用三次函数）
     * @param {number} start - 起始值
     * @param {number} end - 结束值
     * @param {number} t - 插值参数 (0-1)
     * @returns {number} 插值结果
     */
    static smoothstep(start, end, t) {
        t = this.clamp(t, 0, 1);
        t = t * t * (3 - 2 * t);
        return this.lerp(start, end, t);
    }

    /**
     * 更平滑的插值（使用五次函数）
     * @param {number} start - 起始值
     * @param {number} end - 结束值
     * @param {number} t - 插值参数 (0-1)
     * @returns {number} 插值结果
     */
    static smootherstep(start, end, t) {
        t = this.clamp(t, 0, 1);
        t = t * t * t * (t * (t * 6 - 15) + 10);
        return this.lerp(start, end, t);
    }

    /**
     * 生成指定范围内的随机整数
     * @param {number} min - 最小值（包含）
     * @param {number} max - 最大值（包含）
     * @returns {number} 随机整数
     */
    static randomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    /**
     * 生成指定范围内的随机浮点数
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number} 随机浮点数
     */
    static randomFloat(min, max) {
        return Math.random() * (max - min) + min;
    }

    /**
     * 根据概率返回布尔值
     * @param {number} probability - 概率 (0-1)
     * @returns {boolean} 随机布尔值
     */
    static randomBool(probability = 0.5) {
        return Math.random() < probability;
    }

    /**
     * 从数组中随机选择一个元素
     * @param {Array} array - 输入数组
     * @returns {any} 随机选择的元素
     */
    static randomChoice(array) {
        if (array.length === 0) return null;
        return array[this.randomInt(0, array.length - 1)];
    }

    /**
     * 检测点是否在圆形内
     * @param {Vector2D} point - 点坐标
     * @param {Vector2D} center - 圆心坐标
     * @param {number} radius - 圆半径
     * @returns {boolean} 是否在圆内
     */
    static pointInCircle(point, center, radius) {
        return Vector2D.distanceSquared(point, center) <= radius * radius;
    }

    /**
     * 检测点是否在矩形内
     * @param {Vector2D} point - 点坐标
     * @param {Vector2D} rectPos - 矩形位置
     * @param {number} width - 矩形宽度
     * @param {number} height - 矩形高度
     * @returns {boolean} 是否在矩形内
     */
    static pointInRect(point, rectPos, width, height) {
        return point.x >= rectPos.x && 
               point.x <= rectPos.x + width &&
               point.y >= rectPos.y && 
               point.y <= rectPos.y + height;
    }

    /**
     * 检测两个圆形是否碰撞
     * @param {Vector2D} center1 - 圆1中心
     * @param {number} radius1 - 圆1半径
     * @param {Vector2D} center2 - 圆2中心
     * @param {number} radius2 - 圆2半径
     * @returns {boolean} 是否碰撞
     */
    static circleCollision(center1, radius1, center2, radius2) {
        const distance = Vector2D.distance(center1, center2);
        return distance <= radius1 + radius2;
    }

    /**
     * 计算时间相关的波动值
     * @param {number} time - 时间（毫秒）
     * @param {number} frequency - 频率
     * @param {number} amplitude - 振幅
     * @param {number} offset - 偏移
     * @returns {number} 波动值
     */
    static wave(time, frequency = 1, amplitude = 1, offset = 0) {
        return Math.sin(time * frequency * 0.001) * amplitude + offset;
    }

    /**
     * 缓动函数 - 缓入
     * @param {number} t - 时间参数 (0-1)
     * @returns {number} 缓动值
     */
    static easeIn(t) {
        return t * t;
    }

    /**
     * 缓动函数 - 缓出
     * @param {number} t - 时间参数 (0-1)
     * @returns {number} 缓动值
     */
    static easeOut(t) {
        return 1 - (1 - t) * (1 - t);
    }

    /**
     * 缓动函数 - 缓入缓出
     * @param {number} t - 时间参数 (0-1)
     * @returns {number} 缓动值
     */
    static easeInOut(t) {
        return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;
    }

    /**
     * 弹性缓动函数
     * @param {number} t - 时间参数 (0-1)
     * @returns {number} 缓动值
     */
    static easeElastic(t) {
        const c4 = (2 * Math.PI) / 3;
        return t === 0 ? 0 : t === 1 ? 1 : 
               -Math.pow(2, 10 * t - 10) * Math.sin((t * 10 - 10.75) * c4);
    }

    /**
     * 回弹缓动函数
     * @param {number} t - 时间参数 (0-1)
     * @returns {number} 缓动值
     */
    static easeBounce(t) {
        const n1 = 7.5625;
        const d1 = 2.75;

        if (t < 1 / d1) {
            return n1 * t * t;
        } else if (t < 2 / d1) {
            return n1 * (t -= 1.5 / d1) * t + 0.75;
        } else if (t < 2.5 / d1) {
            return n1 * (t -= 2.25 / d1) * t + 0.9375;
        } else {
            return n1 * (t -= 2.625 / d1) * t + 0.984375;
        }
    }

    /**
     * 将数值映射到新的范围
     * @param {number} value - 输入值
     * @param {number} inMin - 输入最小值
     * @param {number} inMax - 输入最大值
     * @param {number} outMin - 输出最小值
     * @param {number} outMax - 输出最大值
     * @returns {number} 映射后的值
     */
    static map(value, inMin, inMax, outMin, outMax) {
        return (value - inMin) * (outMax - outMin) / (inMax - inMin) + outMin;
    }

    /**
     * 计算两个角度之间的最短角度差
     * @param {number} angle1 - 角度1（弧度）
     * @param {number} angle2 - 角度2（弧度）
     * @returns {number} 角度差（弧度）
     */
    static angleDifference(angle1, angle2) {
        let diff = angle2 - angle1;
        while (diff > Math.PI) diff -= 2 * Math.PI;
        while (diff < -Math.PI) diff += 2 * Math.PI;
        return diff;
    }

    /**
     * 角度插值（处理角度环绕）
     * @param {number} start - 起始角度（弧度）
     * @param {number} end - 结束角度（弧度）
     * @param {number} t - 插值参数 (0-1)
     * @returns {number} 插值结果（弧度）
     */
    static lerpAngle(start, end, t) {
        const diff = this.angleDifference(start, end);
        return start + diff * t;
    }
}

// 导出类和工具函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { Vector2D, MathUtils };
} else {
    window.Vector2D = Vector2D;
    window.MathUtils = MathUtils;
}
