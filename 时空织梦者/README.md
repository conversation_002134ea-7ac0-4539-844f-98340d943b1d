# 时空织梦者 (Temporal Dream Weaver)

> 捕捉决定性瞬间，引燃无限可能

## 🎮 游戏简介

时空织梦者是一款创新的策略解谜游戏，玩家通过操控时间流动来编织梦境，解决复杂的时空谜题。与传统的反应速度游戏不同，本游戏注重策略思考和时间管理，为玩家带来全新的游戏体验。

### 🌟 核心玩法

- **时间操控**：暂停、倒退、加速时间，掌控游戏节奏
- **梦境编织**：通过连接不同时间点的元素创造解决方案
- **因果链条**：每个动作都会影响未来，需要深思熟虑
- **多维解谜**：在多个时间线中同时思考和操作

## 🚀 功能特性

### ✨ 核心功能
- ⏰ **时间操控系统**：完整的时间暂停、倒流、加速机制
- 🧩 **策略解谜**：需要规划和思考的谜题设计
- 🌀 **梦境编织**：独特的元素连接和路径创建系统
- 💫 **视觉效果**：精美的时空主题视觉设计

### 🎨 界面设计
- 📱 **响应式设计**：完美适配PC端和移动端
- 🌍 **多语言支持**：中文/英文双语切换
- 🎨 **现代UI**：深蓝紫色主题的梦境风格设计
- 🌙 **沉浸体验**：时空元素和粒子效果

### 👤 玩家系统
- 👥 **多玩家支持**：创建和切换不同玩家账号
- 📊 **进度保存**：自动保存游戏进度和设置
- 🏆 **成就系统**：追踪玩家游戏成就
- 📈 **统计数据**：详细的游戏数据记录

### 🛠️ 技术特性
- 🗄️ **智能存储**：支持IndexedDB/localStorage/内存存储
- ⚡ **高性能**：60FPS流畅游戏体验
- 🎮 **多输入支持**：鼠标、键盘、触摸操作
- 🔧 **错误处理**：完善的错误捕获和恢复机制

## 🎯 操作说明

### 🖱️ PC端操作
- **鼠标左键**：选择和连接游戏元素
- **鼠标拖拽**：创建时空连接线
- **空格键**：暂停/恢复时间
- **R键**：开始时间倒流
- **F键**：时间快进
- **ESC键**：暂停游戏或返回菜单

### 📱 移动端操作
- **触摸选择**：点击选择游戏元素
- **拖拽连接**：拖拽创建元素间的连接
- **双指缩放**：调整视图大小
- **长按**：显示元素属性菜单

### ⏰ 时间控制
- **暂停时间**：冻结所有移动元素，思考策略
- **时间倒流**：撤销错误操作，重新规划
- **时间加速**：快速查看结果，验证策略

## 📁 项目结构

```
时空织梦者/
├── index.html              # 主页面
├── styles/                 # 样式文件
│   ├── main.css           # 主样式
│   ├── game.css           # 游戏界面样式
│   ├── editor.css         # 关卡编辑器样式
│   └── responsive.css     # 响应式样式
├── js/                    # JavaScript文件
│   ├── utils/             # 工具模块
│   │   ├── storage.js     # KV存储服务
│   │   ├── i18n.js        # 国际化服务
│   │   └── math-utils.js  # 数学工具函数
│   ├── core/              # 核心模块
│   │   ├── time-engine.js # 时间操控引擎
│   │   ├── dream-weaver.js # 梦境编织系统
│   │   ├── level-manager.js # 关卡管理器
│   │   ├── physics.js     # 物理引擎
│   │   └── renderer.js    # 渲染引擎
│   ├── game/              # 游戏逻辑
│   │   ├── game-state.js  # 游戏状态管理
│   │   ├── player-manager.js # 玩家管理
│   │   ├── level-data.js  # 关卡数据
│   │   └── achievements.js # 成就系统
│   ├── ui/                # 界面模块
│   │   ├── screen-manager.js # 屏幕管理
│   │   ├── input-handler.js  # 输入处理
│   │   ├── ui-components.js  # UI组件
│   │   └── level-editor.js   # 关卡编辑器
│   └── main.js            # 主程序入口
├── assets/                # 资源文件
│   ├── images/            # 图片资源
│   ├── sounds/            # 音效文件
│   └── levels/            # 内置关卡数据
├── docs/                  # 文档
│   ├── API.md            # API文档
│   ├── GAMEPLAY.md       # 玩法说明
│   └── LEVEL_FORMAT.md   # 关卡格式说明
└── README.md             # 项目说明
```

## 🔧 技术架构

### 🏗️ 模块化设计
- **时间引擎**：核心时间操控系统，支持暂停、倒流、加速
- **梦境编织器**：元素连接和路径创建系统
- **存储服务**：统一的KV存储接口，支持多种后端
- **国际化服务**：完整的多语言支持系统
- **渲染引擎**：高性能的Canvas 2D渲染系统

### 💾 存储系统
```javascript
// 自动选择最佳存储方案
IndexedDB > localStorage > Memory Storage

// 统一接口
await storageService.put(key, value);
const data = await storageService.get(key);
await storageService.delete(key);
const keys = await storageService.list(prefix);
```

### 🌍 国际化系统
```javascript
// 多语言支持
i18nService.t('game.title');        // 时空织梦者
i18nService.t('game.score');        // 得分
await i18nService.setLanguage('en-US');
```

### ⏰ 时间操控系统
```javascript
// 时间控制
timeEngine.pauseTime();              // 暂停时间
timeEngine.startRewind();            // 开始倒流
timeEngine.startFastForward();       // 开始快进
timeEngine.jumpToTime(timeIndex);    // 跳转到指定时间点
```

## 🚀 快速开始

### 📋 环境要求
- 现代浏览器（Chrome 60+, Firefox 55+, Safari 12+, Edge 79+）
- 支持ES6+语法和Canvas 2D API
- 建议启用JavaScript和本地存储

### 🔧 部署方式

#### 1. 本地运行
```bash
# 直接打开index.html文件
# 或使用本地服务器
python -m http.server 8000
# 访问 http://localhost:8000
```

#### 2. 静态部署
- **GitHub Pages**：上传到GitHub仓库，启用Pages
- **Vercel**：连接GitHub仓库，自动部署
- **Netlify**：拖拽文件夹到Netlify
- **其他静态托管**：上传所有文件到服务器

## 🎨 游戏元素

### 🌟 基础元素
- **💫 光球**：需要引导到目标位置的发光球体
- **🚪 时间门**：可以传送光球的时空通道
- **🪞 镜面**：反射光球路径的镜面元素
- **🎯 目标点**：光球需要到达的终点位置

### ⏰ 时间机制
- **时间暂停**：冻结所有元素，规划策略
- **时间倒流**：回到之前的状态，修正错误
- **时间加速**：快速验证解决方案
- **时间片段**：创建特定时间范围的操作

## 🛠️ 开发计划

### 🚧 当前版本功能
- ✅ 基础时间操控系统
- ✅ 核心UI界面和响应式设计
- ✅ 多语言支持系统
- ✅ 存储服务和数据管理
- 🔄 游戏核心逻辑开发中

### 💡 计划功能
- 🎮 **完整游戏逻辑**：梦境编织和解谜机制
- 🏗️ **关卡编辑器**：可视化关卡创建工具
- 🌐 **关卡分享**：自定义关卡分享和评分系统
- 🏆 **排行榜**：全球和好友排行榜
- 🎵 **音效系统**：背景音乐和音效支持
- 🎨 **主题系统**：多种视觉主题选择

## 🐛 故障排除

### 常见问题

1. **游戏无法加载**
   - 检查浏览器控制台错误信息
   - 确认浏览器支持ES6语法
   - 尝试清除浏览器缓存

2. **数据无法保存**
   - 检查浏览器是否禁用了本地存储
   - 尝试在隐私模式下运行
   - 查看存储空间是否已满

3. **触摸操作无响应**
   - 确认设备支持触摸事件
   - 检查是否有其他元素遮挡
   - 尝试刷新页面

### 🔍 调试模式
```javascript
// 在浏览器控制台中启用调试
localStorage.setItem('debug', 'true');
location.reload();
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交问题和功能请求！如果您想贡献代码，请：

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

---

**探索时空奥秘，编织无限梦境！** ✨⏰🌟
