<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时空织梦者 - 功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-section h2 {
            color: #4facfe;
            margin-top: 0;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            transition: transform 0.2s;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
        
        .test-result {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.success {
            background: #10b981;
            color: white;
        }
        
        .status.error {
            background: #ef4444;
            color: white;
        }
        
        .status.info {
            background: #3b82f6;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⧖ 时空织梦者</h1>
            <p>功能测试页面</p>
        </div>

        <!-- 存储服务测试 -->
        <div class="test-section">
            <h2>🗄️ 存储服务测试</h2>
            <button class="test-button" onclick="testStorage()">测试存储功能</button>
            <button class="test-button" onclick="testStorageStats()">获取存储统计</button>
            <button class="test-button" onclick="clearStorage()">清空存储</button>
            <div id="storage-result" class="test-result"></div>
        </div>

        <!-- 国际化服务测试 -->
        <div class="test-section">
            <h2>🌍 国际化服务测试</h2>
            <button class="test-button" onclick="testI18n()">测试翻译功能</button>
            <button class="test-button" onclick="switchLanguage()">切换语言</button>
            <button class="test-button" onclick="updateDOM()">更新DOM文本</button>
            <div id="i18n-result" class="test-result"></div>
            <div style="margin-top: 10px;">
                <span data-i18n="game.title">时空织梦者</span> | 
                <span data-i18n="game.tagline">捕捉决定性瞬间，引燃无限可能</span>
            </div>
        </div>

        <!-- 时间引擎测试 -->
        <div class="test-section">
            <h2>⏰ 时间引擎测试</h2>
            <button class="test-button" onclick="testTimeEngine()">初始化时间引擎</button>
            <button class="test-button" onclick="pauseTime()">暂停时间</button>
            <button class="test-button" onclick="resumeTime()">恢复时间</button>
            <button class="test-button" onclick="rewindTime()">时间倒流</button>
            <button class="test-button" onclick="fastForwardTime()">时间快进</button>
            <button class="test-button" onclick="getTimeInfo()">获取时间信息</button>
            <div id="time-result" class="test-result"></div>
        </div>

        <!-- 数学工具测试 -->
        <div class="test-section">
            <h2>🧮 数学工具测试</h2>
            <button class="test-button" onclick="testVector2D()">测试向量运算</button>
            <button class="test-button" onclick="testMathUtils()">测试数学函数</button>
            <button class="test-button" onclick="testCollision()">测试碰撞检测</button>
            <div id="math-result" class="test-result"></div>
        </div>

        <!-- 系统信息 -->
        <div class="test-section">
            <h2>ℹ️ 系统信息</h2>
            <button class="test-button" onclick="getSystemInfo()">获取系统信息</button>
            <div id="system-result" class="test-result"></div>
        </div>
    </div>

    <!-- 引入核心脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/utils/math-utils.js"></script>
    <script src="js/core/time-engine.js"></script>

    <script>
        // 全局变量
        let timeEngine = null;
        
        // 工具函数
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            const statusText = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            
            element.innerHTML += `[${timestamp}] <span class="status ${statusClass}">${statusText}</span> ${message}\n`;
            element.scrollTop = element.scrollHeight;
        }

        function clearLog(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        // 存储服务测试
        async function testStorage() {
            clearLog('storage-result');
            
            try {
                log('storage-result', '开始测试存储服务...', 'info');
                
                // 初始化存储服务
                await storageService.init();
                log('storage-result', '存储服务初始化成功', 'success');
                
                // 测试写入
                await storageService.put('test_key', { message: 'Hello World', timestamp: Date.now() });
                log('storage-result', '数据写入成功', 'success');
                
                // 测试读取
                const data = await storageService.get('test_key');
                log('storage-result', `数据读取成功: ${JSON.stringify(data)}`, 'success');
                
                // 测试列表
                const keys = await storageService.list('test_');
                log('storage-result', `找到 ${keys.length} 个匹配的键`, 'success');
                
            } catch (error) {
                log('storage-result', `存储测试失败: ${error.message}`, 'error');
            }
        }

        async function testStorageStats() {
            try {
                const stats = await storageService.getStats();
                log('storage-result', `存储统计: ${JSON.stringify(stats, null, 2)}`, 'info');
            } catch (error) {
                log('storage-result', `获取统计失败: ${error.message}`, 'error');
            }
        }

        async function clearStorage() {
            try {
                await storageService.clear();
                log('storage-result', '存储已清空', 'success');
            } catch (error) {
                log('storage-result', `清空失败: ${error.message}`, 'error');
            }
        }

        // 国际化服务测试
        async function testI18n() {
            clearLog('i18n-result');
            
            try {
                log('i18n-result', '开始测试国际化服务...', 'info');
                
                await i18nService.init();
                log('i18n-result', '国际化服务初始化成功', 'success');
                
                const title = i18nService.t('game.title');
                const tagline = i18nService.t('game.tagline');
                
                log('i18n-result', `游戏标题: ${title}`, 'success');
                log('i18n-result', `游戏标语: ${tagline}`, 'success');
                
                const currentLang = i18nService.getCurrentLanguage();
                log('i18n-result', `当前语言: ${currentLang}`, 'info');
                
            } catch (error) {
                log('i18n-result', `国际化测试失败: ${error.message}`, 'error');
            }
        }

        async function switchLanguage() {
            try {
                await i18nService.toggleLanguage();
                const newLang = i18nService.getCurrentLanguage();
                log('i18n-result', `语言已切换为: ${newLang}`, 'success');
            } catch (error) {
                log('i18n-result', `语言切换失败: ${error.message}`, 'error');
            }
        }

        function updateDOM() {
            i18nService.updateDOM();
            log('i18n-result', 'DOM文本已更新', 'success');
        }

        // 时间引擎测试
        function testTimeEngine() {
            clearLog('time-result');
            
            try {
                log('time-result', '初始化时间引擎...', 'info');
                
                timeEngine = new TimeEngine();
                timeEngine.init();
                
                // 添加事件监听器
                timeEngine.addEventListener('stateChanged', (data) => {
                    log('time-result', `时间状态变更: ${data.oldState} → ${data.newState}`, 'info');
                });
                
                timeEngine.addEventListener('timeUpdate', (data) => {
                    // 每秒更新一次时间信息
                    if (Math.floor(data.gameTime / 1000) !== Math.floor((data.gameTime - data.deltaTime) / 1000)) {
                        log('time-result', `游戏时间: ${Math.floor(data.gameTime / 1000)}s`, 'info');
                    }
                });
                
                log('time-result', '时间引擎初始化成功', 'success');
                
                // 开始更新循环
                function updateLoop() {
                    if (timeEngine) {
                        timeEngine.update(performance.now());
                        requestAnimationFrame(updateLoop);
                    }
                }
                updateLoop();
                
            } catch (error) {
                log('time-result', `时间引擎初始化失败: ${error.message}`, 'error');
            }
        }

        function pauseTime() {
            if (timeEngine) {
                timeEngine.pauseTime();
                log('time-result', '时间已暂停', 'success');
            } else {
                log('time-result', '请先初始化时间引擎', 'error');
            }
        }

        function resumeTime() {
            if (timeEngine) {
                timeEngine.resumeTime();
                log('time-result', '时间已恢复', 'success');
            } else {
                log('time-result', '请先初始化时间引擎', 'error');
            }
        }

        function rewindTime() {
            if (timeEngine) {
                timeEngine.startRewind();
                log('time-result', '开始时间倒流', 'success');
            } else {
                log('time-result', '请先初始化时间引擎', 'error');
            }
        }

        function fastForwardTime() {
            if (timeEngine) {
                timeEngine.startFastForward();
                log('time-result', '开始时间快进', 'success');
            } else {
                log('time-result', '请先初始化时间引擎', 'error');
            }
        }

        function getTimeInfo() {
            if (timeEngine) {
                const info = timeEngine.getTimeInfo();
                log('time-result', `时间信息: ${JSON.stringify(info, null, 2)}`, 'info');
            } else {
                log('time-result', '请先初始化时间引擎', 'error');
            }
        }

        // 数学工具测试
        function testVector2D() {
            clearLog('math-result');
            
            try {
                log('math-result', '测试Vector2D类...', 'info');
                
                const v1 = new Vector2D(3, 4);
                const v2 = new Vector2D(1, 2);
                
                log('math-result', `向量1: ${v1.toString()}`, 'info');
                log('math-result', `向量2: ${v2.toString()}`, 'info');
                
                const sum = v1.add(v2);
                log('math-result', `向量相加: ${sum.toString()}`, 'success');
                
                const magnitude = v1.magnitude();
                log('math-result', `向量1长度: ${magnitude.toFixed(2)}`, 'success');
                
                const distance = Vector2D.distance(v1, v2);
                log('math-result', `两向量距离: ${distance.toFixed(2)}`, 'success');
                
            } catch (error) {
                log('math-result', `Vector2D测试失败: ${error.message}`, 'error');
            }
        }

        function testMathUtils() {
            try {
                log('math-result', '测试MathUtils函数...', 'info');
                
                const lerp = MathUtils.lerp(0, 100, 0.5);
                log('math-result', `线性插值(0,100,0.5): ${lerp}`, 'success');
                
                const clamp = MathUtils.clamp(150, 0, 100);
                log('math-result', `数值限制(150,0,100): ${clamp}`, 'success');
                
                const randomInt = MathUtils.randomInt(1, 10);
                log('math-result', `随机整数(1-10): ${randomInt}`, 'success');
                
                const radians = MathUtils.degToRad(90);
                log('math-result', `90度转弧度: ${radians.toFixed(4)}`, 'success');
                
            } catch (error) {
                log('math-result', `MathUtils测试失败: ${error.message}`, 'error');
            }
        }

        function testCollision() {
            try {
                log('math-result', '测试碰撞检测...', 'info');
                
                const center1 = new Vector2D(0, 0);
                const center2 = new Vector2D(3, 4);
                const collision = MathUtils.circleCollision(center1, 3, center2, 2);
                
                log('math-result', `圆形碰撞检测: ${collision ? '碰撞' : '未碰撞'}`, 'success');
                
                const point = new Vector2D(1, 1);
                const rectPos = new Vector2D(0, 0);
                const inRect = MathUtils.pointInRect(point, rectPos, 5, 5);
                
                log('math-result', `点在矩形内: ${inRect ? '是' : '否'}`, 'success');
                
            } catch (error) {
                log('math-result', `碰撞检测测试失败: ${error.message}`, 'error');
            }
        }

        // 系统信息
        function getSystemInfo() {
            clearLog('system-result');
            
            const info = {
                userAgent: navigator.userAgent,
                language: navigator.language,
                platform: navigator.platform,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine,
                screenResolution: `${screen.width}x${screen.height}`,
                windowSize: `${window.innerWidth}x${window.innerHeight}`,
                pixelRatio: window.devicePixelRatio,
                localStorage: typeof Storage !== 'undefined',
                indexedDB: typeof indexedDB !== 'undefined',
                webGL: !!window.WebGLRenderingContext,
                canvas: !!window.CanvasRenderingContext2D
            };
            
            log('system-result', JSON.stringify(info, null, 2), 'info');
        }

        // 页面加载完成后自动运行一些测试
        document.addEventListener('DOMContentLoaded', () => {
            log('system-result', '页面加载完成，开始自动测试...', 'info');
            getSystemInfo();
        });
    </script>
</body>
</html>
