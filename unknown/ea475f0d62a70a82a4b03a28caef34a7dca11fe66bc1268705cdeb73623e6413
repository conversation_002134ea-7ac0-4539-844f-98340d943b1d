# Level 类空指针错误修复说明

## 问题描述

在量子共鸣者游戏中，进入关卡时出现 JavaScript 错误：
```
❌ 全局错误: TypeError: Cannot read properties of null (reading 'update')
    at GameController.update (game-controller.js:433:27)
```

经过深入分析，发现问题不仅仅在于 `InputManager`，还存在于 `Level` 类中多处直接访问全局引擎对象而没有进行空值检查。

## 根本原因

`Level` 类在多个方法中直接访问 `physicsEngine`、`quantumEngine` 等全局对象，但这些对象可能在某些情况下未正确初始化或为 `null`，导致空指针异常。

## 修复内容

### 1. load() 方法修复
**问题**: 直接调用 `physicsEngine.clear()` 和 `quantumEngine.reset()`
**修复**: 添加存在性检查
```javascript
// 修复前
physicsEngine.clear();
quantumEngine.reset();

// 修复后
if (window.physicsEngine && typeof physicsEngine.clear === 'function') {
    physicsEngine.clear();
}
if (window.quantumEngine && typeof quantumEngine.reset === 'function') {
    quantumEngine.reset();
}
```

### 2. createParticles() 方法修复
**问题**: 直接调用 `physicsEngine.createParticle()` 和 `quantumEngine.addParticle()`
**修复**: 添加引擎可用性检查和错误处理
```javascript
// 检查物理引擎是否可用
if (!window.physicsEngine || typeof physicsEngine.createParticle !== 'function') {
    console.warn('⚠️ 物理引擎不可用，跳过粒子创建');
    return;
}

// 添加 try-catch 错误处理
try {
    const particle = physicsEngine.createParticle({...});
    // 检查量子引擎是否可用
    if (window.quantumEngine && typeof quantumEngine.addParticle === 'function') {
        quantumEngine.addParticle(particle);
    }
} catch (error) {
    console.error(`❌ 创建粒子 ${index} 失败:`, error);
}
```

### 3. createForceFields() 方法修复
**问题**: 直接调用 `physicsEngine.createForceField()`
**修复**: 添加引擎检查和错误处理

### 4. applyRules() 方法修复
**问题**: 直接访问引擎属性
**修复**: 为每个引擎访问添加存在性检查
```javascript
// 修复前
if (this.rules.gravity) {
    physicsEngine.gravity = this.rules.gravity;
}

// 修复后
if (this.rules.gravity && window.physicsEngine) {
    physicsEngine.gravity = this.rules.gravity;
}
```

### 5. updateLevelUI() 方法修复
**问题**: 直接访问 `quantumEngine.score`
**修复**: 添加量子引擎检查
```javascript
// 修复前
const progress = Math.min(100, (quantumEngine.score / this.targetScore) * 100);

// 修复后
let progress = 0;
if (window.quantumEngine && typeof quantumEngine.score !== 'undefined') {
    progress = Math.min(100, (quantumEngine.score / this.targetScore) * 100);
}
```

### 6. checkCompletionConditions() 方法修复
**问题**: 直接访问引擎对象和属性
**修复**: 为所有引擎访问添加检查

### 7. checkObjective() 方法修复
**问题**: 在目标检查中直接访问引擎
**修复**: 为每种目标类型添加引擎存在性检查

### 8. complete() 方法修复
**问题**: 直接修改 `quantumEngine.score`
**修复**: 添加量子引擎检查

### 9. calculateBonusScore() 方法修复
**问题**: 直接访问 `quantumEngine.combo`
**修复**: 添加存在性检查

### 10. isPerfectCompletion() 方法修复
**问题**: 直接访问 `quantumEngine.score`
**修复**: 添加量子引擎检查

### 11. saveRecord() 方法修复
**问题**: 记录中直接使用引擎数据
**修复**: 使用条件表达式提供默认值

### 12. getStatus() 方法修复
**问题**: 状态对象中直接访问引擎
**修复**: 为所有引擎访问添加检查和默认值

## 防御性编程模式

所有修复都遵循以下防御性编程模式：

1. **存在性检查**: 使用 `window.engineName` 检查全局对象是否存在
2. **类型检查**: 使用 `typeof` 检查方法是否存在
3. **错误处理**: 使用 `try-catch` 包装可能失败的操作
4. **默认值**: 为可能为空的值提供合理的默认值
5. **日志记录**: 添加警告和错误日志以便调试

## 测试验证

创建了 `debug-null-pointer.html` 调试页面来验证修复效果：
- 检查全局对象状态
- 模拟游戏循环测试
- 实时错误监控
- 详细的调试信息输出

## 预期效果

修复后，即使某些引擎对象未正确初始化，`Level` 类也能够：
1. 优雅地处理缺失的引擎对象
2. 继续执行其他可用的功能
3. 提供有用的调试信息
4. 避免抛出空指针异常

这种防御性编程方法确保了游戏的稳定性和可靠性。
