#!/usr/bin/env python3
"""
创建占位符图标文件
生成基础的PNG图标文件以避免404错误
"""

import os
from PIL import Image, ImageDraw, ImageFont
import math

def create_icon(size, filename, text="S", bg_color=(15, 15, 35), text_color=(255, 255, 255)):
    """创建一个简单的图标"""
    # 创建图像
    img = Image.new('RGBA', (size, size), bg_color + (255,))
    draw = ImageDraw.Draw(img)
    
    # 绘制圆形背景
    margin = size // 10
    draw.ellipse([margin, margin, size - margin, size - margin], 
                fill=(74, 144, 226, 255), outline=(255, 255, 255, 100), width=2)
    
    # 绘制文字
    try:
        # 尝试使用系统字体
        font_size = size // 3
        font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", font_size)
    except:
        # 如果没有找到字体，使用默认字体
        font = ImageFont.load_default()
    
    # 计算文字位置
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size - text_width) // 2
    y = (size - text_height) // 2
    
    # 绘制文字阴影
    draw.text((x + 2, y + 2), text, font=font, fill=(0, 0, 0, 128))
    # 绘制文字
    draw.text((x, y), text, font=font, fill=text_color)
    
    return img

def create_game_icon(size, filename, theme):
    """创建游戏特定图标"""
    themes = {
        'temporal': {
            'bg': (106, 76, 147),
            'accent': (192, 108, 132),
            'text': '时',
            'symbol': 'clock'
        },
        'spark': {
            'bg': (255, 107, 107),
            'accent': (254, 202, 87),
            'text': '瞬',
            'symbol': 'lightning'
        },
        'quantum': {
            'bg': (72, 52, 212),
            'accent': (104, 109, 224),
            'text': '量',
            'symbol': 'atom'
        }
    }
    
    theme_config = themes.get(theme, themes['spark'])
    
    # 创建图像
    img = Image.new('RGBA', (size, size), theme_config['bg'] + (255,))
    draw = ImageDraw.Draw(img)
    
    # 绘制背景圆形
    margin = size // 10
    draw.ellipse([margin, margin, size - margin, size - margin], 
                fill=theme_config['accent'] + (255,), outline=(255, 255, 255, 150), width=2)
    
    # 绘制符号
    center = size // 2
    if theme_config['symbol'] == 'clock':
        # 时钟符号
        radius = size // 4
        draw.ellipse([center - radius, center - radius, center + radius, center + radius], 
                    outline=(255, 255, 255, 255), width=3)
        # 时针
        draw.line([center, center, center, center - radius // 2], fill=(255, 255, 255, 255), width=3)
        # 分针
        draw.line([center, center, center + radius // 1.5, center], fill=(255, 255, 255, 255), width=2)
        
    elif theme_config['symbol'] == 'lightning':
        # 闪电符号
        points = [
            (center - size//8, center - size//6),
            (center + size//12, center - size//6),
            (center - size//12, center),
            (center + size//8, center),
            (center - size//12, center + size//6),
            (center + size//12, center),
            (center - size//8, center)
        ]
        draw.polygon(points, fill=(255, 255, 255, 255))
        
    elif theme_config['symbol'] == 'atom':
        # 原子符号
        radius = size // 5
        # 中心圆
        draw.ellipse([center - 4, center - 4, center + 4, center + 4], fill=(255, 255, 255, 255))
        # 轨道1
        draw.ellipse([center - radius, center - radius//3, center + radius, center + radius//3], 
                    outline=(255, 255, 255, 255), width=2)
        # 轨道2 (旋转45度的椭圆，简化为另一个椭圆)
        draw.ellipse([center - radius//3, center - radius, center + radius//3, center + radius], 
                    outline=(255, 255, 255, 255), width=2)
    
    return img

def main():
    """主函数"""
    # 确保目录存在
    os.makedirs('assets/images', exist_ok=True)
    
    # 主应用图标尺寸
    icon_sizes = [
        (16, 'favicon-16x16'),
        (32, 'favicon-32x32'),
        (72, 'icon-72x72'),
        (96, 'icon-96x96'),
        (128, 'icon-128x128'),
        (144, 'icon-144x144'),
        (152, 'icon-152x152'),
        (192, 'icon-192x192'),
        (384, 'icon-384x384'),
        (512, 'icon-512x512')
    ]
    
    # 创建主应用图标
    print("创建主应用图标...")
    for size, name in icon_sizes:
        img = create_icon(size, name)
        filepath = f'assets/images/{name}.png'
        img.save(filepath, 'PNG')
        print(f"  ✓ 创建 {filepath}")
    
    # 创建游戏特定图标
    print("\n创建游戏特定图标...")
    game_icons = [
        (96, 'temporal-icon-96x96', 'temporal'),
        (96, 'spark-icon-96x96', 'spark'),
        (96, 'quantum-icon-96x96', 'quantum')
    ]
    
    for size, name, theme in game_icons:
        img = create_game_icon(size, name, theme)
        filepath = f'assets/images/{name}.png'
        img.save(filepath, 'PNG')
        print(f"  ✓ 创建 {filepath}")
    
    # 创建截图占位符
    print("\n创建截图占位符...")
    
    # 宽屏截图
    wide_img = Image.new('RGBA', (1280, 720), (15, 15, 35, 255))
    draw = ImageDraw.Draw(wide_img)
    draw.rectangle([0, 0, 1280, 720], fill=(15, 15, 35, 255))
    draw.text((640, 360), "Split-Second Spark\n游戏选择界面", 
             fill=(255, 255, 255, 255), anchor="mm")
    wide_img.save('assets/images/screenshot-wide.png', 'PNG')
    print("  ✓ 创建 assets/images/screenshot-wide.png")
    
    # 窄屏截图
    narrow_img = Image.new('RGBA', (720, 1280), (15, 15, 35, 255))
    draw = ImageDraw.Draw(narrow_img)
    draw.rectangle([0, 0, 720, 1280], fill=(15, 15, 35, 255))
    draw.text((360, 640), "Split-Second Spark\n移动端界面", 
             fill=(255, 255, 255, 255), anchor="mm")
    narrow_img.save('assets/images/screenshot-narrow.png', 'PNG')
    print("  ✓ 创建 assets/images/screenshot-narrow.png")
    
    print(f"\n🎉 所有图标创建完成！共创建了 {len(icon_sizes) + len(game_icons) + 2} 个文件。")

if __name__ == '__main__':
    main()
