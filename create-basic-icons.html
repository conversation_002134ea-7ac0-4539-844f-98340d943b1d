<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基础图标创建器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #0f0f23;
            color: #ffffff;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .status {
            background: #1a1a2e;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .success {
            color: #4CAF50;
        }
        
        .error {
            color: #f44336;
        }
        
        .generate-btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 15px 30px;
            cursor: pointer;
            border-radius: 5px;
            font-size: 16px;
            margin: 20px 0;
        }
        
        .generate-btn:hover {
            background: #1976D2;
        }
        
        .generate-btn:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .progress {
            background: #333;
            height: 20px;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-bar {
            background: #4CAF50;
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>基础图标创建器</h1>
        <p>这个工具将创建应用所需的基础图标文件，解决404错误问题。</p>
        
        <button id="generateBtn" class="generate-btn" onclick="generateAllIcons()">
            开始创建图标
        </button>
        
        <div class="progress" style="display: none;">
            <div id="progressBar" class="progress-bar"></div>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div id="instructions" style="display: none;">
            <h3>使用说明：</h3>
            <ol>
                <li>点击上面的按钮开始创建图标</li>
                <li>图标将自动下载到您的下载文件夹</li>
                <li>请将下载的图标文件手动放置到项目的 <code>assets/images/</code> 目录中</li>
                <li>重新启动服务器以应用更改</li>
            </ol>
        </div>
    </div>

    <script>
        // 需要创建的图标配置
        const iconConfigs = [
            { size: 16, name: 'favicon-16x16', type: 'main' },
            { size: 32, name: 'favicon-32x32', type: 'main' },
            { size: 72, name: 'icon-72x72', type: 'main' },
            { size: 96, name: 'icon-96x96', type: 'main' },
            { size: 128, name: 'icon-128x128', type: 'main' },
            { size: 144, name: 'icon-144x144', type: 'main' },
            { size: 152, name: 'icon-152x152', type: 'main' },
            { size: 192, name: 'icon-192x192', type: 'main' },
            { size: 384, name: 'icon-384x384', type: 'main' },
            { size: 512, name: 'icon-512x512', type: 'main' },
            { size: 96, name: 'temporal-icon-96x96', type: 'temporal' },
            { size: 96, name: 'spark-icon-96x96', type: 'spark' },
            { size: 96, name: 'quantum-icon-96x96', type: 'quantum' }
        ];

        /**
         * 创建主应用图标
         */
        function createMainIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');

            // 背景渐变
            const gradient = ctx.createRadialGradient(size/2, size/2, 0, size/2, size/2, size/2);
            gradient.addColorStop(0, '#4a90e2');
            gradient.addColorStop(0.7, '#2c5aa0');
            gradient.addColorStop(1, '#0f0f23');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);

            // 绘制边框
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = Math.max(1, size / 64);
            ctx.strokeRect(0, 0, size, size);

            // 绘制闪电图标
            ctx.fillStyle = '#ffffff';
            ctx.shadowColor = '#ffffff';
            ctx.shadowBlur = size / 20;
            
            const centerX = size / 2;
            const centerY = size / 2;
            const scale = size / 100;
            
            ctx.beginPath();
            ctx.moveTo(centerX - 12 * scale, centerY - 20 * scale);
            ctx.lineTo(centerX + 8 * scale, centerY - 20 * scale);
            ctx.lineTo(centerX - 4 * scale, centerY - 2 * scale);
            ctx.lineTo(centerX + 12 * scale, centerY - 2 * scale);
            ctx.lineTo(centerX - 8 * scale, centerY + 20 * scale);
            ctx.lineTo(centerX + 4 * scale, centerY + 2 * scale);
            ctx.lineTo(centerX - 12 * scale, centerY + 2 * scale);
            ctx.closePath();
            ctx.fill();

            return canvas;
        }

        /**
         * 创建游戏特定图标
         */
        function createGameIcon(size, type) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');

            const centerX = size / 2;
            const centerY = size / 2;
            const scale = size / 100;

            // 主题配置
            const themes = {
                temporal: { bg: '#6a4c93', accent: '#c06c84', text: '时' },
                spark: { bg: '#ff6b6b', accent: '#feca57', text: '瞬' },
                quantum: { bg: '#4834d4', accent: '#686de0', text: '量' }
            };

            const theme = themes[type] || themes.spark;

            // 背景渐变
            const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, size/2);
            gradient.addColorStop(0, theme.accent);
            gradient.addColorStop(1, theme.bg);
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);

            // 边框
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = Math.max(1, size / 48);
            ctx.strokeRect(0, 0, size, size);

            // 绘制文字
            ctx.fillStyle = '#ffffff';
            ctx.font = `bold ${size * 0.4}px Arial, sans-serif`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.shadowColor = '#000000';
            ctx.shadowBlur = size / 20;
            ctx.fillText(theme.text, centerX, centerY);

            return canvas;
        }

        /**
         * 下载canvas为PNG文件
         */
        function downloadCanvas(canvas, filename) {
            return new Promise((resolve) => {
                canvas.toBlob((blob) => {
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = filename + '.png';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);
                    resolve();
                }, 'image/png');
            });
        }

        /**
         * 更新进度条
         */
        function updateProgress(current, total) {
            const progressBar = document.getElementById('progressBar');
            const percentage = (current / total) * 100;
            progressBar.style.width = percentage + '%';
        }

        /**
         * 更新状态信息
         */
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.style.display = 'block';
            status.className = `status ${type}`;
            status.textContent = message;
        }

        /**
         * 生成所有图标
         */
        async function generateAllIcons() {
            const generateBtn = document.getElementById('generateBtn');
            const progress = document.querySelector('.progress');
            const instructions = document.getElementById('instructions');
            
            generateBtn.disabled = true;
            generateBtn.textContent = '正在创建图标...';
            progress.style.display = 'block';
            
            try {
                updateStatus('开始创建图标文件...', 'info');
                
                for (let i = 0; i < iconConfigs.length; i++) {
                    const config = iconConfigs[i];
                    updateStatus(`正在创建 ${config.name}.png (${i + 1}/${iconConfigs.length})`, 'info');
                    
                    let canvas;
                    if (config.type === 'main') {
                        canvas = createMainIcon(config.size);
                    } else {
                        canvas = createGameIcon(config.size, config.type);
                    }
                    
                    await downloadCanvas(canvas, config.name);
                    updateProgress(i + 1, iconConfigs.length);
                    
                    // 添加小延迟以避免浏览器阻塞下载
                    await new Promise(resolve => setTimeout(resolve, 200));
                }
                
                updateStatus(`✅ 成功创建了 ${iconConfigs.length} 个图标文件！`, 'success');
                instructions.style.display = 'block';
                
            } catch (error) {
                updateStatus(`❌ 创建图标时出错: ${error.message}`, 'error');
                console.error('图标创建错误:', error);
            } finally {
                generateBtn.disabled = false;
                generateBtn.textContent = '重新创建图标';
            }
        }

        // 页面加载完成后显示说明
        window.addEventListener('load', () => {
            updateStatus('准备就绪，点击按钮开始创建图标', 'info');
        });
    </script>
</body>
</html>
