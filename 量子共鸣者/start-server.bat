@echo off
chcp 65001 >nul
title 量子共鸣者游戏服务器

echo 🎮 量子共鸣者游戏服务器启动脚本
echo ==================================

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到 Python，请先安装 Python
    pause
    exit /b 1
)

REM 进入脚本所在目录
cd /d "%~dp0"

echo 📁 当前目录: %CD%

REM 检查必要文件
if not exist "index.html" (
    echo ❌ 错误: 找不到 index.html 文件
    pause
    exit /b 1
)

if not exist "manifest.json" (
    echo ❌ 错误: 找不到 manifest.json 文件
    pause
    exit /b 1
)

echo ✅ 文件检查完成

REM 启动服务器
echo 🚀 启动服务器...
echo 📝 提示: 按 Ctrl+C 停止服务器
echo.

python server.py --port 8080

pause
