<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关卡编辑器功能测试 - 量子共鸣者</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #ffffff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            color: #00d4ff;
            margin-bottom: 10px;
        }
        
        .test-controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .test-btn {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
        }
        
        .test-btn.secondary {
            background: linear-gradient(45deg, #666, #888);
        }
        
        .test-results {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .test-result-item {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .test-result-item:last-child {
            border-bottom: none;
        }
        
        .test-status {
            font-weight: bold;
            font-size: 18px;
        }
        
        .test-status.pass {
            color: #00ff88;
        }
        
        .test-status.fail {
            color: #ff4444;
        }
        
        .test-details {
            font-size: 12px;
            color: #aaa;
            margin-left: 10px;
        }
        
        .summary {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid #00d4ff;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            text-align: center;
        }
        
        .editor-preview {
            margin-top: 30px;
            text-align: center;
        }
        
        .editor-preview iframe {
            width: 100%;
            height: 600px;
            border: 2px solid #00d4ff;
            border-radius: 10px;
            background: white;
        }
        
        .console-output {
            background: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.online {
            background: #00ff88;
            box-shadow: 0 0 10px #00ff88;
        }
        
        .status-indicator.offline {
            background: #ff4444;
            box-shadow: 0 0 10px #ff4444;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🛠️ 关卡编辑器功能测试</h1>
            <p>验证量子共鸣者游戏的关卡编辑器功能是否完整</p>
            <div>
                <span class="status-indicator" id="serverStatus"></span>
                <span id="serverStatusText">检查服务器状态...</span>
            </div>
        </div>
        
        <div class="test-controls">
            <button class="test-btn" onclick="runQuickTest()">🚀 快速测试</button>
            <button class="test-btn" onclick="runFullTest()">🔍 完整测试</button>
            <button class="test-btn" onclick="openEditor()">🛠️ 打开编辑器</button>
            <button class="test-btn secondary" onclick="clearResults()">🗑️ 清空结果</button>
            <button class="test-btn secondary" onclick="openMainGame()">🎮 返回游戏</button>
        </div>
        
        <div class="test-results" id="testResults">
            <div class="test-result-item">
                <span>等待测试开始...</span>
                <span class="test-status">⏳</span>
            </div>
        </div>
        
        <div class="summary" id="testSummary" style="display: none;">
            <h3>测试结果摘要</h3>
            <div id="summaryContent"></div>
        </div>
        
        <div class="console-output" id="consoleOutput">
            <div>关卡编辑器测试控制台</div>
            <div>等待测试开始...</div>
        </div>
    </div>

    <script>
        let testResults = [];
        let consoleMessages = [];
        
        // 检查服务器状态
        function checkServerStatus() {
            const indicator = document.getElementById('serverStatus');
            const text = document.getElementById('serverStatusText');
            
            fetch('/')
                .then(response => {
                    if (response.ok) {
                        indicator.className = 'status-indicator online';
                        text.textContent = '服务器在线';
                    } else {
                        indicator.className = 'status-indicator offline';
                        text.textContent = '服务器响应异常';
                    }
                })
                .catch(error => {
                    indicator.className = 'status-indicator offline';
                    text.textContent = '服务器离线';
                });
        }
        
        // 运行快速测试
        function runQuickTest() {
            addConsoleMessage('🚀 开始快速测试...');
            clearResults();
            
            // 检查基本组件
            const tests = [
                {
                    name: '检查页面加载',
                    test: () => document.readyState === 'complete'
                },
                {
                    name: '检查关卡编辑器脚本',
                    test: () => typeof LevelEditor !== 'undefined'
                },
                {
                    name: '检查全局编辑器实例',
                    test: () => window.levelEditor instanceof LevelEditor
                },
                {
                    name: '检查编辑器按钮',
                    test: () => !!document.getElementById('level-editor-btn')
                },
                {
                    name: '检查编辑器容器',
                    test: () => !!document.getElementById('levelEditorScreen')
                }
            ];
            
            tests.forEach(test => {
                try {
                    const result = test.test();
                    addTestResult(test.name, result);
                } catch (error) {
                    addTestResult(test.name, false, error.message);
                }
            });
            
            updateSummary();
            addConsoleMessage('✅ 快速测试完成');
        }
        
        // 运行完整测试
        function runFullTest() {
            addConsoleMessage('🔍 开始完整测试...');
            clearResults();
            
            // 在主页面中运行测试
            const iframe = document.createElement('iframe');
            iframe.src = '/';
            iframe.style.display = 'none';
            document.body.appendChild(iframe);
            
            iframe.onload = function() {
                try {
                    const iframeWindow = iframe.contentWindow;
                    if (iframeWindow.testLevelEditor) {
                        // 监听控制台输出
                        const originalLog = iframeWindow.console.log;
                        iframeWindow.console.log = function(...args) {
                            addConsoleMessage(args.join(' '));
                            originalLog.apply(iframeWindow.console, args);
                        };
                        
                        // 运行测试
                        iframeWindow.testLevelEditor();
                        
                        setTimeout(() => {
                            document.body.removeChild(iframe);
                            addConsoleMessage('✅ 完整测试完成');
                        }, 3000);
                    } else {
                        addConsoleMessage('❌ 测试函数未找到');
                        document.body.removeChild(iframe);
                    }
                } catch (error) {
                    addConsoleMessage('❌ 测试执行失败: ' + error.message);
                    document.body.removeChild(iframe);
                }
            };
        }
        
        // 打开编辑器
        function openEditor() {
            window.open('/', '_blank');
            addConsoleMessage('🛠️ 在新窗口中打开游戏，请手动点击关卡编辑器按钮');
        }
        
        // 返回主游戏
        function openMainGame() {
            window.location.href = '/';
        }
        
        // 清空结果
        function clearResults() {
            testResults = [];
            document.getElementById('testResults').innerHTML = '<div class="test-result-item"><span>等待测试开始...</span><span class="test-status">⏳</span></div>';
            document.getElementById('testSummary').style.display = 'none';
        }
        
        // 添加测试结果
        function addTestResult(name, passed, details = '') {
            testResults.push({ name, passed, details });
            updateResultsDisplay();
        }
        
        // 更新结果显示
        function updateResultsDisplay() {
            const container = document.getElementById('testResults');
            container.innerHTML = '';
            
            testResults.forEach(result => {
                const item = document.createElement('div');
                item.className = 'test-result-item';
                
                const status = result.passed ? '✅' : '❌';
                const statusClass = result.passed ? 'pass' : 'fail';
                const details = result.details ? `<span class="test-details">${result.details}</span>` : '';
                
                item.innerHTML = `
                    <span>${result.name}${details}</span>
                    <span class="test-status ${statusClass}">${status}</span>
                `;
                
                container.appendChild(item);
            });
        }
        
        // 更新摘要
        function updateSummary() {
            if (testResults.length === 0) return;
            
            const total = testResults.length;
            const passed = testResults.filter(r => r.passed).length;
            const failed = total - passed;
            const percentage = ((passed / total) * 100).toFixed(1);
            
            const summary = document.getElementById('testSummary');
            const content = document.getElementById('summaryContent');
            
            content.innerHTML = `
                <p>总测试数: ${total} | 通过: ${passed} | 失败: ${failed} | 成功率: ${percentage}%</p>
                ${failed > 0 ? '<p style="color: #ff4444;">存在失败的测试项，请检查具体问题</p>' : '<p style="color: #00ff88;">所有测试通过！</p>'}
            `;
            
            summary.style.display = 'block';
        }
        
        // 添加控制台消息
        function addConsoleMessage(message) {
            consoleMessages.push(`[${new Date().toLocaleTimeString()}] ${message}`);
            const console = document.getElementById('consoleOutput');
            console.innerHTML = consoleMessages.slice(-50).join('<br>'); // 只保留最近50条消息
            console.scrollTop = console.scrollHeight;
        }
        
        // 页面加载完成后检查服务器状态
        document.addEventListener('DOMContentLoaded', function() {
            checkServerStatus();
            addConsoleMessage('📋 关卡编辑器测试页面已加载');
            addConsoleMessage('💡 点击"快速测试"开始基础功能检查');
        });
    </script>
</body>
</html>
