/**
 * 关卡选择界面修复验证脚本
 * 用于验证关卡选择界面是否正常工作
 */

// 等待页面完全加载
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 开始验证关卡选择界面修复...');

    // 确保关卡选择界面已经初始化后再进行验证
    initializeLevelSelectForFixVerification().then(() => {
        setTimeout(verifyLevelSelectFix, 1000);
    });
});

/**
 * 为修复验证初始化关卡选择界面
 */
async function initializeLevelSelectForFixVerification() {
    console.log('🔧 为修复验证初始化关卡选择界面...');

    // 检查关卡选择实例是否存在
    if (window.levelSelect && !window.levelSelect.isInitialized) {
        console.log('🔧 初始化关卡选择界面...');
        const initResult = window.levelSelect.init();
        if (initResult) {
            console.log('✅ 关卡选择界面初始化成功');
        } else {
            console.error('❌ 关卡选择界面初始化失败');
        }
    }
}

function verifyLevelSelectFix() {
    console.log('🎯 验证关卡选择界面修复状态');
    
    let allTestsPassed = true;
    
    // 测试1: 检查 LevelSelect 类是否存在
    if (typeof LevelSelect !== 'undefined') {
        console.log('✅ 测试1通过: LevelSelect 类已定义');
    } else {
        console.error('❌ 测试1失败: LevelSelect 类未定义');
        allTestsPassed = false;
    }
    
    // 测试2: 检查全局实例是否存在
    if (window.levelSelect && window.levelSelect instanceof LevelSelect) {
        console.log('✅ 测试2通过: levelSelect 全局实例存在');
    } else {
        console.error('❌ 测试2失败: levelSelect 全局实例不存在');
        allTestsPassed = false;
    }
    
    // 测试3: 检查 DOM 容器是否存在
    const container = document.getElementById('levelSelectScreen');
    if (container) {
        console.log('✅ 测试3通过: levelSelectScreen DOM 容器存在');
    } else {
        console.error('❌ 测试3失败: levelSelectScreen DOM 容器不存在');
        allTestsPassed = false;
    }
    
    // 测试4: 检查 UI Manager 是否存在
    if (window.uiManager) {
        console.log('✅ 测试4通过: uiManager 全局实例存在');
    } else {
        console.error('❌ 测试4失败: uiManager 全局实例不存在');
        allTestsPassed = false;
    }
    
    // 测试5: 检查 UI Manager 的 initializeScreen 方法是否包含 levelSelectScreen 处理
    if (window.uiManager && typeof window.uiManager.initializeScreen === 'function') {
        console.log('✅ 测试5通过: uiManager.initializeScreen 方法存在');
        
        // 尝试检查方法内容（这只是一个简单的检查）
        const methodStr = window.uiManager.initializeScreen.toString();
        if (methodStr.includes('levelSelectScreen')) {
            console.log('✅ 测试5.1通过: initializeScreen 包含 levelSelectScreen 处理');
        } else {
            console.error('❌ 测试5.1失败: initializeScreen 不包含 levelSelectScreen 处理');
            allTestsPassed = false;
        }
    } else {
        console.error('❌ 测试5失败: uiManager.initializeScreen 方法不存在');
        allTestsPassed = false;
    }
    
    // 测试6: 模拟关卡选择界面显示
    if (allTestsPassed) {
        console.log('🧪 执行功能测试: 模拟显示关卡选择界面');
        
        try {
            // 保存原始状态
            const originalDisplay = container.style.display;
            
            // 模拟 UI Manager 的 showScreen 调用
            if (window.uiManager && window.uiManager.showScreen) {
                console.log('📱 调用 uiManager.showScreen("levelSelectScreen")');
                window.uiManager.showScreen('levelSelectScreen');
                
                // 检查结果
                setTimeout(() => {
                    const hasActiveClass = container.classList.contains('active');
                    const isDisplayFlex = container.style.display === 'flex';
                    const computedStyle = window.getComputedStyle(container);
                    const isVisible = computedStyle.display === 'flex' || isDisplayFlex;

                    if (isVisible && hasActiveClass) {
                        console.log('✅ 测试6通过: 关卡选择界面成功显示');
                        console.log(`✅ 测试6.1通过: display 属性正确 (${container.style.display || computedStyle.display})`);
                        console.log(`✅ 测试6.2通过: active 类已添加`);

                        // 检查内容是否生成
                        const levelGrid = document.getElementById('levelGrid');
                        if (levelGrid && levelGrid.children.length > 0) {
                            console.log(`✅ 测试6.3通过: 关卡网格已生成 ${levelGrid.children.length} 个关卡`);
                        } else {
                            console.error('❌ 测试6.3失败: 关卡网格为空');
                        }

                        // 检查关键元素是否存在
                        const levelPreview = document.getElementById('levelPreview');
                        const difficultySelector = document.getElementById('difficultySelector');
                        const startButton = document.getElementById('startLevelButton');

                        if (levelPreview) {
                            console.log('✅ 测试6.4通过: 关卡预览区域已生成');
                        } else {
                            console.error('❌ 测试6.4失败: 关卡预览区域未生成');
                        }

                        if (difficultySelector) {
                            console.log('✅ 测试6.5通过: 难度选择器已生成');
                        } else {
                            console.error('❌ 测试6.5失败: 难度选择器未生成');
                        }

                        if (startButton) {
                            console.log('✅ 测试6.6通过: 开始游戏按钮已生成');
                        } else {
                            console.error('❌ 测试6.6失败: 开始游戏按钮未生成');
                        }

                        // 恢复原始状态
                        setTimeout(() => {
                            container.style.display = originalDisplay;
                            container.classList.remove('active');
                            console.log('🔄 已恢复原始显示状态');
                        }, 2000);

                    } else {
                        console.error('❌ 测试6失败: 关卡选择界面未能正确显示');
                        if (!isVisible) {
                            console.error(`  - display 属性错误: ${container.style.display || computedStyle.display}`);
                        }
                        if (!hasActiveClass) {
                            console.error(`  - 缺少 active 类`);
                        }
                    }
                }, 100);
                
            } else {
                console.error('❌ 测试6失败: uiManager.showScreen 方法不存在');
                allTestsPassed = false;
            }
            
        } catch (error) {
            console.error(`❌ 测试6失败: 功能测试出错 - ${error.message}`);
            allTestsPassed = false;
        }
    }
    
    // 输出最终结果
    setTimeout(() => {
        if (allTestsPassed) {
            console.log('🎉 所有测试通过！关卡选择界面修复成功！');
            console.log('💡 现在点击"开始游戏"按钮应该能正常显示关卡选择界面');
        } else {
            console.error('❌ 部分测试失败，关卡选择界面可能仍有问题');
        }
    }, 3000);
}

// 添加一个手动测试函数
window.testLevelSelect = function() {
    console.log('🎮 手动测试关卡选择界面');
    
    if (window.levelSelect) {
        if (!window.levelSelect.isInitialized) {
            console.log('🔧 初始化关卡选择界面...');
            window.levelSelect.init();
        }
        
        console.log('📱 显示关卡选择界面...');
        window.levelSelect.show();
        
        setTimeout(() => {
            const container = document.getElementById('levelSelectScreen');
            if (container && container.style.display === 'flex') {
                console.log('✅ 手动测试成功！关卡选择界面已显示');
            } else {
                console.error('❌ 手动测试失败！关卡选择界面未显示');
            }
        }, 500);
    } else {
        console.error('❌ levelSelect 实例不存在');
    }
};

console.log('🔧 修复验证脚本已加载');
console.log('💡 可以在控制台运行 testLevelSelect() 进行手动测试');
