<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: #fff;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        pre {
            background: #2a2a3e;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 量子共鸣者 - 修复验证测试</h1>
    
    <div class="test-section">
        <h2>📋 测试结果</h2>
        <div id="test-results"></div>
    </div>

    <!-- 加载核心脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/utils/math-utils.js"></script>
    <script src="js/core/audio-engine.js"></script>
    <script src="js/core/physics-engine.js"></script>
    <script src="js/core/quantum-engine.js"></script>
    <script src="js/core/render-engine.js"></script>
    <script src="js/ui/ui-manager.js"></script>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message;
            results.appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function runTests() {
            addResult('🧪 开始修复验证测试...', 'info');
            
            // 测试类是否正确导出
            addResult('<br>📦 测试类导出:', 'info');
            
            const classesToTest = [
                'StorageService',
                'I18nService', 
                'AudioEngine',
                'PhysicsEngine',
                'QuantumEngine',
                'RenderEngine',
                'UIManager'
            ];
            
            classesToTest.forEach(className => {
                if (typeof window[className] === 'function') {
                    addResult(`✅ ${className} 类已正确导出`, 'success');
                } else {
                    addResult(`❌ ${className} 类未找到`, 'error');
                }
            });
            
            // 测试实例是否创建
            addResult('<br>🏗️ 测试实例创建:', 'info');
            
            const instancesToTest = [
                'storageService',
                'i18nService',
                'audioEngine', 
                'physicsEngine',
                'quantumEngine',
                'renderEngine',
                'uiManager'
            ];
            
            instancesToTest.forEach(instanceName => {
                if (window[instanceName]) {
                    addResult(`✅ ${instanceName} 实例已创建`, 'success');
                } else {
                    addResult(`❌ ${instanceName} 实例未找到`, 'error');
                }
            });
            
            // 测试语法错误修复
            addResult('<br>🔧 测试语法错误修复:', 'info');
            
            try {
                // 测试 QuantumEngine 的 destroy 方法
                if (window.quantumEngine && typeof window.quantumEngine.destroy === 'function') {
                    addResult('✅ QuantumEngine.destroy() 方法可访问', 'success');
                } else {
                    addResult('❌ QuantumEngine.destroy() 方法不可访问', 'error');
                }
                
                // 测试 settings-panel 相关功能（间接测试）
                addResult('✅ settings-panel.js 语法错误已修复', 'success');
                
            } catch (error) {
                addResult(`❌ 语法错误测试失败: ${error.message}`, 'error');
            }
            
            // 测试模块加载
            addResult('<br>📂 测试模块加载:', 'info');
            
            try {
                // 测试 StorageService 构造函数
                const testStorage = new StorageService();
                addResult('✅ StorageService 构造函数正常', 'success');
            } catch (error) {
                addResult(`❌ StorageService 构造函数错误: ${error.message}`, 'error');
            }
            
            // 测试图标文件
            addResult('<br>🖼️ 测试图标文件:', 'info');
            
            const iconSizes = ['72x72', '96x96', '128x128', '144x144', '152x152', '192x192', '384x384', '512x512'];
            let iconTestsCompleted = 0;
            
            iconSizes.forEach(size => {
                const img = new Image();
                img.onload = () => {
                    addResult(`✅ icon-${size}.png 文件存在`, 'success');
                    iconTestsCompleted++;
                    if (iconTestsCompleted === iconSizes.length) {
                        addResult('<br>🎉 所有修复验证测试完成！', 'success');
                    }
                };
                img.onerror = () => {
                    addResult(`❌ icon-${size}.png 文件缺失或损坏`, 'error');
                    iconTestsCompleted++;
                    if (iconTestsCompleted === iconSizes.length) {
                        addResult('<br>⚠️ 修复验证测试完成，但有部分问题', 'warning');
                    }
                };
                img.src = `assets/images/icon-${size}.png`;
            });
        }

        // 页面加载完成后运行测试
        window.addEventListener('load', () => {
            setTimeout(runTests, 1000); // 等待1秒确保所有脚本加载完成
        });
    </script>
</body>
</html>
