<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量子共鸣者 - i18n测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: rgba(255,255,255,0.1);
        }
        .pass { border-left: 4px solid #00ff00; }
        .fail { border-left: 4px solid #ff0000; }
        button {
            background: #00d4ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .log-output {
            background: #000;
            color: #00ff00;
            font-family: monospace;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🎮 量子共鸣者 - i18n服务测试</h1>
    <button onclick="testI18n()">测试 i18n 服务</button>
    <button onclick="testInit()">测试 init 方法</button>
    <button onclick="clearResults()">清空结果</button>
    <div id="results"></div>
    <div id="logs" class="log-output" style="display: none;"></div>

    <!-- 只加载 i18n 脚本 -->
    <script src="js/utils/i18n.js"></script>

    <script>
        let logs = [];

        // 捕获控制台日志
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            logs.push(`[LOG] ${args.join(' ')}`);
            originalLog.apply(console, args);
            updateLogs();
        };

        console.error = function(...args) {
            logs.push(`[ERROR] ${args.join(' ')}`);
            originalError.apply(console, args);
            updateLogs();
        };

        console.warn = function(...args) {
            logs.push(`[WARN] ${args.join(' ')}`);
            originalWarn.apply(console, args);
            updateLogs();
        };

        function updateLogs() {
            const logsDiv = document.getElementById('logs');
            logsDiv.innerHTML = logs.slice(-20).join('\n'); // 只显示最近20条日志
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        function addResult(name, passed, message) {
            const div = document.createElement('div');
            div.className = `test-result ${passed ? 'pass' : 'fail'}`;
            div.innerHTML = `<strong>${name}:</strong> ${message}`;
            document.getElementById('results').appendChild(div);
        }

        function testI18n() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('logs').style.display = 'block';
            logs = [];
            
            console.log('🔍 开始测试 i18n 服务...');
            
            // 测试1: i18nService 存在
            const i18nServiceExists = typeof window.i18nService !== 'undefined';
            addResult('i18nService 存在', i18nServiceExists, 
                i18nServiceExists ? '✅ window.i18nService 存在' : '❌ window.i18nService 不存在');
            
            // 测试2: i18n 别名存在
            const i18nExists = typeof window.i18n !== 'undefined';
            addResult('i18n 别名存在', i18nExists, 
                i18nExists ? '✅ window.i18n 存在' : '❌ window.i18n 不存在');
            
            // 测试3: i18n 和 i18nService 是同一个对象
            if (i18nServiceExists && i18nExists) {
                const sameObject = window.i18n === window.i18nService;
                addResult('i18n 别名正确', sameObject, 
                    sameObject ? '✅ i18n 是 i18nService 的别名' : '❌ i18n 不是 i18nService 的别名');
            }
            
            // 测试4: init 方法存在
            if (i18nExists) {
                const hasInit = typeof window.i18n.init === 'function';
                addResult('init 方法存在', hasInit, 
                    hasInit ? '✅ i18n.init 方法存在' : '❌ i18n.init 方法不存在');
                
                // 测试5: 其他关键方法存在
                const methods = ['t', 'setLanguage', 'getCurrentLanguage', 'updatePageTexts'];
                methods.forEach(method => {
                    const hasMethod = typeof window.i18n[method] === 'function';
                    addResult(`${method} 方法存在`, hasMethod, 
                        hasMethod ? `✅ i18n.${method} 方法存在` : `❌ i18n.${method} 方法不存在`);
                });
            }
        }

        async function testInit() {
            document.getElementById('logs').style.display = 'block';
            
            console.log('🚀 开始测试 i18n.init() 方法...');
            
            if (!window.i18n) {
                addResult('init 测试', false, '❌ i18n 服务不存在');
                return;
            }
            
            if (typeof window.i18n.init !== 'function') {
                addResult('init 测试', false, '❌ i18n.init 方法不存在');
                return;
            }
            
            try {
                const result = await window.i18n.init();
                addResult('init 方法调用', true, `✅ i18n.init() 调用成功，返回值: ${result}`);
                
                // 测试翻译功能
                const testKey = 'game.title';
                const translation = window.i18n.t(testKey);
                addResult('翻译功能测试', true, `✅ 翻译 "${testKey}": "${translation}"`);
                
                // 测试当前语言
                const currentLang = window.i18n.getCurrentLanguage();
                addResult('当前语言获取', true, `✅ 当前语言: ${currentLang}`);
                
            } catch (error) {
                addResult('init 方法调用', false, `❌ i18n.init() 调用失败: ${error.message}`);
                console.error('详细错误:', error);
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('logs').style.display = 'none';
            logs = [];
        }

        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                console.log('🚀 页面加载完成，开始自动测试...');
                testI18n();
            }, 500);
        });
    </script>
</body>
</html>
