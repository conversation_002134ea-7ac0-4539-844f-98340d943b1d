<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关卡管理器测试 - 量子共鸣者</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #ffffff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            color: #00d4ff;
            margin-bottom: 30px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            font-size: 2.5em;
        }
        
        .test-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #00d4ff;
        }
        
        .test-result {
            margin: 8px 0;
            padding: 12px;
            border-radius: 6px;
            background: rgba(0, 0, 0, 0.3);
            border-left: 4px solid #666;
            display: flex;
            align-items: center;
            font-size: 14px;
        }
        
        .pass { 
            border-left-color: #00ff00; 
            background: rgba(0, 255, 0, 0.1);
        }
        
        .fail { 
            border-left-color: #ff0000; 
            background: rgba(255, 0, 0, 0.1);
        }
        
        .status-indicator {
            display: inline-block;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .status-pass { background: #00ff00; box-shadow: 0 0 8px rgba(0, 255, 0, 0.5); }
        .status-fail { background: #ff0000; box-shadow: 0 0 8px rgba(255, 0, 0, 0.5); }
        
        button {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        
        button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
        }
        
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        
        h3 {
            color: #00d4ff;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .log-output {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .summary {
            background: rgba(0, 212, 255, 0.2);
            border: 2px solid #00d4ff;
            padding: 25px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 关卡管理器测试</h1>
        
        <div class="controls">
            <button onclick="runLevelManagerTest()">🚀 运行测试</button>
            <button onclick="testRegisterLevelPack()">📦 测试关卡包注册</button>
            <button onclick="clearResults()">🧹 清空结果</button>
        </div>
        
        <div id="test-results"></div>
        
        <div class="test-section">
            <h3>📋 实时日志</h3>
            <div id="log-output" class="log-output"></div>
        </div>
    </div>

    <!-- 必要的DOM元素 -->
    <div id="loadingScreen" style="display: none;">
        <div id="loadingProgress" style="width: 0%; height: 4px; background: #00d4ff;"></div>
        <div id="loadingText">0%</div>
    </div>

    <!-- 加载关键脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/game/level-manager.js"></script>

    <script>
        let testResults = [];
        let logOutput = [];

        // 捕获控制台日志
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function captureLog(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            logOutput.push(logEntry);
            updateLogDisplay();
            
            // 调用原始方法
            if (type === 'error') {
                originalError(message);
            } else if (type === 'warn') {
                originalWarn(message);
            } else {
                originalLog(message);
            }
        }

        console.log = (...args) => captureLog(args.join(' '), 'log');
        console.error = (...args) => captureLog(args.join(' '), 'error');
        console.warn = (...args) => captureLog(args.join(' '), 'warn');

        function updateLogDisplay() {
            const logDiv = document.getElementById('log-output');
            logDiv.textContent = logOutput.slice(-50).join('\n'); // 显示最近50条日志
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function addResult(name, passed, message) {
            const result = { name, passed, message };
            testResults.push(result);
            
            const resultsDiv = document.getElementById('test-results');
            const section = resultsDiv.querySelector('.test-section') || createTestSection('测试结果', '🧪');
            
            const div = document.createElement('div');
            div.className = `test-result ${passed ? 'pass' : 'fail'}`;
            div.innerHTML = `
                <span class="status-indicator ${passed ? 'status-pass' : 'status-fail'}"></span>
                <div>
                    <strong>${name}:</strong><br>
                    <span style="opacity: 0.9; font-size: 12px;">${message}</span>
                </div>
            `;
            section.appendChild(div);
        }

        function createTestSection(title, emoji) {
            const resultsDiv = document.getElementById('test-results');
            const section = document.createElement('div');
            section.className = 'test-section';
            section.innerHTML = `<h3><span style="margin-right: 10px;">${emoji}</span>${title}</h3>`;
            resultsDiv.appendChild(section);
            return section;
        }

        async function runLevelManagerTest() {
            testResults = [];
            logOutput = [];
            document.getElementById('test-results').innerHTML = '';
            
            console.log('🎯 开始关卡管理器测试...');
            
            // 等待初始化
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 测试关卡管理器存在
            const managerExists = typeof window.levelManager !== 'undefined';
            addResult('关卡管理器存在', managerExists, 
                managerExists ? '✅ window.levelManager 已创建' : '❌ window.levelManager 不存在');
            
            if (managerExists) {
                // 测试 registerLevelPack 方法
                const hasRegisterMethod = typeof window.levelManager.registerLevelPack === 'function';
                addResult('registerLevelPack 方法', hasRegisterMethod, 
                    hasRegisterMethod ? '✅ registerLevelPack 方法存在' : '❌ registerLevelPack 方法不存在');
                
                // 测试初始化
                const hasInit = typeof window.levelManager.init === 'function';
                addResult('init 方法', hasInit, 
                    hasInit ? '✅ init 方法存在' : '❌ init 方法不存在');
                
                if (hasInit) {
                    try {
                        const initResult = window.levelManager.init();
                        addResult('初始化调用', true, '✅ init 方法调用成功');
                    } catch (error) {
                        addResult('初始化调用', false, `❌ init 调用失败: ${error.message}`);
                    }
                }
                
                // 测试其他关键方法
                const methods = [
                    'loadLevel',
                    'getLevelConfig', 
                    'getAllLevelPacks',
                    'getLevelPackInfo'
                ];
                
                methods.forEach(methodName => {
                    const hasMethod = typeof window.levelManager[methodName] === 'function';
                    addResult(`${methodName} 方法`, hasMethod, 
                        hasMethod ? `✅ ${methodName} 方法存在` : `❌ ${methodName} 方法不存在`);
                });
            }
            
            generateSummary();
        }

        async function testRegisterLevelPack() {
            console.log('📦 测试关卡包注册功能...');
            
            if (!window.levelManager) {
                addResult('关卡包注册测试', false, '❌ 关卡管理器不存在');
                return;
            }
            
            // 测试关卡包数据
            const testPack = {
                name: '测试关卡包',
                description: '用于测试的关卡包',
                author: '测试系统',
                levels: [
                    {
                        name: '测试关卡1',
                        description: '第一个测试关卡',
                        particles: [
                            { x: 300, y: 200, frequency: 440, radius: 15 },
                            { x: 500, y: 200, frequency: 880, radius: 15 }
                        ],
                        targetScore: 500,
                        timeLimit: 30
                    },
                    {
                        name: '测试关卡2',
                        description: '第二个测试关卡',
                        particles: [
                            { x: 200, y: 150, frequency: 330, radius: 12 },
                            { x: 400, y: 150, frequency: 660, radius: 12 },
                            { x: 600, y: 150, frequency: 990, radius: 12 }
                        ],
                        targetScore: 800,
                        timeLimit: 45
                    }
                ]
            };
            
            try {
                const result = window.levelManager.registerLevelPack('test-pack', testPack);
                addResult('关卡包注册', result, 
                    result ? '✅ 测试关卡包注册成功' : '❌ 关卡包注册失败');
                
                if (result) {
                    // 测试获取关卡包信息
                    const packInfo = window.levelManager.getLevelPackInfo('test-pack');
                    addResult('获取关卡包信息', !!packInfo, 
                        packInfo ? `✅ 获取到关卡包: ${packInfo.name}` : '❌ 无法获取关卡包信息');
                    
                    // 测试获取所有关卡包
                    const allPacks = window.levelManager.getAllLevelPacks();
                    addResult('获取所有关卡包', Array.isArray(allPacks), 
                        Array.isArray(allPacks) ? `✅ 获取到 ${allPacks.length} 个关卡包` : '❌ 无法获取关卡包列表');
                }
                
            } catch (error) {
                addResult('关卡包注册', false, `❌ 注册异常: ${error.message}`);
            }
        }

        function generateSummary() {
            const passed = testResults.filter(r => r.passed).length;
            const total = testResults.length;
            const percentage = Math.round((passed / total) * 100);
            
            const summaryDiv = document.createElement('div');
            summaryDiv.className = 'summary';
            
            let emoji, status, color;
            if (percentage === 100) {
                emoji = '🎉';
                status = '完美通过';
                color = '#00ff00';
            } else if (percentage >= 80) {
                emoji = '🎯';
                status = '基本通过';
                color = '#ffaa00';
            } else {
                emoji = '😞';
                status = '需要修复';
                color = '#ff6600';
            }
            
            summaryDiv.innerHTML = `
                <div style="font-size: 3em; margin-bottom: 15px;">${emoji}</div>
                <div style="color: ${color}; font-size: 1.5em;">
                    关卡管理器测试结果: ${passed}/${total} (${percentage}%) ${status}
                </div>
            `;
            
            document.getElementById('test-results').appendChild(summaryDiv);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('log-output').textContent = '';
            testResults = [];
            logOutput = [];
        }

        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 关卡管理器测试页面加载完成');
            
            setTimeout(() => {
                console.log('⏰ 开始自动运行测试...');
                runLevelManagerTest();
            }, 2000);
        });
    </script>
</body>
</html>
