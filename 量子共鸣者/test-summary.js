/**
 * 量子共鸣者 - 测试总结脚本
 * 整合所有测试结果，生成完整的测试报告
 */

class TestSummary {
    constructor() {
        this.allTests = {
            systemTest: null,
            compatibilityTest: null,
            gameFunctionalityTest: null,
            performanceTest: null
        };
        
        this.overallResults = {
            totalTests: 0,
            passedTests: 0,
            failedTests: 0,
            successRate: 0,
            recommendations: []
        };
        
        console.log('📋 测试总结器已创建');
    }

    /**
     * 生成完整的测试总结报告
     */
    generateTestSummary() {
        console.log('='.repeat(60));
        console.log('📋 量子共鸣者 - 完整测试总结报告');
        console.log('='.repeat(60));
        
        // 收集所有测试结果
        this.collectTestResults();
        
        // 生成总体统计
        this.generateOverallStatistics();
        
        // 生成详细报告
        this.generateDetailedReport();
        
        // 生成优化建议
        this.generateOptimizationRecommendations();
        
        // 生成部署建议
        this.generateDeploymentRecommendations();
        
        console.log('='.repeat(60));
        console.log('📋 测试总结报告完成');
        console.log('='.repeat(60));
    }

    /**
     * 收集所有测试结果
     */
    collectTestResults() {
        // 系统测试结果
        if (window.runSystemTests) {
            console.log('📦 收集系统测试结果...');
            // 系统测试结果已在控制台输出
        }
        
        // 兼容性测试结果
        if (window.compatibilityTest) {
            console.log('🔍 收集兼容性测试结果...');
            this.allTests.compatibilityTest = {
                features: compatibilityTest.features,
                deviceInfo: compatibilityTest.deviceInfo,
                browserInfo: compatibilityTest.browserInfo
            };
        }
        
        // 游戏功能测试结果
        if (window.gameFunctionalityTest) {
            console.log('🎮 收集游戏功能测试结果...');
            this.allTests.gameFunctionalityTest = gameFunctionalityTest.testResults;
        }
        
        // 性能测试结果
        if (window.performanceTest) {
            console.log('🚀 收集性能测试结果...');
            this.allTests.performanceTest = performanceTest.metrics;
        }
    }

    /**
     * 生成总体统计
     */
    generateOverallStatistics() {
        console.log('\n📊 总体测试统计:');
        
        let totalTests = 0;
        let passedTests = 0;
        let failedTests = 0;
        
        // 统计游戏功能测试
        if (this.allTests.gameFunctionalityTest) {
            totalTests += this.allTests.gameFunctionalityTest.total;
            passedTests += this.allTests.gameFunctionalityTest.passed;
            failedTests += this.allTests.gameFunctionalityTest.failed;
        }
        
        // 统计兼容性测试
        if (this.allTests.compatibilityTest) {
            const compatibilityFeatures = Object.values(this.allTests.compatibilityTest.features);
            const supportedFeatures = compatibilityFeatures.filter(Boolean).length;
            totalTests += compatibilityFeatures.length;
            passedTests += supportedFeatures;
            failedTests += compatibilityFeatures.length - supportedFeatures;
        }
        
        this.overallResults = {
            totalTests,
            passedTests,
            failedTests,
            successRate: totalTests > 0 ? (passedTests / totalTests) * 100 : 0
        };
        
        console.log(`   总测试数: ${totalTests}`);
        console.log(`   通过测试: ${passedTests}`);
        console.log(`   失败测试: ${failedTests}`);
        console.log(`   成功率: ${this.overallResults.successRate.toFixed(1)}%`);
        
        // 评估整体质量
        this.evaluateOverallQuality();
    }

    /**
     * 评估整体质量
     */
    evaluateOverallQuality() {
        const successRate = this.overallResults.successRate;
        
        console.log('\n🎯 整体质量评估:');
        
        if (successRate >= 90) {
            console.log('   ✅ 优秀 - 游戏质量很高，可以发布');
        } else if (successRate >= 80) {
            console.log('   ⚠️ 良好 - 游戏质量不错，建议修复少量问题后发布');
        } else if (successRate >= 70) {
            console.log('   ⚠️ 一般 - 游戏基本可用，但需要优化多个方面');
        } else {
            console.log('   ❌ 较差 - 游戏存在较多问题，需要大量优化');
        }
    }

    /**
     * 生成详细报告
     */
    generateDetailedReport() {
        console.log('\n📋 详细测试报告:');
        
        // 兼容性报告
        this.generateCompatibilityReport();
        
        // 功能测试报告
        this.generateFunctionalityReport();
        
        // 性能测试报告
        this.generatePerformanceReport();
    }

    /**
     * 生成兼容性报告
     */
    generateCompatibilityReport() {
        if (!this.allTests.compatibilityTest) return;
        
        console.log('\n🔍 兼容性测试详情:');
        
        const { features, deviceInfo, browserInfo } = this.allTests.compatibilityTest;
        
        console.log(`   设备类型: ${compatibilityTest.getDeviceType()}`);
        console.log(`   浏览器: ${browserInfo.name} ${browserInfo.version}`);
        console.log(`   屏幕分辨率: ${deviceInfo.screenWidth}x${deviceInfo.screenHeight}`);
        
        const supportedFeatures = Object.entries(features)
            .filter(([key, value]) => value)
            .map(([key]) => key);
        
        const unsupportedFeatures = Object.entries(features)
            .filter(([key, value]) => !value)
            .map(([key]) => key);
        
        if (supportedFeatures.length > 0) {
            console.log(`   支持的功能: ${supportedFeatures.join(', ')}`);
        }
        
        if (unsupportedFeatures.length > 0) {
            console.log(`   不支持的功能: ${unsupportedFeatures.join(', ')}`);
        }
    }

    /**
     * 生成功能测试报告
     */
    generateFunctionalityReport() {
        if (!this.allTests.gameFunctionalityTest) return;
        
        console.log('\n🎮 游戏功能测试详情:');
        
        const { details } = this.allTests.gameFunctionalityTest;
        
        const failedTests = details.filter(test => test.status !== 'passed');
        
        if (failedTests.length > 0) {
            console.log('   失败的功能测试:');
            failedTests.forEach(test => {
                console.log(`     - ${test.name}: ${test.error || '测试失败'}`);
            });
        } else {
            console.log('   ✅ 所有功能测试通过');
        }
    }

    /**
     * 生成性能测试报告
     */
    generatePerformanceReport() {
        if (!this.allTests.performanceTest) return;
        
        console.log('\n🚀 性能测试详情:');
        
        const metrics = this.allTests.performanceTest;
        
        if (metrics.frameRate && metrics.frameRate.length > 0) {
            const avgFPS = metrics.frameRate.reduce((a, b) => a + b, 0) / metrics.frameRate.length;
            console.log(`   平均帧率: ${avgFPS.toFixed(1)} FPS`);
        }
        
        if (metrics.memoryUsage && metrics.memoryUsage.length > 0) {
            const avgMemory = metrics.memoryUsage.reduce((a, b) => a + b.used, 0) / metrics.memoryUsage.length;
            console.log(`   平均内存使用: ${avgMemory.toFixed(1)} MB`);
        }
        
        if (metrics.renderTime && metrics.renderTime.length > 0) {
            const avgRenderTime = metrics.renderTime.reduce((a, b) => a + b, 0) / metrics.renderTime.length;
            console.log(`   平均渲染时间: ${avgRenderTime.toFixed(2)} ms`);
        }
    }

    /**
     * 生成优化建议
     */
    generateOptimizationRecommendations() {
        console.log('\n💡 优化建议:');
        
        const recommendations = [];
        
        // 基于兼容性测试的建议
        if (this.allTests.compatibilityTest) {
            const { features } = this.allTests.compatibilityTest;
            
            if (!features.webGL) {
                recommendations.push('WebGL不支持，建议优化Canvas 2D渲染性能');
            }
            
            if (!features.webAudio) {
                recommendations.push('Web Audio API不支持，建议提供HTML5 Audio降级方案');
            }
            
            if (!features.indexedDB) {
                recommendations.push('IndexedDB不支持，建议使用localStorage作为备选存储');
            }
        }
        
        // 基于功能测试的建议
        if (this.allTests.gameFunctionalityTest) {
            const failedTests = this.allTests.gameFunctionalityTest.details
                .filter(test => test.status !== 'passed');
            
            if (failedTests.length > 0) {
                recommendations.push(`修复${failedTests.length}个失败的功能测试`);
            }
        }
        
        // 基于性能测试的建议
        if (this.allTests.performanceTest) {
            const metrics = this.allTests.performanceTest;
            
            if (metrics.frameRate && metrics.frameRate.length > 0) {
                const avgFPS = metrics.frameRate.reduce((a, b) => a + b, 0) / metrics.frameRate.length;
                if (avgFPS < 30) {
                    recommendations.push('帧率较低，建议优化渲染性能');
                }
            }
            
            if (metrics.memoryUsage && metrics.memoryUsage.length > 0) {
                const avgMemory = metrics.memoryUsage.reduce((a, b) => a + b.used, 0) / metrics.memoryUsage.length;
                if (avgMemory > 100) {
                    recommendations.push('内存使用较高，建议检查内存泄漏');
                }
            }
        }
        
        if (recommendations.length === 0) {
            console.log('   🎉 无需特别优化，游戏表现良好！');
        } else {
            recommendations.forEach((rec, index) => {
                console.log(`   ${index + 1}. ${rec}`);
            });
        }
        
        this.overallResults.recommendations = recommendations;
    }

    /**
     * 生成部署建议
     */
    generateDeploymentRecommendations() {
        console.log('\n🚀 部署建议:');
        
        const deploymentTips = [
            '使用HTTPS协议以支持Web Audio API和其他现代Web功能',
            '配置适当的缓存策略以提高加载速度',
            '压缩JavaScript和CSS文件以减少传输大小',
            '使用CDN加速静态资源加载',
            '配置适当的Content-Security-Policy头',
            '确保服务器支持gzip压缩',
            '为移动设备优化触摸交互',
            '提供离线支持（Service Worker）'
        ];
        
        deploymentTips.forEach((tip, index) => {
            console.log(`   ${index + 1}. ${tip}`);
        });
    }

    /**
     * 导出完整测试报告
     */
    exportFullReport() {
        const report = {
            timestamp: new Date().toISOString(),
            gameInfo: {
                name: '量子共鸣者',
                version: '1.0.0',
                description: '音乐节拍 + 物理模拟 + 策略布局的量子粒子共振游戏'
            },
            overallResults: this.overallResults,
            detailedResults: this.allTests,
            deviceInfo: this.allTests.compatibilityTest?.deviceInfo,
            browserInfo: this.allTests.compatibilityTest?.browserInfo
        };
        
        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `quantum-resonance-test-report-${Date.now()}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        
        console.log('📁 完整测试报告已导出');
    }

    /**
     * 生成测试通过证书
     */
    generateTestCertificate() {
        if (this.overallResults.successRate >= 90) {
            console.log('\n🏆 测试通过证书:');
            console.log('┌─────────────────────────────────────────┐');
            console.log('│          量子共鸣者游戏                 │');
            console.log('│         测试通过证书                    │');
            console.log('├─────────────────────────────────────────┤');
            console.log(`│ 测试日期: ${new Date().toLocaleDateString('zh-CN')}                    │`);
            console.log(`│ 成功率: ${this.overallResults.successRate.toFixed(1)}%                          │`);
            console.log(`│ 通过测试: ${this.overallResults.passedTests}/${this.overallResults.totalTests}                        │`);
            console.log('│ 质量等级: 优秀                          │');
            console.log('│ 建议: 可以发布                         │');
            console.log('└─────────────────────────────────────────┘');
        } else {
            console.log('\n⚠️ 游戏需要进一步优化才能获得测试通过证书');
        }
    }
}

// 创建全局测试总结实例
window.testSummary = new TestSummary();

// 添加快捷方法到控制台
window.generateTestSummary = () => testSummary.generateTestSummary();
window.exportFullTestReport = () => testSummary.exportFullReport();
window.generateTestCertificate = () => testSummary.generateTestCertificate();

console.log('📋 测试总结器已加载');
console.log('💡 使用 generateTestSummary() 生成完整测试总结');
console.log('💡 使用 exportFullTestReport() 导出完整报告');
console.log('💡 使用 generateTestCertificate() 生成测试证书');

// 延迟运行测试总结，确保所有测试都完成
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        testSummary.generateTestSummary();
        testSummary.generateTestCertificate();
    }, 5000); // 等待5秒确保所有测试完成
});
