<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Manager 修复测试 - 量子共鸣者</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0f0f1e;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background: #1a1a2e;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 4px solid #4a90e2;
        }
        
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .test-result.success {
            background: #2d5a2d;
            color: #90ee90;
        }
        
        .test-result.error {
            background: #5a2d2d;
            color: #ff6b6b;
        }
        
        .test-result.warning {
            background: #5a5a2d;
            color: #ffff90;
        }
        
        .test-result.info {
            background: #2d4a5a;
            color: #87ceeb;
        }
        
        .test-button {
            background: #4a90e2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #357abd;
        }
        
        .screen-preview {
            border: 2px solid #333;
            margin: 10px 0;
            padding: 10px;
            background: #0f0f1e;
            border-radius: 5px;
        }
        
        .screen-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 10px;
            background: #2a2a3e;
            border-radius: 3px;
            margin: 5px 0;
        }
        
        .screen-status {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .screen-status.visible {
            background: #2d5a2d;
            color: #90ee90;
        }
        
        .screen-status.hidden {
            background: #5a2d2d;
            color: #ff6b6b;
        }
        
        .console-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 UI Manager 修复测试</h1>
        <p>测试UI Manager的屏幕切换和显示功能</p>
        
        <!-- UI Manager 初始化测试 -->
        <div class="test-section">
            <h2>⚙️ UI Manager 初始化测试</h2>
            <div id="ui-init-results"></div>
            
            <button class="test-button" onclick="testUIManagerInit()">测试UI Manager初始化</button>
            <button class="test-button" onclick="testScreenRegistration()">测试屏幕注册</button>
        </div>
        
        <!-- 屏幕切换测试 -->
        <div class="test-section">
            <h2>📱 屏幕切换测试</h2>
            <div id="screen-switch-results"></div>
            
            <div class="screen-preview" id="screen-preview">
                <!-- 屏幕状态将显示在这里 -->
            </div>
            
            <button class="test-button" onclick="testShowScreen('main-menu-screen')">显示主菜单</button>
            <button class="test-button" onclick="testShowScreen('game-screen')">显示游戏屏幕</button>
            <button class="test-button" onclick="testShowScreen('levelSelectScreen')">显示关卡选择</button>
            <button class="test-button" onclick="testShowScreen('settings-screen')">显示设置</button>
            <button class="test-button" onclick="refreshScreenStatus()">刷新屏幕状态</button>
        </div>
        
        <!-- 关卡选择测试 -->
        <div class="test-section">
            <h2>🎯 关卡选择测试</h2>
            <div id="level-select-results"></div>
            
            <button class="test-button" onclick="testLevelSelectInit()">测试关卡选择初始化</button>
            <button class="test-button" onclick="testLevelSelectShow()">测试关卡选择显示</button>
            <button class="test-button" onclick="testLevelSelectElements()">检查关卡选择元素</button>
        </div>
        
        <!-- 控制台输出监控 -->
        <div class="test-section">
            <h2>📊 控制台输出监控</h2>
            <div class="console-output" id="console-output"></div>
            <button class="test-button" onclick="clearConsoleOutput()">清空输出</button>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/ui/ui-manager.js"></script>
    <script src="js/ui/level-select.js"></script>
    
    <script>
        let consoleOutput = document.getElementById('console-output');
        let originalConsole = {};
        let testUIManager = null;
        
        // 劫持控制台输出
        function setupConsoleCapture() {
            ['log', 'error', 'warn', 'info'].forEach(method => {
                originalConsole[method] = console[method];
                console[method] = function(...args) {
                    // 调用原始方法
                    originalConsole[method].apply(console, args);
                    
                    // 添加到我们的输出
                    const timestamp = new Date().toLocaleTimeString();
                    const message = args.map(arg => 
                        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                    ).join(' ');
                    
                    const color = {
                        log: '#0f0',
                        error: '#f00',
                        warn: '#ff0',
                        info: '#0ff'
                    }[method] || '#0f0';
                    
                    consoleOutput.innerHTML += `<div style="color: ${color}">[${timestamp}] ${method.toUpperCase()}: ${message}</div>`;
                    consoleOutput.scrollTop = consoleOutput.scrollHeight;
                };
            });
        }
        
        // 添加测试结果
        function addTestResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            container.appendChild(resultDiv);
        }
        
        // 创建必要的DOM元素
        function createTestScreens() {
            const screens = [
                { id: 'loading-screen', name: '加载屏幕' },
                { id: 'main-menu-screen', name: '主菜单' },
                { id: 'game-screen', name: '游戏屏幕' },
                { id: 'levelSelectScreen', name: '关卡选择' },
                { id: 'settings-screen', name: '设置屏幕' }
            ];
            
            screens.forEach(screen => {
                if (!document.getElementById(screen.id)) {
                    const screenDiv = document.createElement('div');
                    screenDiv.id = screen.id;
                    screenDiv.className = 'screen';
                    screenDiv.style.display = 'none';
                    screenDiv.style.opacity = '0';
                    screenDiv.style.visibility = 'hidden';
                    screenDiv.innerHTML = `<h2>${screen.name}</h2><p>这是${screen.name}的测试内容</p>`;
                    document.body.appendChild(screenDiv);
                    console.log(`✅ 创建测试屏幕: ${screen.id}`);
                }
            });
        }
        
        // 测试UI Manager初始化
        function testUIManagerInit() {
            const containerId = 'ui-init-results';
            document.getElementById(containerId).innerHTML = '';

            try {
                addTestResult(containerId, '🔧 开始测试UI Manager初始化...', 'info');

                // 检查是否有全局UI Manager实例
                if (window.uiManager) {
                    testUIManager = window.uiManager;
                    addTestResult(containerId, '✅ 使用全局UI Manager实例', 'success');
                } else {
                    // 检查UIManager类是否存在
                    if (typeof UIManager === 'undefined') {
                        throw new Error('UIManager类未找到');
                    }
                    addTestResult(containerId, '✅ UIManager类已找到', 'success');

                    // 创建UI Manager实例
                    testUIManager = new UIManager();
                    addTestResult(containerId, '✅ UIManager实例创建成功', 'success');

                    // 初始化UI Manager
                    const initResult = testUIManager.init();
                    if (initResult) {
                        addTestResult(containerId, '✅ UIManager初始化成功', 'success');
                    } else {
                        throw new Error('UIManager初始化失败');
                    }
                }

                // 检查屏幕注册情况
                const screenCount = testUIManager.screens.size;
                addTestResult(containerId, `📱 已注册 ${screenCount} 个屏幕`, 'info');

                addTestResult(containerId, '✅ UI Manager初始化测试完成', 'success');

            } catch (error) {
                addTestResult(containerId, `❌ UI Manager初始化测试失败: ${error.message}`, 'error');
                console.error('UI Manager初始化测试错误:', error);
            }
        }
        
        // 测试屏幕注册
        function testScreenRegistration() {
            const containerId = 'ui-init-results';
            
            if (!testUIManager) {
                addTestResult(containerId, '❌ UI Manager未初始化', 'error');
                return;
            }
            
            try {
                addTestResult(containerId, '📱 检查屏幕注册情况...', 'info');
                
                const expectedScreens = [
                    'loading-screen',
                    'main-menu-screen', 
                    'game-screen',
                    'levelSelectScreen',
                    'settings-screen'
                ];
                
                expectedScreens.forEach(screenId => {
                    const screen = testUIManager.screens.get(screenId);
                    if (screen) {
                        addTestResult(containerId, `✅ 屏幕已注册: ${screenId}`, 'success');
                    } else {
                        addTestResult(containerId, `❌ 屏幕未注册: ${screenId}`, 'error');
                    }
                });
                
            } catch (error) {
                addTestResult(containerId, `❌ 屏幕注册测试失败: ${error.message}`, 'error');
                console.error('屏幕注册测试错误:', error);
            }
        }
        
        // 测试显示屏幕
        async function testShowScreen(screenName) {
            const containerId = 'screen-switch-results';
            
            if (!testUIManager) {
                addTestResult(containerId, '❌ UI Manager未初始化', 'error');
                return;
            }
            
            try {
                addTestResult(containerId, `🔄 切换到屏幕: ${screenName}`, 'info');
                
                await testUIManager.showScreen(screenName);
                
                // 检查屏幕状态
                const screen = testUIManager.screens.get(screenName);
                if (screen && screen.visible) {
                    addTestResult(containerId, `✅ 屏幕切换成功: ${screenName}`, 'success');
                } else {
                    addTestResult(containerId, `❌ 屏幕切换失败: ${screenName}`, 'error');
                }
                
                // 刷新屏幕状态显示
                refreshScreenStatus();
                
            } catch (error) {
                addTestResult(containerId, `❌ 屏幕切换失败: ${error.message}`, 'error');
                console.error('屏幕切换错误:', error);
            }
        }
        
        // 刷新屏幕状态显示
        function refreshScreenStatus() {
            const preview = document.getElementById('screen-preview');
            
            if (!testUIManager) {
                preview.innerHTML = '<p>UI Manager未初始化</p>';
                return;
            }
            
            let html = '<h3>屏幕状态</h3>';
            
            testUIManager.screens.forEach((screen, screenId) => {
                const element = screen.element;
                const isVisible = screen.visible;
                const display = element ? element.style.display : 'unknown';
                const opacity = element ? element.style.opacity : 'unknown';
                const visibility = element ? element.style.visibility : 'unknown';
                const hasActive = element ? element.classList.contains('active') : false;
                
                html += `
                    <div class="screen-info">
                        <span>${screenId}</span>
                        <div>
                            <span class="screen-status ${isVisible ? 'visible' : 'hidden'}">
                                ${isVisible ? '可见' : '隐藏'}
                            </span>
                            <small>display: ${display}, opacity: ${opacity}, active: ${hasActive}</small>
                        </div>
                    </div>
                `;
            });
            
            preview.innerHTML = html;
        }
        
        // 测试关卡选择初始化
        function testLevelSelectInit() {
            const containerId = 'level-select-results';
            document.getElementById(containerId).innerHTML = '';
            
            try {
                addTestResult(containerId, '🎯 开始测试关卡选择初始化...', 'info');
                
                // 检查LevelSelect类是否存在
                if (typeof LevelSelect === 'undefined') {
                    throw new Error('LevelSelect类未找到');
                }
                addTestResult(containerId, '✅ LevelSelect类已找到', 'success');
                
                // 检查全局实例
                if (window.levelSelect) {
                    addTestResult(containerId, '✅ levelSelect全局实例存在', 'success');
                } else {
                    addTestResult(containerId, '❌ levelSelect全局实例不存在', 'error');
                }
                
                addTestResult(containerId, '✅ 关卡选择初始化测试完成', 'success');
                
            } catch (error) {
                addTestResult(containerId, `❌ 关卡选择初始化测试失败: ${error.message}`, 'error');
                console.error('关卡选择初始化测试错误:', error);
            }
        }
        
        // 测试关卡选择显示
        function testLevelSelectShow() {
            const containerId = 'level-select-results';
            
            if (!window.levelSelect) {
                addTestResult(containerId, '❌ levelSelect实例不存在', 'error');
                return;
            }
            
            try {
                addTestResult(containerId, '🎯 测试关卡选择显示...', 'info');
                
                window.levelSelect.show();
                
                addTestResult(containerId, '✅ 关卡选择显示调用完成', 'success');
                
            } catch (error) {
                addTestResult(containerId, `❌ 关卡选择显示测试失败: ${error.message}`, 'error');
                console.error('关卡选择显示测试错误:', error);
            }
        }
        
        // 检查关卡选择元素
        function testLevelSelectElements() {
            const containerId = 'level-select-results';
            
            const elementsToCheck = [
                { id: 'levelSelectScreen', name: '关卡选择容器' },
                { id: 'levelGrid', name: '关卡网格' },
                { id: 'levelPreview', name: '关卡预览' },
                { id: 'startLevelButton', name: '开始游戏按钮' }
            ];
            
            elementsToCheck.forEach(({ id, name }) => {
                const element = document.getElementById(id);
                if (element) {
                    addTestResult(containerId, `✅ ${name} 存在`, 'success');
                } else {
                    addTestResult(containerId, `❌ ${name} 不存在`, 'error');
                }
            });
        }
        
        // 清空控制台输出
        function clearConsoleOutput() {
            consoleOutput.innerHTML = '';
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupConsoleCapture();
            createTestScreens();
            console.log('🔧 UI Manager修复测试页面已加载');
            
            // 自动运行一些基础测试
            setTimeout(() => {
                testUIManagerInit();
            }, 1000);
        });
    </script>
</body>
</html>
