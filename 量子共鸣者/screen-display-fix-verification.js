/**
 * 量子共鸣者 - 屏幕显示修复验证脚本
 * 用于验证屏幕显示问题的修复效果
 * 创建时间: 2025-07-31
 */

class ScreenDisplayFixVerification {
    constructor() {
        this.testResults = [];
        this.isRunning = false;
    }

    /**
     * 运行所有验证测试
     */
    async runAllTests() {
        if (this.isRunning) {
            console.warn('⚠️ 测试正在运行中...');
            return;
        }

        this.isRunning = true;
        this.testResults = [];
        
        console.log('🧪 开始屏幕显示修复验证测试');
        console.log('='.repeat(50));

        try {
            // 测试1：CSS样式检查
            await this.testCSSStyles();
            
            // 测试2：屏幕元素检查
            await this.testScreenElements();
            
            // 测试3：UI管理器功能测试
            await this.testUIManager();
            
            // 测试4：游戏屏幕显示测试
            await this.testGameScreenDisplay();
            
            // 测试5：屏幕切换测试
            await this.testScreenSwitching();
            
            // 生成测试报告
            return this.generateReport();
            
        } catch (error) {
            console.error('❌ 测试过程中发生错误:', error);
            return { success: false, error: error.message };
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * 测试CSS样式
     */
    async testCSSStyles() {
        console.log('📋 测试1: CSS样式检查');
        
        const gameScreen = document.getElementById('game-screen');
        if (!gameScreen) {
            this.addResult('CSS样式检查', false, '游戏屏幕元素不存在');
            return;
        }

        const computedStyle = window.getComputedStyle(gameScreen);
        const tests = [
            {
                name: 'position属性',
                expected: 'fixed',
                actual: computedStyle.position,
                pass: computedStyle.position === 'fixed'
            },
            {
                name: 'display属性',
                expected: 'flex',
                actual: computedStyle.display,
                pass: computedStyle.display === 'flex'
            },
            {
                name: '初始opacity',
                expected: '0或1',
                actual: computedStyle.opacity,
                pass: ['0', '1'].includes(computedStyle.opacity)
            },
            {
                name: '初始visibility',
                expected: 'hidden或visible',
                actual: computedStyle.visibility,
                pass: ['hidden', 'visible'].includes(computedStyle.visibility)
            }
        ];

        let allPassed = true;
        tests.forEach(test => {
            console.log(`  ${test.pass ? '✅' : '❌'} ${test.name}: ${test.actual} (期望: ${test.expected})`);
            if (!test.pass) allPassed = false;
        });

        this.addResult('CSS样式检查', allPassed, allPassed ? '所有样式属性正确' : '部分样式属性不正确');
    }

    /**
     * 测试屏幕元素
     */
    async testScreenElements() {
        console.log('📋 测试2: 屏幕元素检查');
        
        const screens = document.querySelectorAll('.screen');
        const gameScreen = document.getElementById('game-screen');
        
        const tests = [
            {
                name: '屏幕元素数量',
                pass: screens.length > 0,
                message: `找到 ${screens.length} 个屏幕元素`
            },
            {
                name: '游戏屏幕存在',
                pass: !!gameScreen,
                message: gameScreen ? '游戏屏幕元素存在' : '游戏屏幕元素不存在'
            },
            {
                name: '游戏屏幕有screen类',
                pass: gameScreen && gameScreen.classList.contains('screen'),
                message: gameScreen ? `游戏屏幕类列表: ${gameScreen.className}` : '游戏屏幕不存在'
            }
        ];

        let allPassed = true;
        tests.forEach(test => {
            console.log(`  ${test.pass ? '✅' : '❌'} ${test.name}: ${test.message}`);
            if (!test.pass) allPassed = false;
        });

        this.addResult('屏幕元素检查', allPassed, allPassed ? '所有屏幕元素正常' : '屏幕元素存在问题');
    }

    /**
     * 测试UI管理器
     */
    async testUIManager() {
        console.log('📋 测试3: UI管理器功能测试');
        
        const hasUIManager = typeof window.UIManager !== 'undefined';
        const uiManagerInstance = window.uiManager;
        
        const tests = [
            {
                name: 'UIManager类存在',
                pass: hasUIManager,
                message: hasUIManager ? 'UIManager类已定义' : 'UIManager类未定义'
            },
            {
                name: 'UI管理器实例',
                pass: !!uiManagerInstance,
                message: uiManagerInstance ? 'UI管理器实例存在' : 'UI管理器实例不存在'
            },
            {
                name: 'showScreen方法',
                pass: uiManagerInstance && typeof uiManagerInstance.showScreen === 'function',
                message: (uiManagerInstance && typeof uiManagerInstance.showScreen === 'function') ? 
                        'showScreen方法存在' : 'showScreen方法不存在'
            }
        ];

        let allPassed = true;
        tests.forEach(test => {
            console.log(`  ${test.pass ? '✅' : '❌'} ${test.name}: ${test.message}`);
            if (!test.pass) allPassed = false;
        });

        this.addResult('UI管理器测试', allPassed, allPassed ? 'UI管理器功能正常' : 'UI管理器存在问题');
    }

    /**
     * 测试游戏屏幕显示
     */
    async testGameScreenDisplay() {
        console.log('📋 测试4: 游戏屏幕显示测试');
        
        const gameScreen = document.getElementById('game-screen');
        if (!gameScreen) {
            this.addResult('游戏屏幕显示测试', false, '游戏屏幕元素不存在');
            return;
        }

        // 先隐藏所有屏幕
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });

        console.log('  🎬 尝试显示游戏屏幕...');
        
        // 使用修复后的显示逻辑
        gameScreen.classList.add('active');
        gameScreen.style.opacity = '1';
        gameScreen.style.visibility = 'visible';
        gameScreen.style.display = 'flex';
        gameScreen.style.position = 'fixed';
        gameScreen.style.zIndex = '10';

        // 等待样式应用
        await new Promise(resolve => setTimeout(resolve, 300));

        // 检查显示结果
        const computedStyle = window.getComputedStyle(gameScreen);
        const isVisible = computedStyle.opacity === '1' && computedStyle.visibility === 'visible';
        const hasActiveClass = gameScreen.classList.contains('active');

        console.log(`  📊 显示结果:`);
        console.log(`    - opacity: ${computedStyle.opacity}`);
        console.log(`    - visibility: ${computedStyle.visibility}`);
        console.log(`    - display: ${computedStyle.display}`);
        console.log(`    - position: ${computedStyle.position}`);
        console.log(`    - z-index: ${computedStyle.zIndex}`);
        console.log(`    - active类: ${hasActiveClass}`);
        console.log(`    - 整体可见: ${isVisible}`);

        const success = isVisible && hasActiveClass;
        this.addResult('游戏屏幕显示测试', success, 
            success ? '游戏屏幕显示正常' : '游戏屏幕显示异常');
    }

    /**
     * 测试屏幕切换
     */
    async testScreenSwitching() {
        console.log('📋 测试5: 屏幕切换测试');
        
        const gameScreen = document.getElementById('game-screen');
        const mainMenuScreen = document.getElementById('main-menu-screen');
        
        if (!gameScreen || !mainMenuScreen) {
            this.addResult('屏幕切换测试', false, '必要的屏幕元素不存在');
            return;
        }

        console.log('  🔄 测试屏幕切换...');
        
        // 显示主菜单
        this.showScreen(mainMenuScreen);
        await new Promise(resolve => setTimeout(resolve, 200));
        
        const mainMenuVisible = this.isScreenVisible(mainMenuScreen);
        const gameScreenHidden = !this.isScreenVisible(gameScreen);
        
        console.log(`    - 主菜单显示: ${mainMenuVisible}`);
        console.log(`    - 游戏屏幕隐藏: ${gameScreenHidden}`);
        
        // 切换到游戏屏幕
        this.showScreen(gameScreen);
        await new Promise(resolve => setTimeout(resolve, 200));
        
        const gameScreenVisible = this.isScreenVisible(gameScreen);
        const mainMenuHidden = !this.isScreenVisible(mainMenuScreen);
        
        console.log(`    - 游戏屏幕显示: ${gameScreenVisible}`);
        console.log(`    - 主菜单隐藏: ${mainMenuHidden}`);
        
        const success = gameScreenVisible && mainMenuHidden;
        this.addResult('屏幕切换测试', success, 
            success ? '屏幕切换功能正常' : '屏幕切换功能异常');
    }

    /**
     * 显示屏幕的辅助方法
     */
    showScreen(screen) {
        // 隐藏所有屏幕
        document.querySelectorAll('.screen').forEach(s => {
            s.classList.remove('active');
        });
        
        // 显示目标屏幕
        screen.classList.add('active');
        screen.style.opacity = '1';
        screen.style.visibility = 'visible';
        screen.style.display = 'flex';
        screen.style.zIndex = '10';
    }

    /**
     * 检查屏幕是否可见
     */
    isScreenVisible(screen) {
        const computedStyle = window.getComputedStyle(screen);
        return computedStyle.opacity === '1' && 
               computedStyle.visibility === 'visible' && 
               screen.classList.contains('active');
    }

    /**
     * 添加测试结果
     */
    addResult(testName, passed, message) {
        this.testResults.push({
            name: testName,
            passed,
            message,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 生成测试报告
     */
    generateReport() {
        console.log('='.repeat(50));
        console.log('📊 屏幕显示修复验证报告');
        console.log('='.repeat(50));

        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;

        console.log(`总测试数: ${totalTests}`);
        console.log(`通过: ${passedTests} ✅`);
        console.log(`失败: ${failedTests} ❌`);
        console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
        console.log('');

        console.log('详细结果:');
        this.testResults.forEach((result, index) => {
            console.log(`${index + 1}. ${result.passed ? '✅' : '❌'} ${result.name}`);
            console.log(`   ${result.message}`);
        });

        console.log('='.repeat(50));
        
        const success = failedTests === 0;
        if (success) {
            console.log('🎉 所有测试通过！屏幕显示修复成功！');
        } else {
            console.log('⚠️ 部分测试失败，需要进一步修复');
        }

        return {
            success,
            total: totalTests,
            passed: passedTests,
            failed: failedTests,
            successRate: (passedTests / totalTests) * 100,
            results: this.testResults
        };
    }
}

// 创建全局实例
window.screenDisplayFixVerification = new ScreenDisplayFixVerification();

// 导出验证函数
window.runScreenDisplayFixVerification = () => {
    return window.screenDisplayFixVerification.runAllTests();
};

console.log('🔧 屏幕显示修复验证脚本已加载');
console.log('💡 使用 runScreenDisplayFixVerification() 开始验证测试');
