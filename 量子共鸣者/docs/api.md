# 量子共鸣者 API 文档

## 概述

本文档描述了量子共鸣者游戏的核心API接口，包括各个系统模块的公共方法和事件接口。

## 核心系统 API

### 存储服务 (StorageService)

存储服务提供了多层存储架构，支持IndexedDB、localStorage和内存存储。

#### 方法

```javascript
// 初始化存储服务
storageService.init()

// 存储数据
storageService.put(key, value)

// 获取数据
const data = storageService.get(key, defaultValue)

// 删除数据
storageService.remove(key)

// 清空所有数据
storageService.clear()

// 获取所有键
const keys = storageService.keys()

// 检查键是否存在
const exists = storageService.has(key)

// 获取存储大小
const size = storageService.size()

// 专用方法
storageService.putPlayerData(playerId, data)
const playerData = storageService.getPlayerData(playerId)
storageService.putLevelData(levelId, data)
const levelData = storageService.getLevelData(levelId)
```

#### 事件

```javascript
// 监听存储变化
storageService.on('change', (key, newValue, oldValue) => {
    console.log(`存储键 ${key} 发生变化`);
});

// 监听存储错误
storageService.on('error', (error) => {
    console.error('存储错误:', error);
});
```

### 国际化服务 (I18nService)

提供多语言支持和文本本地化功能。

#### 方法

```javascript
// 初始化国际化服务
i18n.init()

// 设置语言
i18n.setLanguage('zh') // 'zh' 或 'en'

// 获取当前语言
const lang = i18n.getCurrentLanguage()

// 翻译文本
const text = i18n.t('menu.start') // 获取翻译文本

// 翻译带参数的文本
const text = i18n.t('game.score', { score: 1000 })

// 检查翻译是否存在
const exists = i18n.has('menu.start')

// 获取所有支持的语言
const languages = i18n.getSupportedLanguages()
```

#### 事件

```javascript
// 监听语言变化
i18n.on('languageChanged', (newLang, oldLang) => {
    console.log(`语言从 ${oldLang} 切换到 ${newLang}`);
});
```

### 音频引擎 (AudioEngine)

处理所有音频相关功能，包括音效播放、音乐合成和音频分析。

#### 方法

```javascript
// 初始化音频引擎
audioEngine.init()

// 播放音调
audioEngine.playTone(frequency, duration, volume)

// 播放音效
audioEngine.playSound(soundId, volume)

// 开始/停止音频分析
audioEngine.startAnalysis()
audioEngine.stopAnalysis()

// 获取频率数据
const frequencyData = audioEngine.getFrequencyData()

// 获取时域数据
const timeData = audioEngine.getTimeData()

// 设置主音量
audioEngine.setMasterVolume(volume)

// 获取音频上下文
const context = audioEngine.getAudioContext()

// 创建振荡器
const oscillator = audioEngine.createOscillator(type, frequency)

// 创建音频缓冲区
const buffer = audioEngine.createBuffer(channels, length, sampleRate)

// 麦克风相关
audioEngine.requestMicrophone()
audioEngine.startMicrophoneAnalysis()
audioEngine.stopMicrophoneAnalysis()
const micFrequency = audioEngine.getMicrophoneFrequency()
```

#### 事件

```javascript
// 音频初始化完成
audioEngine.on('initialized', () => {
    console.log('音频引擎初始化完成');
});

// 麦克风权限获取
audioEngine.on('microphoneGranted', () => {
    console.log('麦克风权限已获取');
});

// 频率检测
audioEngine.on('frequencyDetected', (frequency) => {
    console.log('检测到频率:', frequency);
});
```

### 渲染引擎 (RenderEngine)

负责所有图形渲染，包括2D Canvas和3D WebGL渲染。

#### 方法

```javascript
// 初始化渲染引擎
renderEngine.init(canvasId)

// 渲染一帧
renderEngine.render(alpha)

// 设置相机位置
renderEngine.setCamera(x, y, zoom)

// 获取相机信息
const camera = renderEngine.getCamera()

// 屏幕坐标转世界坐标
const worldPos = renderEngine.screenToWorld(screenX, screenY)

// 世界坐标转屏幕坐标
const screenPos = renderEngine.worldToScreen(worldX, worldY)

// 添加渲染对象
renderEngine.addRenderable(object)

// 移除渲染对象
renderEngine.removeRenderable(object)

// 清空画布
renderEngine.clear()

// 设置背景色
renderEngine.setBackgroundColor(color)

// 绘制粒子
renderEngine.drawParticle(particle)

// 绘制连接线
renderEngine.drawConnection(from, to, strength)

// 绘制UI元素
renderEngine.drawUI(element)

// 3D相关方法
renderEngine.init3D()
renderEngine.render3D(scene)
renderEngine.setCamera3D(position, target, up)
```

#### 事件

```javascript
// 渲染引擎初始化完成
renderEngine.on('initialized', () => {
    console.log('渲染引擎初始化完成');
});

// 渲染帧完成
renderEngine.on('frameRendered', (frameTime) => {
    console.log('帧渲染完成，耗时:', frameTime);
});
```

### 量子引擎 (QuantumEngine)

实现量子物理模拟和粒子系统。

#### 方法

```javascript
// 初始化量子引擎
quantumEngine.init()

// 更新物理模拟
quantumEngine.update(deltaTime)

// 添加粒子
const particle = quantumEngine.addParticle(x, y, frequency, energy)

// 移除粒子
quantumEngine.removeParticle(particleId)

// 获取所有粒子
const particles = quantumEngine.getParticles()

// 激活粒子
quantumEngine.activateParticle(particleId, frequency)

// 计算共鸣强度
const resonance = quantumEngine.calculateResonance(freq1, freq2)

// 传播能量波
quantumEngine.propagateEnergyWave(sourceParticle, energy)

// 检测连锁反应
const chainReaction = quantumEngine.detectChainReaction()

// 设置量子场参数
quantumEngine.setQuantumField(strength, phase)

// 获取量子场状态
const field = quantumEngine.getQuantumField()

// 空间查询
const nearbyParticles = quantumEngine.findNearbyParticles(x, y, radius)

// 碰撞检测
const collisions = quantumEngine.detectCollisions()
```

#### 事件

```javascript
// 粒子激活
quantumEngine.on('particleActivated', (particle) => {
    console.log('粒子激活:', particle.id);
});

// 共鸣发生
quantumEngine.on('resonanceOccurred', (particle1, particle2, strength) => {
    console.log('共鸣发生:', particle1.id, particle2.id, strength);
});

// 连锁反应
quantumEngine.on('chainReaction', (particles, totalEnergy) => {
    console.log('连锁反应:', particles.length, totalEnergy);
});
```

### 游戏控制器 (GameController)

管理游戏状态、关卡进度和用户交互。

#### 方法

```javascript
// 初始化游戏控制器
gameController.init()

// 开始游戏
gameController.startGame(levelId)

// 暂停/恢复游戏
gameController.pauseGame()
gameController.resumeGame()

// 结束游戏
gameController.endGame()

// 重启关卡
gameController.restartLevel()

// 获取游戏状态
const state = gameController.getGameState()

// 获取当前关卡
const level = gameController.getCurrentLevel()

// 获取游戏统计
const stats = gameController.getGameStats()

// 更新分数
gameController.updateScore(points)

// 处理用户输入
gameController.handleInput(inputType, data)

// 设置游戏参数
gameController.setGameSettings(settings)

// 获取游戏参数
const settings = gameController.getGameSettings()
```

#### 事件

```javascript
// 游戏开始
gameController.on('gameStarted', (level) => {
    console.log('游戏开始:', level.name);
});

// 游戏结束
gameController.on('gameEnded', (result) => {
    console.log('游戏结束:', result);
});

// 分数更新
gameController.on('scoreUpdated', (newScore, oldScore) => {
    console.log('分数更新:', newScore);
});

// 关卡完成
gameController.on('levelCompleted', (level, stats) => {
    console.log('关卡完成:', level.name, stats);
});
```

### 关卡管理器 (LevelManager)

管理游戏关卡、关卡编辑和自定义关卡。

#### 方法

```javascript
// 初始化关卡管理器
levelManager.init()

// 加载关卡
const level = levelManager.loadLevel(levelId)

// 保存关卡
levelManager.saveLevel(level)

// 获取所有关卡
const levels = levelManager.getAllLevels()

// 获取内置关卡
const builtInLevels = levelManager.getBuiltInLevels()

// 获取自定义关卡
const customLevels = levelManager.getCustomLevels()

// 验证关卡
const validation = levelManager.validateLevel(level)

// 创建新关卡
const newLevel = levelManager.createLevel(name, description)

// 删除关卡
levelManager.deleteLevel(levelId)

// 导入关卡
levelManager.importLevel(levelData)

// 导出关卡
const levelData = levelManager.exportLevel(levelId)

// 获取关卡进度
const progress = levelManager.getLevelProgress(levelId)

// 更新关卡进度
levelManager.updateLevelProgress(levelId, progress)
```

#### 事件

```javascript
// 关卡加载完成
levelManager.on('levelLoaded', (level) => {
    console.log('关卡加载完成:', level.name);
});

// 关卡保存完成
levelManager.on('levelSaved', (level) => {
    console.log('关卡保存完成:', level.name);
});
```

### 玩家管理器 (PlayerManager)

管理玩家数据、成就系统和排行榜。

#### 方法

```javascript
// 初始化玩家管理器
playerManager.init()

// 创建玩家
const player = playerManager.createPlayer(name, avatar)

// 获取当前玩家
const player = playerManager.getCurrentPlayer()

// 设置当前玩家
playerManager.setCurrentPlayer(playerId)

// 更新玩家统计
playerManager.updatePlayerStats(stats)

// 更新关卡进度
playerManager.updateLevelProgress(levelId, progress)

// 检查成就
const newAchievements = playerManager.checkAchievements()

// 获取玩家成就
const achievements = playerManager.getPlayerAchievements(playerId)

// 更新排行榜
playerManager.updateLeaderboard(entry)

// 获取排行榜
const leaderboard = playerManager.getLeaderboard(category, limit)

// 保存玩家数据
playerManager.savePlayerData()

// 加载玩家数据
playerManager.loadPlayerData()

// 获取玩家统计
const stats = playerManager.getPlayerStats(playerId)
```

#### 事件

```javascript
// 成就解锁
playerManager.on('achievementUnlocked', (achievement) => {
    console.log('成就解锁:', achievement.name);
});

// 排行榜更新
playerManager.on('leaderboardUpdated', (category, entry) => {
    console.log('排行榜更新:', category, entry);
});
```

## UI 系统 API

### UI管理器 (UIManager)

管理用户界面和屏幕切换。

#### 方法

```javascript
// 初始化UI管理器
uiManager.init()

// 显示屏幕
uiManager.showScreen(screenId)

// 隐藏屏幕
uiManager.hideScreen(screenId)

// 显示模态框
uiManager.showModal(modalId, data)

// 隐藏模态框
uiManager.hideModal(modalId)

// 显示通知
uiManager.showToast(message, type, duration)

// 更新UI元素
uiManager.updateElement(elementId, data)

// 注册UI组件
uiManager.registerComponent(componentId, component)

// 获取UI组件
const component = uiManager.getComponent(componentId)
```

#### 事件

```javascript
// 屏幕切换
uiManager.on('screenChanged', (newScreen, oldScreen) => {
    console.log('屏幕切换:', newScreen);
});

// 模态框显示/隐藏
uiManager.on('modalShown', (modalId) => {
    console.log('模态框显示:', modalId);
});
```

## 工具类 API

### 数学工具 (MathUtils)

提供游戏中使用的数学计算函数。

#### 方法

```javascript
// 距离计算
const distance = MathUtils.distance(x1, y1, x2, y2)

// 角度计算
const angle = MathUtils.angle(x1, y1, x2, y2)

// 向量运算
const vector = MathUtils.normalize(x, y)
const dot = MathUtils.dotProduct(v1, v2)
const cross = MathUtils.crossProduct(v1, v2)

// 插值
const value = MathUtils.lerp(start, end, t)
const smoothValue = MathUtils.smoothstep(start, end, t)

// 随机数
const random = MathUtils.random(min, max)
const randomInt = MathUtils.randomInt(min, max)

// 频率相关
const frequency = MathUtils.noteToFrequency(note)
const note = MathUtils.frequencyToNote(frequency)
const cents = MathUtils.frequencyToCents(freq1, freq2)

// 物理计算
const resonance = MathUtils.calculateResonance(freq1, freq2, tolerance)
const harmonic = MathUtils.getHarmonic(fundamental, n)
```

## 事件系统

所有系统都支持事件监听机制：

```javascript
// 添加事件监听器
system.on('eventName', callback)

// 移除事件监听器
system.off('eventName', callback)

// 触发事件
system.emit('eventName', data)

// 一次性事件监听
system.once('eventName', callback)
```

## 错误处理

所有API调用都应该包含适当的错误处理：

```javascript
try {
    const result = system.someMethod(params);
    // 处理成功结果
} catch (error) {
    console.error('API调用失败:', error);
    // 处理错误情况
}
```

## 性能优化建议

1. **批量操作**：尽可能使用批量API而不是单个操作
2. **事件节流**：对高频事件使用节流或防抖
3. **内存管理**：及时清理不需要的对象和事件监听器
4. **异步操作**：使用Promise或async/await处理异步操作

## 版本兼容性

当前API版本：1.0.0

所有API都向后兼容，新版本会在文档中标明版本要求。
