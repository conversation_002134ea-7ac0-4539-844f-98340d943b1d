# 量子共鸣者 - 开发者贡献指南

## 欢迎贡献！

感谢您对量子共鸣者项目的兴趣！我们欢迎各种形式的贡献，包括但不限于：

- 🐛 错误报告和修复
- ✨ 新功能开发
- 📚 文档改进
- 🎨 UI/UX优化
- 🎵 音频内容创作
- 🎮 关卡设计
- 🌐 国际化翻译
- 🧪 测试用例编写

## 开发环境搭建

### 前置要求

- **Node.js** 16+ (用于开发工具)
- **Git** (版本控制)
- **现代浏览器** (Chrome 80+, Firefox 75+)
- **代码编辑器** (推荐 VS Code)

### 克隆项目

```bash
git clone <repository-url>
cd 量子共鸣者
```

### 安装开发依赖

```bash
# 安装开发工具
npm install -g http-server live-server

# 或使用yarn
yarn global add http-server live-server
```

### 启动开发服务器

```bash
# 使用http-server
http-server -p 8080

# 使用live-server (支持热重载)
live-server --port=8080

# 使用Python
python -m http.server 8080
```

### 推荐的VS Code扩展

- **Live Server** - 实时预览
- **Prettier** - 代码格式化
- **ESLint** - 代码检查
- **GitLens** - Git增强
- **Thunder Client** - API测试
- **Auto Rename Tag** - HTML标签重命名

## 项目结构

```
量子共鸣者/
├── index.html                 # 主页面
├── styles/                    # 样式文件
│   ├── main.css              # 主样式
│   ├── ui-components.css     # UI组件样式
│   └── responsive.css        # 响应式样式
├── js/                       # JavaScript模块
│   ├── utils/                # 工具模块
│   │   ├── storage.js        # 存储服务
│   │   ├── i18n.js          # 国际化
│   │   └── math-utils.js     # 数学工具
│   ├── core/                 # 核心系统
│   │   ├── audio-engine.js   # 音频引擎
│   │   ├── physics-engine.js # 物理引擎
│   │   ├── quantum-engine.js # 量子引擎
│   │   └── render-engine.js  # 渲染引擎
│   ├── game/                 # 游戏逻辑
│   │   ├── game-controller.js # 游戏控制器
│   │   ├── level-manager.js  # 关卡管理
│   │   └── player-manager.js # 玩家管理
│   ├── ui/                   # 用户界面
│   │   ├── ui-manager.js     # UI管理器
│   │   ├── achievements.js   # 成就系统
│   │   └── leaderboard.js    # 排行榜
│   ├── audio/                # 音频系统
│   ├── render/               # 渲染系统
│   └── main.js               # 主入口
├── assets/                   # 资源文件
├── levels/                   # 关卡数据
├── docs/                     # 文档
├── tests/                    # 测试文件
└── README.md                 # 项目说明
```

## 代码规范

### JavaScript规范

我们遵循以下JavaScript编码规范：

```javascript
// 使用const和let，避免var
const CONSTANT_VALUE = 100;
let variableValue = 0;

// 使用驼峰命名法
const audioEngine = new AudioEngine();
const playerManager = new PlayerManager();

// 类名使用帕斯卡命名法
class QuantumEngine {
    constructor() {
        this.particles = [];
    }
    
    // 方法使用驼峰命名法
    addParticle(x, y, frequency) {
        // 实现代码
    }
}

// 常量使用大写下划线
const MAX_PARTICLES = 1000;
const DEFAULT_FREQUENCY = 440;

// 使用模板字符串
const message = `粒子频率: ${frequency}Hz`;

// 使用箭头函数（适当时）
const processParticles = (particles) => {
    return particles.filter(p => p.active);
};

// 使用解构赋值
const { x, y, frequency } = particle;
const [first, second] = coordinates;
```

### CSS规范

```css
/* 使用BEM命名法 */
.quantum-particle {
    position: absolute;
    border-radius: 50%;
}

.quantum-particle--active {
    box-shadow: 0 0 20px #00ffff;
}

.quantum-particle__energy {
    opacity: 0.8;
}

/* 使用CSS自定义属性 */
:root {
    --primary-color: #1a1a2e;
    --secondary-color: #16213e;
    --accent-color: #00ffff;
    --text-color: #ffffff;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .quantum-particle {
        transform: scale(0.8);
    }
}
```

### HTML规范

```html
<!-- 使用语义化标签 -->
<main class="game-container">
    <section class="game-area">
        <canvas id="gameCanvas" class="game-canvas"></canvas>
    </section>
    
    <aside class="control-panel">
        <button class="btn btn--primary" id="startBtn">
            开始游戏
        </button>
    </aside>
</main>

<!-- 使用适当的属性 -->
<button 
    class="btn btn--primary" 
    id="playBtn"
    aria-label="播放音效"
    data-frequency="440">
    播放
</button>
```

### 注释规范

```javascript
/**
 * 量子粒子类
 * 表示游戏中的一个量子粒子，具有位置、频率和能量属性
 */
class QuantumParticle {
    /**
     * 创建量子粒子
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} frequency - 粒子频率 (Hz)
     * @param {number} energy - 粒子能量 (0-1)
     */
    constructor(x, y, frequency, energy = 1.0) {
        this.x = x;
        this.y = y;
        this.frequency = frequency;
        this.energy = energy;
        this.active = false;
    }
    
    /**
     * 激活粒子
     * 当粒子被激活时，会开始发光并影响周围粒子
     */
    activate() {
        this.active = true;
        // TODO: 添加激活动画
    }
}
```

## 提交规范

### Git提交信息格式

我们使用约定式提交格式：

```
<类型>[可选的作用域]: <描述>

[可选的正文]

[可选的脚注]
```

#### 提交类型

- **feat**: 新功能
- **fix**: 错误修复
- **docs**: 文档更新
- **style**: 代码格式化（不影响功能）
- **refactor**: 代码重构
- **perf**: 性能优化
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动

#### 示例

```bash
# 新功能
git commit -m "feat(audio): 添加麦克风频率检测功能"

# 错误修复
git commit -m "fix(render): 修复粒子渲染闪烁问题"

# 文档更新
git commit -m "docs: 更新API文档中的音频引擎部分"

# 性能优化
git commit -m "perf(physics): 优化碰撞检测算法性能"
```

### 分支管理

我们使用Git Flow工作流：

- **main**: 主分支，包含稳定的发布版本
- **develop**: 开发分支，包含最新的开发代码
- **feature/**: 功能分支，用于开发新功能
- **hotfix/**: 热修复分支，用于紧急修复
- **release/**: 发布分支，用于准备新版本发布

#### 工作流程

```bash
# 1. 从develop创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/new-particle-system

# 2. 开发功能
# ... 编写代码 ...

# 3. 提交更改
git add .
git commit -m "feat(physics): 实现新的粒子系统"

# 4. 推送分支
git push origin feature/new-particle-system

# 5. 创建Pull Request
# 在GitHub/GitLab上创建PR，请求合并到develop分支
```

## 测试指南

### 运行测试

```bash
# 在浏览器中打开测试页面
open http://localhost:8080/test-systems.js

# 或在控制台中运行测试
runSystemTests()
runCompatibilityTest()
runGameFunctionalityTest()
```

### 编写测试

```javascript
// 示例：为新功能编写测试
function testNewFeature() {
    console.log('测试新功能...');
    
    // 准备测试数据
    const testData = { x: 100, y: 100, frequency: 440 };
    
    // 执行功能
    const result = newFeature(testData);
    
    // 验证结果
    if (result.success) {
        console.log('✅ 新功能测试通过');
        return true;
    } else {
        console.log('❌ 新功能测试失败:', result.error);
        return false;
    }
}
```

### 性能测试

```javascript
// 性能测试示例
function performanceTest() {
    const startTime = performance.now();
    
    // 执行需要测试的代码
    for (let i = 0; i < 1000; i++) {
        quantumEngine.update(16.67); // 60 FPS
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    console.log(`性能测试完成，耗时: ${duration.toFixed(2)}ms`);
    
    // 性能基准：1000次更新应在100ms内完成
    return duration < 100;
}
```

## 问题报告

### 报告Bug

在提交Bug报告时，请包含以下信息：

1. **问题描述**: 清楚地描述遇到的问题
2. **重现步骤**: 详细的重现步骤
3. **预期行为**: 描述期望的正确行为
4. **实际行为**: 描述实际发生的情况
5. **环境信息**: 浏览器版本、操作系统等
6. **截图/录屏**: 如果可能，提供视觉证据

#### Bug报告模板

```markdown
## Bug描述
简要描述遇到的问题

## 重现步骤
1. 打开游戏
2. 点击开始按钮
3. 选择第一关
4. 点击粒子

## 预期行为
粒子应该发光并播放音效

## 实际行为
粒子没有反应，控制台显示错误

## 环境信息
- 浏览器: Chrome 91.0.4472.124
- 操作系统: Windows 10
- 设备: 桌面电脑

## 附加信息
控制台错误信息：
```
TypeError: Cannot read property 'frequency' of undefined
```

### 功能请求

提交功能请求时，请说明：

1. **功能描述**: 详细描述建议的功能
2. **使用场景**: 说明为什么需要这个功能
3. **实现建议**: 如果有想法，可以提供实现建议
4. **优先级**: 说明功能的重要性

## 代码审查

### 审查清单

在提交代码前，请确保：

- [ ] 代码遵循项目规范
- [ ] 添加了适当的注释
- [ ] 通过了所有测试
- [ ] 没有引入新的警告或错误
- [ ] 更新了相关文档
- [ ] 考虑了性能影响
- [ ] 考虑了兼容性问题

### Pull Request模板

```markdown
## 更改描述
简要描述这个PR的更改内容

## 更改类型
- [ ] Bug修复
- [ ] 新功能
- [ ] 文档更新
- [ ] 性能优化
- [ ] 代码重构

## 测试
- [ ] 添加了新的测试
- [ ] 所有测试通过
- [ ] 手动测试通过

## 检查清单
- [ ] 代码遵循项目规范
- [ ] 添加了适当的注释
- [ ] 更新了相关文档
- [ ] 考虑了向后兼容性

## 截图（如适用）
如果有UI更改，请提供截图

## 相关Issue
关闭 #123
```

## 社区准则

### 行为准则

我们致力于为所有人提供友好、安全和欢迎的环境，无论：

- 经验水平
- 性别认同和表达
- 性取向
- 残疾
- 个人外貌
- 身体大小
- 种族
- 民族
- 年龄
- 宗教
- 国籍

### 预期行为

- 使用友好和包容的语言
- 尊重不同的观点和经验
- 优雅地接受建设性批评
- 关注对社区最有利的事情
- 对其他社区成员表示同理心

### 不当行为

- 使用性化的语言或图像
- 人身攻击或政治攻击
- 公开或私下骚扰
- 未经明确许可发布他人的私人信息
- 其他在专业环境中可能被认为不当的行为

## 获得帮助

如果您需要帮助或有任何问题，可以通过以下方式联系我们：

- **GitHub Issues**: 提交问题或功能请求
- **讨论区**: 参与社区讨论
- **邮件**: 发送邮件到项目维护者

## 致谢

感谢所有为量子共鸣者项目做出贡献的开发者！您的贡献让这个项目变得更好。

---

再次感谢您的贡献！让我们一起创造一个令人惊叹的量子音乐游戏体验！ 🌌🎵
