# 量子共鸣者 - 部署指南

## 概述

本指南详细说明如何在不同环境中部署量子共鸣者游戏，包括本地开发、测试环境和生产环境的部署方案。

## 系统要求

### 服务器要求
- **操作系统**：Linux (推荐 Ubuntu 20.04+), Windows Server, macOS
- **Web服务器**：Nginx (推荐), Apache, IIS
- **内存**：最小 512MB，推荐 2GB+
- **存储**：最小 100MB，推荐 1GB+
- **网络**：支持HTTPS (SSL证书)

### 浏览器兼容性
- **桌面端**：Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **移动端**：iOS Safari 13+, Chrome Mobile 80+, Samsung Internet 12+
- **必需功能**：Web Audio API, Canvas 2D, WebGL (可选), IndexedDB

## 本地开发环境

### 快速启动

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd 量子共鸣者
   ```

2. **启动本地服务器**
   
   使用Python：
   ```bash
   # Python 3
   python -m http.server 8080
   
   # Python 2
   python -m SimpleHTTPServer 8080
   ```
   
   使用Node.js：
   ```bash
   npx http-server -p 8080
   ```
   
   使用PHP：
   ```bash
   php -S localhost:8080
   ```

3. **访问游戏**
   打开浏览器访问 `http://localhost:8080`

### 开发工具推荐

- **代码编辑器**：VS Code, WebStorm, Sublime Text
- **浏览器扩展**：Live Server, Web Developer Tools
- **调试工具**：Chrome DevTools, Firefox Developer Tools
- **性能分析**：Lighthouse, WebPageTest

## 测试环境部署

### 使用Docker

1. **创建Dockerfile**
   ```dockerfile
   FROM nginx:alpine
   
   # 复制游戏文件
   COPY . /usr/share/nginx/html
   
   # 复制nginx配置
   COPY nginx.conf /etc/nginx/nginx.conf
   
   # 暴露端口
   EXPOSE 80
   
   CMD ["nginx", "-g", "daemon off;"]
   ```

2. **创建nginx.conf**
   ```nginx
   events {
       worker_connections 1024;
   }
   
   http {
       include       /etc/nginx/mime.types;
       default_type  application/octet-stream;
       
       gzip on;
       gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
       
       server {
           listen 80;
           server_name localhost;
           root /usr/share/nginx/html;
           index index.html;
           
           # 缓存静态资源
           location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
               expires 1y;
               add_header Cache-Control "public, immutable";
           }
           
           # 安全头
           add_header X-Frame-Options "SAMEORIGIN" always;
           add_header X-Content-Type-Options "nosniff" always;
           add_header X-XSS-Protection "1; mode=block" always;
           
           # 支持单页应用
           location / {
               try_files $uri $uri/ /index.html;
           }
       }
   }
   ```

3. **构建和运行**
   ```bash
   docker build -t quantum-resonance .
   docker run -p 8080:80 quantum-resonance
   ```

### 使用GitHub Pages

1. **准备部署分支**
   ```bash
   git checkout -b gh-pages
   git add .
   git commit -m "Deploy to GitHub Pages"
   git push origin gh-pages
   ```

2. **配置GitHub Pages**
   - 在GitHub仓库设置中启用Pages
   - 选择gh-pages分支作为源
   - 等待部署完成

## 生产环境部署

### Nginx配置

1. **安装Nginx**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install nginx
   
   # CentOS/RHEL
   sudo yum install nginx
   ```

2. **配置站点**
   ```nginx
   server {
       listen 80;
       server_name yourdomain.com www.yourdomain.com;
       return 301 https://$server_name$request_uri;
   }
   
   server {
       listen 443 ssl http2;
       server_name yourdomain.com www.yourdomain.com;
       
       # SSL配置
       ssl_certificate /path/to/certificate.crt;
       ssl_certificate_key /path/to/private.key;
       ssl_protocols TLSv1.2 TLSv1.3;
       ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
       ssl_prefer_server_ciphers off;
       
       # 网站根目录
       root /var/www/quantum-resonance;
       index index.html;
       
       # Gzip压缩
       gzip on;
       gzip_vary on;
       gzip_min_length 1024;
       gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
       
       # 缓存配置
       location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
           expires 1y;
           add_header Cache-Control "public, immutable";
           add_header Vary "Accept-Encoding";
       }
       
       # HTML文件不缓存
       location ~* \.html$ {
           expires -1;
           add_header Cache-Control "no-cache, no-store, must-revalidate";
       }
       
       # 安全头
       add_header X-Frame-Options "SAMEORIGIN" always;
       add_header X-Content-Type-Options "nosniff" always;
       add_header X-XSS-Protection "1; mode=block" always;
       add_header Referrer-Policy "strict-origin-when-cross-origin" always;
       add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'; media-src 'self'; object-src 'none'; frame-src 'none';" always;
       
       # 主页面
       location / {
           try_files $uri $uri/ /index.html;
       }
       
       # API代理（如果需要）
       location /api/ {
           proxy_pass http://backend-server;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
   }
   ```

3. **部署文件**
   ```bash
   # 创建网站目录
   sudo mkdir -p /var/www/quantum-resonance
   
   # 复制文件
   sudo cp -r /path/to/game/* /var/www/quantum-resonance/
   
   # 设置权限
   sudo chown -R www-data:www-data /var/www/quantum-resonance
   sudo chmod -R 755 /var/www/quantum-resonance
   
   # 启用站点
   sudo ln -s /etc/nginx/sites-available/quantum-resonance /etc/nginx/sites-enabled/
   
   # 测试配置
   sudo nginx -t
   
   # 重启Nginx
   sudo systemctl restart nginx
   ```

### SSL证书配置

#### 使用Let's Encrypt

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

#### 使用自签名证书（仅用于测试）

```bash
# 生成私钥
openssl genrsa -out private.key 2048

# 生成证书
openssl req -new -x509 -key private.key -out certificate.crt -days 365
```

### CDN配置

#### 使用Cloudflare

1. **添加域名到Cloudflare**
2. **配置DNS记录**
3. **启用以下功能**：
   - Auto Minify (JS, CSS, HTML)
   - Brotli Compression
   - Browser Cache TTL
   - Always Use HTTPS

#### 使用AWS CloudFront

1. **创建S3存储桶**
   ```bash
   aws s3 mb s3://quantum-resonance-static
   aws s3 sync . s3://quantum-resonance-static --delete
   ```

2. **创建CloudFront分发**
   - Origin: S3存储桶
   - Viewer Protocol Policy: Redirect HTTP to HTTPS
   - Compress Objects Automatically: Yes
   - Price Class: Use All Edge Locations

## 性能优化

### 文件压缩

1. **JavaScript压缩**
   ```bash
   # 使用UglifyJS
   npm install -g uglify-js
   uglifyjs js/app.js -o js/app.min.js -c -m
   
   # 使用Terser
   npm install -g terser
   terser js/app.js -o js/app.min.js -c -m
   ```

2. **CSS压缩**
   ```bash
   # 使用clean-css
   npm install -g clean-css-cli
   cleancss -o styles/main.min.css styles/main.css
   ```

3. **图片优化**
   ```bash
   # 使用ImageOptim
   imageoptim assets/images/*
   
   # 使用TinyPNG API
   tinypng assets/images/*.png
   ```

### 缓存策略

1. **浏览器缓存**
   - 静态资源：1年
   - HTML文件：不缓存
   - API响应：根据需要设置

2. **Service Worker缓存**
   ```javascript
   // 在sw.js中实现
   const CACHE_NAME = 'quantum-resonance-v1';
   const urlsToCache = [
       '/',
       '/styles/main.css',
       '/js/app.js',
       '/assets/images/logo.png'
   ];
   ```

### 监控和分析

1. **性能监控**
   - Google Analytics
   - Google PageSpeed Insights
   - WebPageTest
   - Lighthouse CI

2. **错误监控**
   - Sentry
   - LogRocket
   - Bugsnag

3. **用户分析**
   - Google Analytics
   - Mixpanel
   - Amplitude

## 安全配置

### 内容安全策略 (CSP)

```html
<meta http-equiv="Content-Security-Policy" content="
    default-src 'self';
    script-src 'self' 'unsafe-inline';
    style-src 'self' 'unsafe-inline';
    img-src 'self' data:;
    font-src 'self';
    connect-src 'self';
    media-src 'self';
    object-src 'none';
    frame-src 'none';
">
```

### 其他安全头

```nginx
# 防止点击劫持
add_header X-Frame-Options "SAMEORIGIN";

# 防止MIME类型嗅探
add_header X-Content-Type-Options "nosniff";

# XSS保护
add_header X-XSS-Protection "1; mode=block";

# 引用策略
add_header Referrer-Policy "strict-origin-when-cross-origin";

# HSTS
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
```

## 备份和恢复

### 自动备份脚本

```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/quantum-resonance"
SITE_DIR="/var/www/quantum-resonance"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份网站文件
tar -czf $BACKUP_DIR/site_$DATE.tar.gz -C $SITE_DIR .

# 保留最近30天的备份
find $BACKUP_DIR -name "site_*.tar.gz" -mtime +30 -delete

echo "备份完成: site_$DATE.tar.gz"
```

### 恢复流程

```bash
# 停止服务
sudo systemctl stop nginx

# 恢复文件
sudo tar -xzf /backup/quantum-resonance/site_20231201_120000.tar.gz -C /var/www/quantum-resonance

# 设置权限
sudo chown -R www-data:www-data /var/www/quantum-resonance

# 启动服务
sudo systemctl start nginx
```

## 故障排除

### 常见问题

1. **音频不工作**
   - 检查HTTPS配置
   - 验证Web Audio API支持
   - 检查浏览器权限

2. **WebGL渲染问题**
   - 检查显卡驱动
   - 验证WebGL支持
   - 启用硬件加速

3. **存储问题**
   - 检查IndexedDB支持
   - 验证存储配额
   - 清理浏览器缓存

### 日志分析

```bash
# Nginx访问日志
sudo tail -f /var/log/nginx/access.log

# Nginx错误日志
sudo tail -f /var/log/nginx/error.log

# 系统日志
sudo journalctl -u nginx -f
```

## 维护计划

### 定期任务

1. **每日**：检查系统状态、备份数据
2. **每周**：更新安全补丁、分析性能报告
3. **每月**：检查SSL证书、清理日志文件
4. **每季度**：性能优化、安全审计

### 更新流程

1. **测试环境验证**
2. **创建备份**
3. **部署更新**
4. **功能测试**
5. **性能监控**
6. **回滚准备**

## 部署检查清单

### 部署前检查
- [ ] 代码已通过所有测试
- [ ] 静态资源已优化压缩
- [ ] SSL证书已配置
- [ ] 安全头已设置
- [ ] 缓存策略已配置
- [ ] 监控系统已部署
- [ ] 备份系统已设置

### 部署后验证
- [ ] 网站可正常访问
- [ ] HTTPS重定向正常
- [ ] 音频功能正常
- [ ] 3D渲染正常
- [ ] 移动端适配正常
- [ ] 性能指标达标
- [ ] 错误监控正常

---

通过遵循本部署指南，您可以成功地在各种环境中部署量子共鸣者游戏，确保最佳的性能和用户体验。
