# 量子共鸣者 - 音频系统文档

## 概述

量子共鸣者的音频系统是一个基于Web Audio API的高级音频处理框架，专为量子主题的音乐节拍游戏设计。系统提供了完整的音频合成、效果处理、可视化和管理功能。

## 系统架构

### 核心组件

```
AudioManager (音频管理器)
├── AudioEngine (音频引擎) - 基础Web Audio API封装
├── AudioSynthesizer (合成器) - 音频合成和声音生成
├── AudioEffect (效果器) - 音频效果处理
├── AudioVisualizer (可视化器) - 实时音频可视化
└── AudioSequencer (序列器) - 音乐序列和节拍管理
```

### 模块说明

#### 1. AudioEngine (js/core/audio-engine.js)
- **功能**: Web Audio API的基础封装
- **特性**:
  - 音频上下文管理
  - 音频节点创建和连接
  - 麦克风输入支持
  - 音频分析器
  - 音量控制系统

#### 2. AudioSynthesizer (js/audio/synthesizer.js)
- **功能**: 音频合成和声音生成
- **特性**:
  - ADSR包络控制
  - 多种波形支持 (sine, square, sawtooth, triangle)
  - 量子主题音效生成
  - 和声生成
  - 环境音效

#### 3. AudioEffect (js/audio/effects.js)
- **功能**: 音频效果处理
- **支持的效果**:
  - 混响 (Reverb) - 空间感增强
  - 延迟 (Delay) - 回声效果
  - 失真 (Distortion) - 波形变形
  - 合唱 (Chorus) - 声音加厚
  - 滤波器 (Filter) - 频率过滤
  - 压缩器 (Compressor) - 动态范围控制
  - 位压缩 (Bitcrusher) - 数字失真

#### 4. AudioVisualizer (js/audio/visualizer.js)
- **功能**: 实时音频可视化
- **可视化类型**:
  - 频谱条形图
  - 波形显示
  - 粒子系统
  - 频率圆环
  - 量子主题效果

#### 5. AudioManager (js/audio/audio-manager.js)
- **功能**: 统一音频系统管理
- **特性**:
  - 模块协调
  - 配置管理
  - 主题切换
  - 状态控制

## 音频主题系统

### 量子主题 (Quantum)
```javascript
quantum: {
    baseFrequency: 55,
    harmonics: [1, 2, 3, 5, 8],
    colors: ['#00ffff', '#ff00ff', '#ffff00'],
    effects: ['reverb', 'chorus']
}
```

### 太空主题 (Space)
```javascript
space: {
    baseFrequency: 40,
    harmonics: [1, 1.5, 2, 3, 4],
    colors: ['#0066ff', '#6600ff', '#ff6600'],
    effects: ['delay', 'reverb']
}
```

### 能量主题 (Energy)
```javascript
energy: {
    baseFrequency: 80,
    harmonics: [1, 2, 4, 8, 16],
    colors: ['#ff0066', '#66ff00', '#0066ff'],
    effects: ['distortion', 'filter']
}
```

## API 使用指南

### 初始化音频系统

```javascript
// 初始化音频管理器
await audioManager.init({
    backgroundMusic: { enabled: true, volume: 0.6, bpm: 120 },
    soundEffects: { enabled: true, volume: 0.8 },
    microphone: { enabled: false, sensitivity: 0.8 },
    visualization: { enabled: true, style: 'quantum' }
});

// 设置可视化器
const canvas = document.getElementById('visualizerCanvas');
audioManager.setupVisualizer(canvas);
```

### 播放音效

```javascript
// 播放粒子激活音效
audioManager.playParticleActivation(440, 1.0);

// 播放量子共鸣音效
audioManager.playQuantumResonance(440, 0.8, 2.0);

// 播放连锁反应音效
audioManager.playChainReaction([440, 660, 880], 0.1);
```

### 控制背景音乐

```javascript
// 开始背景音乐
audioManager.startBackgroundMusic();

// 停止背景音乐
audioManager.stopBackgroundMusic();

// 设置音乐音量
audioManager.setMusicVolume(0.7);
```

### 主题切换

```javascript
// 切换到太空主题
audioManager.setTheme('space');

// 切换到能量主题
audioManager.setTheme('energy');
```

### 音频可视化

```javascript
// 启动可视化
audioManager.visualizer.start();

// 停止可视化
audioManager.visualizer.stop();

// 获取当前主要频率
const frequency = audioManager.getCurrentFrequency();
```

## 游戏集成

### 粒子系统集成
音频系统与粒子系统紧密集成，实现音频驱动的视觉效果：

```javascript
// 粒子激活时播放音效
onParticleActivate(particle) {
    audioManager.playParticleActivation(particle.frequency, particle.energy);
}

// 共鸣链反应时播放音效
onResonanceChain(particles) {
    const frequencies = particles.map(p => p.frequency);
    audioManager.playChainReaction(frequencies, 0.1);
}
```

### 频率检测集成
支持麦克风输入进行频率检测：

```javascript
// 启用麦克风
audioManager.setMicrophoneEnabled(true);

// 获取当前检测到的频率
const detectedFreq = audioManager.getCurrentFrequency();

// 根据检测频率激活粒子
if (detectedFreq > 0) {
    activateParticleByFrequency(detectedFreq);
}
```

## 性能优化

### 音频节点管理
- 自动回收未使用的音频节点
- 限制同时播放的音效数量
- 智能音频上下文暂停/恢复

### 内存管理
- 音频缓冲区复用
- 效果器节点池化
- 可视化数据缓存

### 浏览器兼容性
- Web Audio API特性检测
- 降级处理方案
- 移动设备优化

## 配置选项

### 音频质量设置
```javascript
{
    sampleRate: 44100,        // 采样率
    bufferSize: 4096,         // 缓冲区大小
    maxVoices: 32,            // 最大同时发声数
    effectQuality: 'high'     // 效果质量 (low/medium/high)
}
```

### 可视化设置
```javascript
{
    fftSize: 2048,           // FFT大小
    smoothingTimeConstant: 0.8, // 平滑常数
    minDecibels: -90,        // 最小分贝
    maxDecibels: -10,        // 最大分贝
    particleCount: 100       // 粒子数量
}
```

## 调试和测试

### 音频测试页面
使用 `audio-test.html` 进行音频系统测试：
- 音频引擎功能测试
- 合成器音效测试
- 效果器处理测试
- 可视化效果测试
- 主题切换测试

### 调试工具
```javascript
// 启用音频调试
audioManager.enableDebug(true);

// 获取音频统计信息
const stats = audioManager.getAudioStats();

// 监听音频事件
audioManager.on('noteStart', (frequency) => {
    console.log(`音符开始: ${frequency}Hz`);
});
```

## 扩展开发

### 自定义音效
```javascript
// 创建自定义音效
class CustomEffect extends AudioEffect {
    constructor(audioEngine, params) {
        super(audioEngine, 'custom', params);
        this.setupCustomNodes();
    }
    
    setupCustomNodes() {
        // 实现自定义音频处理逻辑
    }
}
```

### 新增主题
```javascript
// 添加新的音频主题
audioManager.themes.cyberpunk = {
    baseFrequency: 110,
    harmonics: [1, 1.414, 2, 2.828, 4],
    colors: ['#ff0080', '#00ff80', '#8000ff'],
    effects: ['distortion', 'delay', 'filter']
};
```

## 故障排除

### 常见问题
1. **音频无法播放**: 检查浏览器音频权限和用户交互要求
2. **延迟过高**: 调整缓冲区大小和音频上下文设置
3. **可视化不显示**: 确认Canvas元素正确设置和音频分析器连接
4. **麦克风无法访问**: 检查HTTPS环境和麦克风权限

### 错误处理
```javascript
try {
    await audioManager.init();
} catch (error) {
    console.error('音频初始化失败:', error);
    // 实施降级方案
    fallbackToBasicAudio();
}
```

## 未来扩展

### 计划功能
- 3D空间音频支持
- MIDI设备集成
- 音频录制和回放
- 高级音频分析
- 机器学习音频识别

### 技术升级
- Web Audio API新特性集成
- WebAssembly音频处理
- 音频工作线程优化
- 实时音频流处理
