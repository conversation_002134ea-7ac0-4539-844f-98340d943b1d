# 量子共鸣者 - 3D渲染系统文档

## 概述

3D渲染系统是量子共鸣者游戏的核心视觉组件，基于WebGL技术实现高性能的3D粒子渲染和量子效果可视化。系统采用模块化设计，支持2D/3D混合渲染模式。

## 系统架构

### 核心组件

1. **WebGLRenderer** (`js/render/webgl-renderer.js`)
   - WebGL上下文管理
   - 着色器程序编译和管理
   - 缓冲区管理
   - 矩阵运算
   - 基础几何体创建

2. **ParticleSystem3D** (`js/render/particle-system-3d.js`)
   - 3D粒子物理模拟
   - 量子共鸣效果
   - 粒子间相互作用
   - 连锁反应处理

3. **SceneManager3D** (`js/render/scene-manager-3d.js`)
   - 3D场景管理
   - 相机控制
   - 光照系统
   - 后处理效果
   - 用户交互处理

4. **RenderEngine** (`js/core/render-engine.js`)
   - 渲染模式管理
   - 2D/3D渲染协调
   - 性能监控

## 技术特性

### WebGL渲染

- **着色器系统**：自定义顶点和片段着色器
- **缓冲区管理**：高效的顶点数据管理
- **矩阵运算**：完整的3D变换矩阵支持
- **深度测试**：正确的3D深度渲染
- **混合模式**：透明度和发光效果

### 3D粒子系统

- **物理模拟**：
  - 重力和阻尼
  - 粒子间相互作用
  - 边界约束
  - 速度限制

- **量子效果**：
  - 频率共鸣检测
  - 能量波传播
  - 连锁反应
  - 量子场可视化

- **视觉效果**：
  - 粒子发光
  - 连接线动画
  - 颜色频率映射
  - 脉动效果

### 相机系统

- **自动旋转**：平滑的相机自动旋转
- **交互控制**：鼠标/触摸控制
- **缩放功能**：滚轮/手势缩放
- **平滑插值**：流畅的相机运动

### 光照系统

- **环境光**：基础场景照明
- **方向光**：主要光源
- **点光源**：动态彩色光照
- **动态效果**：光源位置和颜色动画

## API 参考

### WebGLRenderer

```javascript
// 初始化渲染器
const renderer = new WebGLRenderer();
await renderer.init(canvas);

// 开始/结束渲染帧
renderer.beginFrame();
renderer.endFrame();

// 获取统计信息
const stats = renderer.getStats();
```

### ParticleSystem3D

```javascript
// 创建粒子系统
const particleSystem = new ParticleSystem3D(webglRenderer);
particleSystem.init();

// 添加粒子
const particle = particleSystem.addParticle({
    position: [x, y, z],
    velocity: [vx, vy, vz],
    frequency: 440,
    energy: 1.0,
    color: [r, g, b]
});

// 更新和渲染
particleSystem.update(deltaTime);
particleSystem.render();
```

### SceneManager3D

```javascript
// 创建场景管理器
const sceneManager = new SceneManager3D();
await sceneManager.init(canvas);

// 更新和渲染场景
sceneManager.update(deltaTime);
sceneManager.render();

// 相机控制
sceneManager.resetCamera();
sceneManager.cameraController.autoRotate = true;
```

## 配置选项

### 渲染设置

```javascript
// WebGL渲染器配置
const renderer = new WebGLRenderer();
renderer.camera.fov = 45;           // 视野角度
renderer.camera.near = 0.1;        // 近裁剪面
renderer.camera.far = 100.0;       // 远裁剪面

// 光照配置
renderer.lighting.ambient = [0.2, 0.2, 0.4, 1.0];
renderer.lighting.directional.intensity = 0.8;
```

### 粒子系统配置

```javascript
// 物理参数
particleSystem.physics.gravity = [0, -0.001, 0];
particleSystem.physics.damping = 0.99;
particleSystem.physics.repulsion = 0.1;
particleSystem.physics.attraction = 0.05;

// 量子效果参数
particleSystem.quantumEffects.resonanceRadius = 2.0;
particleSystem.quantumEffects.waveSpeed = 2.0;
particleSystem.quantumEffects.energyDecay = 0.95;
```

### 相机控制配置

```javascript
// 相机控制器
sceneManager.cameraController.autoRotate = true;
sceneManager.cameraController.rotationSpeed = 0.2;
sceneManager.cameraController.minDistance = 3.0;
sceneManager.cameraController.maxDistance = 15.0;
```

## 性能优化

### 渲染优化

1. **批量渲染**：将相同类型的对象批量渲染
2. **视锥剔除**：只渲染可见的对象
3. **LOD系统**：根据距离调整细节级别
4. **缓冲区复用**：重用WebGL缓冲区

### 粒子优化

1. **对象池**：重用粒子对象
2. **空间分割**：使用空间网格优化碰撞检测
3. **更新频率**：根据距离调整更新频率
4. **内存管理**：及时清理过期粒子

### 内存管理

```javascript
// 清理资源
renderer.destroy();
particleSystem.destroy();
sceneManager.destroy();
```

## 浏览器兼容性

### WebGL支持

- **WebGL 2.0**：现代浏览器首选
- **WebGL 1.0**：兼容性回退
- **检测机制**：自动检测WebGL支持

### 性能要求

- **最低要求**：支持WebGL的现代浏览器
- **推荐配置**：独立显卡，4GB+ RAM
- **移动设备**：iOS 12+, Android 8+

## 调试和测试

### 调试工具

1. **性能监控**：FPS、渲染时间、内存使用
2. **统计信息**：粒子数量、绘制调用、顶点数
3. **错误处理**：WebGL错误检测和报告

### 测试页面

使用 `3d-test.html` 进行系统测试：

```bash
# 在浏览器中打开
open 量子共鸣者/3d-test.html
```

### 常见问题

1. **WebGL初始化失败**
   - 检查浏览器WebGL支持
   - 更新显卡驱动
   - 检查硬件加速设置

2. **性能问题**
   - 减少粒子数量
   - 降低渲染质量
   - 关闭后处理效果

3. **内存泄漏**
   - 确保正确调用destroy方法
   - 检查事件监听器清理
   - 监控WebGL资源释放

## 扩展开发

### 添加新的着色器

```javascript
// 在WebGLRenderer中添加新着色器
const vertexShader = `
    attribute vec3 a_position;
    uniform mat4 u_mvpMatrix;
    void main() {
        gl_Position = u_mvpMatrix * vec4(a_position, 1.0);
    }
`;

const fragmentShader = `
    precision mediump float;
    void main() {
        gl_FragColor = vec4(1.0, 0.0, 0.0, 1.0);
    }
`;

const program = renderer.createShaderProgram(vertexShader, fragmentShader);
renderer.shaderPrograms.set('custom', program);
```

### 添加新的粒子效果

```javascript
// 扩展ParticleSystem3D
class CustomParticleSystem extends ParticleSystem3D {
    updateCustomEffect() {
        // 自定义效果逻辑
    }
    
    renderCustomEffect() {
        // 自定义渲染逻辑
    }
}
```

### 添加后处理效果

```javascript
// 在SceneManager3D中添加后处理
applyCustomPostProcessing() {
    // 后处理效果实现
    // 如：辉光、模糊、色彩调整等
}
```

## 更新日志

### v1.0.0 (2024-07-30)
- 初始版本发布
- 基础WebGL渲染系统
- 3D粒子系统
- 相机控制系统
- 光照系统
- 性能监控

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。
