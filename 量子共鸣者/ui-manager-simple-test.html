<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Manager 简化测试 - 量子共鸣者</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0f0f1e;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: #1a1a2e;
            padding: 20px;
            border-radius: 10px;
        }
        
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .success { background: #2d5a2d; color: #90ee90; }
        .error { background: #5a2d2d; color: #ff6b6b; }
        .info { background: #2d4a5a; color: #87ceeb; }
        
        .test-button {
            background: #4a90e2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .screen {
            display: none;
            opacity: 0;
            visibility: hidden;
            padding: 20px;
            margin: 10px 0;
            background: #2a2a3e;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .screen.active {
            opacity: 1;
            visibility: visible;
        }
        
        .console-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 UI Manager 简化测试</h1>
        
        <div id="test-results"></div>
        
        <div>
            <button class="test-button" onclick="runBasicTest()">运行基础测试</button>
            <button class="test-button" onclick="testScreenSwitching()">测试屏幕切换</button>
            <button class="test-button" onclick="clearResults()">清空结果</button>
        </div>
        
        <!-- 测试屏幕 -->
        <div id="main-menu-screen" class="screen">
            <h2>主菜单屏幕</h2>
            <p>这是主菜单的测试内容</p>
        </div>
        
        <div id="game-screen" class="screen">
            <h2>游戏屏幕</h2>
            <p>这是游戏屏幕的测试内容</p>
        </div>
        
        <div id="settings-screen" class="screen">
            <h2>设置屏幕</h2>
            <p>这是设置屏幕的测试内容</p>
        </div>
        
        <div id="levelSelectScreen" class="screen">
            <h2>关卡选择屏幕</h2>
            <p>这是关卡选择的测试内容</p>
        </div>
        
        <div class="console-output" id="console-output"></div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/ui/ui-manager.js"></script>
    
    <script>
        let testResults = document.getElementById('test-results');
        let consoleOutput = document.getElementById('console-output');
        let testUIManager = null;
        
        // 劫持控制台输出
        const originalConsole = {};
        ['log', 'error', 'warn', 'info'].forEach(method => {
            originalConsole[method] = console[method];
            console[method] = function(...args) {
                originalConsole[method].apply(console, args);
                
                const timestamp = new Date().toLocaleTimeString();
                const message = args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                ).join(' ');
                
                const color = {
                    log: '#0f0',
                    error: '#f00', 
                    warn: '#ff0',
                    info: '#0ff'
                }[method] || '#0f0';
                
                consoleOutput.innerHTML += `<div style="color: ${color}">[${timestamp}] ${method.toUpperCase()}: ${message}</div>`;
                consoleOutput.scrollTop = consoleOutput.scrollHeight;
            };
        });
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            testResults.appendChild(div);
        }
        
        function clearResults() {
            testResults.innerHTML = '';
            consoleOutput.innerHTML = '';
        }
        
        function runBasicTest() {
            clearResults();
            addResult('🔧 开始基础测试...', 'info');
            
            try {
                // 检查UIManager类
                if (typeof UIManager === 'undefined') {
                    throw new Error('UIManager类未找到');
                }
                addResult('✅ UIManager类存在', 'success');
                
                // 创建实例
                testUIManager = new UIManager();
                addResult('✅ UIManager实例创建成功', 'success');
                
                // 初始化
                const initResult = testUIManager.init();
                addResult('✅ UIManager初始化完成', 'success');
                
                // 检查屏幕注册
                const screenCount = testUIManager.screens.size;
                addResult(`📱 已注册 ${screenCount} 个屏幕`, 'info');
                
                // 列出注册的屏幕
                testUIManager.screens.forEach((screen, screenId) => {
                    addResult(`  - ${screenId}: ${screen.element ? '✅' : '❌'}`, screen.element ? 'success' : 'error');
                });
                
                addResult('🎉 基础测试完成', 'success');
                
            } catch (error) {
                addResult(`❌ 基础测试失败: ${error.message}`, 'error');
                console.error('基础测试错误:', error);
            }
        }
        
        async function testScreenSwitching() {
            if (!testUIManager) {
                addResult('❌ 请先运行基础测试', 'error');
                return;
            }
            
            addResult('🔄 开始屏幕切换测试...', 'info');
            
            const screensToTest = ['main-menu-screen', 'game-screen', 'settings-screen', 'levelSelectScreen'];
            
            for (const screenName of screensToTest) {
                try {
                    addResult(`🔄 切换到: ${screenName}`, 'info');
                    
                    await testUIManager.showScreen(screenName);
                    
                    // 检查屏幕状态
                    const screen = testUIManager.screens.get(screenName);
                    if (screen && screen.visible) {
                        addResult(`✅ ${screenName} 切换成功`, 'success');
                    } else {
                        addResult(`❌ ${screenName} 切换失败`, 'error');
                    }
                    
                    // 等待一下再切换下一个
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                } catch (error) {
                    addResult(`❌ ${screenName} 切换错误: ${error.message}`, 'error');
                }
            }
            
            addResult('🎉 屏幕切换测试完成', 'success');
        }
        
        // 页面加载完成后自动运行基础测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 UI Manager简化测试页面已加载');
            setTimeout(runBasicTest, 1000);
        });
    </script>
</body>
</html>
