<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知系统和关卡启动修复测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: #ffffff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            text-align: center;
            color: #00d4ff;
            margin-bottom: 30px;
            font-size: 28px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 4px solid #00d4ff;
        }

        .test-section h2 {
            color: #00ff88;
            margin-top: 0;
            font-size: 20px;
        }

        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .test-btn {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            text-transform: none;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
        }

        .test-btn.success {
            background: linear-gradient(45deg, #00ff88, #00cc66);
        }

        .test-btn.warning {
            background: linear-gradient(45deg, #ffaa00, #cc8800);
        }

        .test-btn.error {
            background: linear-gradient(45deg, #ff4444, #cc3333);
        }

        .test-btn.game {
            background: linear-gradient(45deg, #ff6b6b, #cc5555);
        }

        .console-output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
            border: 1px solid #333;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-loading { background: #ffaa00; }
        .status-success { background: #00ff88; }
        .status-error { background: #ff4444; }

        .info-box {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .info-box h3 {
            margin-top: 0;
            color: #00d4ff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 通知系统和关卡启动修复测试</h1>

        <div class="test-section">
            <h2>📢 通知系统测试</h2>
            <p>测试新的消息通知系统是否正常工作，替代传统的alert弹框：</p>
            <div class="test-buttons">
                <button class="test-btn" onclick="testInfoNotification()">📝 信息通知</button>
                <button class="test-btn success" onclick="testSuccessNotification()">✅ 成功通知</button>
                <button class="test-btn warning" onclick="testWarningNotification()">⚠️ 警告通知</button>
                <button class="test-btn error" onclick="testErrorNotification()">❌ 错误通知</button>
                <button class="test-btn game" onclick="testGameNotification()">🎮 游戏通知</button>
            </div>
        </div>

        <div class="test-section">
            <h2>🎮 关卡启动功能测试</h2>
            <p>测试修复后的关卡选择和启动功能：</p>
            <div class="test-buttons">
                <button class="test-btn" onclick="testLevelSelect()">🎯 打开关卡选择</button>
                <button class="test-btn" onclick="testStartLevel('quantum_basics', 'normal')">🚀 启动基础关卡</button>
                <button class="test-btn" onclick="testStartLevel('resonance_chains', 'hard')">⚡ 启动困难关卡</button>
                <button class="test-btn" onclick="testGameController()">🔧 测试游戏控制器</button>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 系统状态检查</h2>
            <p>检查各个系统组件的加载状态：</p>
            <div class="test-buttons">
                <button class="test-btn" onclick="checkSystemStatus()">🔍 检查系统状态</button>
                <button class="test-btn" onclick="clearAllNotifications()">🧹 清除所有通知</button>
            </div>
        </div>

        <div class="info-box">
            <h3>🎯 测试说明</h3>
            <p><strong>修复内容：</strong></p>
            <ul>
                <li>✅ 添加了缺失的 <code>startLevel</code> 方法到 GameController</li>
                <li>✅ 创建了完整的消息通知系统替代 alert 弹框</li>
                <li>✅ 改进了错误处理机制，提供更好的用户体验</li>
                <li>✅ 在游戏关键事件中添加了通知提醒</li>
            </ul>
            <p><strong>预期效果：</strong></p>
            <ul>
                <li>关卡选择后点击"开始游戏"不再报错</li>
                <li>所有错误和提示信息以优雅的通知形式显示</li>
                <li>游戏状态变化时有相应的通知反馈</li>
            </ul>
        </div>

        <div class="console-output" id="consoleOutput">
            <div>🔧 测试控制台输出：</div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/utils/math-utils.js"></script>
    <script src="js/core/audio-engine.js"></script>
    <script src="js/core/physics-engine.js"></script>
    <script src="js/core/quantum-engine.js"></script>
    <script src="js/core/render-engine.js"></script>
    <script src="js/ui/notification-system.js"></script>
    <script src="js/ui/ui-manager.js"></script>
    <script src="js/ui/level-select.js"></script>
    <script src="js/game/level-manager.js"></script>
    <script src="js/game/game-controller.js"></script>
    <script src="js/main.js"></script>

    <script>
        // 控制台输出拦截
        const consoleOutput = document.getElementById('consoleOutput');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.style.color = type === 'error' ? '#ff4444' : type === 'warn' ? '#ffaa00' : '#00ff00';
            div.textContent = `[${timestamp}] ${message}`;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        // 测试函数
        function testInfoNotification() {
            if (window.notificationSystem) {
                window.notificationSystem.info('这是一个信息通知示例，用于显示一般性信息。', {
                    title: '信息通知测试',
                    duration: 4000
                });
                console.log('✅ 信息通知测试完成');
            } else {
                console.error('❌ 通知系统未加载');
            }
        }

        function testSuccessNotification() {
            if (window.notificationSystem) {
                window.notificationSystem.success('操作成功完成！这是一个成功通知的示例。', {
                    title: '成功通知测试',
                    duration: 3000
                });
                console.log('✅ 成功通知测试完成');
            } else {
                console.error('❌ 通知系统未加载');
            }
        }

        function testWarningNotification() {
            if (window.notificationSystem) {
                window.notificationSystem.warning('这是一个警告通知，提醒用户注意某些情况。', {
                    title: '警告通知测试',
                    duration: 5000
                });
                console.log('✅ 警告通知测试完成');
            } else {
                console.error('❌ 通知系统未加载');
            }
        }

        function testErrorNotification() {
            if (window.notificationSystem) {
                window.notificationSystem.error('这是一个错误通知，用于显示错误信息和异常情况。', {
                    title: '错误通知测试',
                    duration: 6000
                });
                console.log('✅ 错误通知测试完成');
            } else {
                console.error('❌ 通知系统未加载');
            }
        }

        function testGameNotification() {
            if (window.notificationSystem) {
                window.notificationSystem.game('欢迎来到量子共鸣者！准备开始你的量子冒险之旅。', {
                    title: '游戏通知测试',
                    duration: 4000
                });
                console.log('✅ 游戏通知测试完成');
            } else {
                console.error('❌ 通知系统未加载');
            }
        }

        function testLevelSelect() {
            console.log('🎯 测试关卡选择界面...');
            if (window.quantumApp && window.quantumApp.startGame) {
                window.quantumApp.startGame();
                console.log('✅ 关卡选择界面已打开');
            } else {
                console.error('❌ 应用程序未正确初始化');
            }
        }

        function testStartLevel(levelId, difficulty) {
            console.log(`🚀 测试启动关卡: ${levelId} (${difficulty})`);
            if (window.gameController && window.gameController.startLevel) {
                window.gameController.startLevel(levelId, difficulty);
                console.log(`✅ 关卡 ${levelId} 启动测试完成`);
            } else {
                console.error('❌ 游戏控制器未正确初始化或缺少startLevel方法');
            }
        }

        function testGameController() {
            console.log('🔧 测试游戏控制器功能...');
            if (window.gameController) {
                console.log('✅ 游戏控制器已加载');
                console.log('🔍 检查startLevel方法:', typeof window.gameController.startLevel);
                console.log('🔍 检查showError方法:', typeof window.gameController.showError);
                console.log('🔍 检查showGameNotification方法:', typeof window.gameController.showGameNotification);
                
                // 测试错误处理
                window.gameController.showError('这是一个测试错误消息');
                
                // 测试游戏通知
                if (window.gameController.showGameNotification) {
                    window.gameController.showGameNotification('测试游戏通知', '副标题信息');
                }
            } else {
                console.error('❌ 游戏控制器未加载');
            }
        }

        function checkSystemStatus() {
            console.log('🔍 检查系统状态...');
            
            const systems = [
                { name: '通知系统', obj: window.notificationSystem },
                { name: '量子应用', obj: window.quantumApp },
                { name: '游戏控制器', obj: window.gameController },
                { name: '关卡选择', obj: window.levelSelect },
                { name: 'UI管理器', obj: window.uiManager },
                { name: '音频引擎', obj: window.audioEngine },
                { name: '物理引擎', obj: window.physicsEngine },
                { name: '量子引擎', obj: window.quantumEngine }
            ];

            systems.forEach(system => {
                const status = system.obj ? '✅ 已加载' : '❌ 未加载';
                console.log(`${system.name}: ${status}`);
            });

            // 检查关键方法
            if (window.gameController) {
                const methods = ['startLevel', 'showError', 'showGameNotification'];
                methods.forEach(method => {
                    const exists = typeof window.gameController[method] === 'function';
                    console.log(`GameController.${method}: ${exists ? '✅ 存在' : '❌ 缺失'}`);
                });
            }
        }

        function clearAllNotifications() {
            if (window.notificationSystem) {
                window.notificationSystem.clear();
                console.log('🧹 所有通知已清除');
            } else {
                console.error('❌ 通知系统未加载');
            }
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            console.log('🚀 测试页面加载完成');
            setTimeout(() => {
                checkSystemStatus();
                if (window.notificationSystem) {
                    window.notificationSystem.info('测试页面已准备就绪，可以开始测试各项功能。', {
                        title: '系统就绪',
                        duration: 4000
                    });
                }
            }, 2000);
        });
    </script>
</body>
</html>
