<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终UI测试 - 量子共鸣者</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0f0f1e;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: #1a1a2e;
            padding: 20px;
            border-radius: 10px;
        }
        
        .test-section {
            background: #2a2a3e;
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #4a90e2;
        }
        
        .test-result {
            padding: 8px 12px;
            margin: 8px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .success { background: #2d5a2d; color: #90ee90; }
        .error { background: #5a2d2d; color: #ff6b6b; }
        .info { background: #2d4a5a; color: #87ceeb; }
        .warning { background: #5a5a2d; color: #ffff90; }
        
        .test-button {
            background: #4a90e2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #357abd;
        }
        
        .screen {
            display: none;
            opacity: 0;
            visibility: hidden;
            padding: 20px;
            margin: 10px 0;
            background: #3a3a4e;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .screen.active {
            display: flex;
            opacity: 1;
            visibility: visible;
        }
        
        .console-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            max-height: 250px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        
        .status-item {
            background: #3a3a4e;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        
        .status-item.active {
            background: #2d5a2d;
        }
        
        .status-item.inactive {
            background: #5a2d2d;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎯 最终UI测试 - 量子共鸣者</h1>
        <p>全面测试UI Manager修复效果</p>
        
        <!-- 系统状态 -->
        <div class="test-section">
            <h2>📊 系统状态</h2>
            <div class="status-grid" id="system-status">
                <!-- 状态项将动态生成 -->
            </div>
        </div>
        
        <!-- 测试控制 -->
        <div class="test-section">
            <h2>🎮 测试控制</h2>
            <button class="test-button" onclick="runFullTest()">运行完整测试</button>
            <button class="test-button" onclick="testUIManager()">测试UI Manager</button>
            <button class="test-button" onclick="testScreens()">测试屏幕切换</button>
            <button class="test-button" onclick="testLevelSelect()">测试关卡选择</button>
            <button class="test-button" onclick="clearAll()">清空所有</button>
        </div>
        
        <!-- 测试结果 -->
        <div class="test-section">
            <h2>📋 测试结果</h2>
            <div id="test-results"></div>
        </div>
        
        <!-- 屏幕预览 -->
        <div class="test-section">
            <h2>📱 屏幕预览</h2>
            <div id="screen-preview">
                <!-- 屏幕状态将显示在这里 -->
            </div>
        </div>
        
        <!-- 测试屏幕 -->
        <div id="loading-screen" class="screen">
            <h2>加载屏幕</h2>
            <p>正在加载游戏...</p>
        </div>
        
        <div id="main-menu-screen" class="screen">
            <h2>主菜单</h2>
            <p>欢迎来到量子共鸣者</p>
        </div>
        
        <div id="game-screen" class="screen">
            <h2>游戏屏幕</h2>
            <p>游戏进行中...</p>
        </div>
        
        <div id="settings-screen" class="screen">
            <h2>设置屏幕</h2>
            <p>游戏设置</p>
        </div>
        
        <div id="levelSelectScreen" class="screen">
            <h2>关卡选择</h2>
            <div id="levelGrid">关卡网格</div>
            <div id="levelPreview">关卡预览</div>
            <button id="startLevelButton">开始游戏</button>
        </div>
        
        <!-- 控制台输出 -->
        <div class="test-section">
            <h2>📊 控制台输出</h2>
            <div class="console-output" id="console-output"></div>
        </div>
    </div>

    <!-- 引入脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/ui/ui-manager.js"></script>
    <script src="js/ui/level-select.js"></script>
    
    <script>
        let testResults = document.getElementById('test-results');
        let consoleOutput = document.getElementById('console-output');
        let systemStatus = document.getElementById('system-status');
        let screenPreview = document.getElementById('screen-preview');
        let testUIManager = null;
        let testLevelSelect = null;
        
        // 劫持控制台输出
        const originalConsole = {};
        ['log', 'error', 'warn', 'info'].forEach(method => {
            originalConsole[method] = console[method];
            console[method] = function(...args) {
                originalConsole[method].apply(console, args);
                
                const timestamp = new Date().toLocaleTimeString();
                const message = args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                ).join(' ');
                
                const color = {
                    log: '#0f0',
                    error: '#f00', 
                    warn: '#ff0',
                    info: '#0ff'
                }[method] || '#0f0';
                
                consoleOutput.innerHTML += `<div style="color: ${color}">[${timestamp}] ${method.toUpperCase()}: ${message}</div>`;
                consoleOutput.scrollTop = consoleOutput.scrollHeight;
            };
        });
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            testResults.appendChild(div);
        }
        
        function updateSystemStatus() {
            const systems = [
                { name: 'UIManager类', check: () => typeof UIManager !== 'undefined' },
                { name: 'UIManager实例', check: () => testUIManager !== null },
                { name: 'UIManager初始化', check: () => testUIManager && testUIManager.screens.size > 0 },
                { name: 'LevelSelect类', check: () => typeof LevelSelect !== 'undefined' },
                { name: 'LevelSelect实例', check: () => testLevelSelect !== null },
                { name: '全局uiManager', check: () => window.uiManager !== undefined },
                { name: '屏幕元素', check: () => document.getElementById('main-menu-screen') !== null },
                { name: '关卡选择元素', check: () => document.getElementById('levelSelectScreen') !== null }
            ];
            
            systemStatus.innerHTML = '';
            systems.forEach(system => {
                const div = document.createElement('div');
                div.className = `status-item ${system.check() ? 'active' : 'inactive'}`;
                div.innerHTML = `<strong>${system.name}</strong><br>${system.check() ? '✅ 正常' : '❌ 异常'}`;
                systemStatus.appendChild(div);
            });
        }
        
        function updateScreenPreview() {
            if (!testUIManager) {
                screenPreview.innerHTML = '<p>UI Manager未初始化</p>';
                return;
            }
            
            let html = '<h3>屏幕状态</h3>';
            testUIManager.screens.forEach((screen, screenId) => {
                const element = screen.element;
                const isVisible = screen.visible;
                const display = element ? element.style.display : 'unknown';
                const opacity = element ? element.style.opacity : 'unknown';
                const hasActive = element ? element.classList.contains('active') : false;
                
                html += `
                    <div style="display: flex; justify-content: space-between; padding: 5px; margin: 2px 0; background: #3a3a4e; border-radius: 3px;">
                        <span>${screenId}</span>
                        <span style="color: ${isVisible ? '#90ee90' : '#ff6b6b'}">
                            ${isVisible ? '可见' : '隐藏'} | display: ${display} | opacity: ${opacity} | active: ${hasActive}
                        </span>
                    </div>
                `;
            });
            
            screenPreview.innerHTML = html;
        }
        
        function clearAll() {
            testResults.innerHTML = '';
            consoleOutput.innerHTML = '';
            updateSystemStatus();
            updateScreenPreview();
        }
        
        async function testUIManager() {
            addResult('🔧 开始测试UI Manager...', 'info');
            
            try {
                // 检查类存在
                if (typeof UIManager === 'undefined') {
                    throw new Error('UIManager类未找到');
                }
                addResult('✅ UIManager类存在', 'success');
                
                // 创建实例
                testUIManager = new UIManager();
                addResult('✅ UIManager实例创建成功', 'success');
                
                // 初始化
                testUIManager.init();
                addResult('✅ UIManager初始化完成', 'success');
                
                // 检查屏幕注册
                const screenCount = testUIManager.screens.size;
                addResult(`📱 已注册 ${screenCount} 个屏幕`, 'info');
                
                if (screenCount === 0) {
                    addResult('⚠️ 没有屏幕被注册', 'warning');
                }
                
                updateSystemStatus();
                updateScreenPreview();
                
            } catch (error) {
                addResult(`❌ UI Manager测试失败: ${error.message}`, 'error');
                console.error('UI Manager测试错误:', error);
            }
        }
        
        async function testScreens() {
            if (!testUIManager) {
                addResult('❌ 请先测试UI Manager', 'error');
                return;
            }
            
            addResult('🔄 开始测试屏幕切换...', 'info');
            
            const screensToTest = ['main-menu-screen', 'game-screen', 'settings-screen', 'levelSelectScreen'];
            
            for (const screenName of screensToTest) {
                try {
                    addResult(`🔄 切换到: ${screenName}`, 'info');
                    
                    await testUIManager.showScreen(screenName);
                    
                    const screen = testUIManager.screens.get(screenName);
                    if (screen && screen.visible) {
                        addResult(`✅ ${screenName} 切换成功`, 'success');
                    } else {
                        addResult(`❌ ${screenName} 切换失败`, 'error');
                    }
                    
                    updateScreenPreview();
                    await new Promise(resolve => setTimeout(resolve, 300));
                    
                } catch (error) {
                    addResult(`❌ ${screenName} 切换错误: ${error.message}`, 'error');
                }
            }
        }
        
        async function testLevelSelect() {
            addResult('🎯 开始测试关卡选择...', 'info');
            
            try {
                if (typeof LevelSelect === 'undefined') {
                    throw new Error('LevelSelect类未找到');
                }
                addResult('✅ LevelSelect类存在', 'success');
                
                testLevelSelect = new LevelSelect();
                addResult('✅ LevelSelect实例创建成功', 'success');
                
                testLevelSelect.init();
                addResult('✅ LevelSelect初始化完成', 'success');
                
                testLevelSelect.show();
                addResult('✅ LevelSelect显示调用完成', 'success');
                
                updateSystemStatus();
                
            } catch (error) {
                addResult(`❌ 关卡选择测试失败: ${error.message}`, 'error');
                console.error('关卡选择测试错误:', error);
            }
        }
        
        async function runFullTest() {
            clearAll();
            addResult('🚀 开始运行完整测试...', 'info');
            
            await testUIManager();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testScreens();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testLevelSelect();
            
            addResult('🎉 完整测试完成', 'success');
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 最终UI测试页面已加载');
            updateSystemStatus();
            
            // 自动运行测试
            setTimeout(runFullTest, 1000);
        });
    </script>
</body>
</html>
