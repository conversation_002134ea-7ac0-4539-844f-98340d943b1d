<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量子共鸣者 - 调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
            padding: 20px;
        }
        .debug-info {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        button {
            background: #00d4ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>🎮 量子共鸣者 - 调试测试</h1>
    
    <button onclick="debugLevelSelect()">调试关卡选择</button>
    <button onclick="debugGameController()">调试游戏控制器</button>
    <button onclick="debugAll()">调试所有</button>
    
    <div id="debug-output"></div>

    <!-- 必要的DOM元素 -->
    <div id="loadingScreen" style="display: none;">
        <div id="loadingProgress"></div>
        <div id="loadingText">0%</div>
    </div>
    <div id="mainMenu" style="display: none;"></div>
    <div id="settingsScreen" style="display: none;"></div>
    <div id="aboutScreen" style="display: none;"></div>
    <div id="levelSelectScreen" style="display: none;"></div>
    <canvas id="game-canvas" style="display: none;" width="800" height="600"></canvas>
    <button id="initAudioButton" style="display: none;">初始化音频</button>

    <!-- 只加载关键脚本 -->
    <script src="js/ui/level-select.js"></script>
    <script src="js/game/game-controller.js"></script>

    <script>
        function addDebugInfo(title, content) {
            const div = document.createElement('div');
            div.className = 'debug-info';
            div.innerHTML = `<strong>${title}:</strong><br>${content}`;
            document.getElementById('debug-output').appendChild(div);
        }

        function debugLevelSelect() {
            document.getElementById('debug-output').innerHTML = '';
            
            addDebugInfo('window.levelSelect 存在', window.levelSelect ? '✅ 是' : '❌ 否');
            
            if (window.levelSelect) {
                addDebugInfo('levelSelect 类型', typeof window.levelSelect);
                addDebugInfo('levelSelect 构造函数', window.levelSelect.constructor.name);
                
                // 检查所有方法
                const methods = Object.getOwnPropertyNames(Object.getPrototypeOf(window.levelSelect));
                addDebugInfo('levelSelect 方法列表', methods.join(', '));
                
                // 检查特定方法
                addDebugInfo('startLevel 方法', typeof window.levelSelect.startLevel === 'function' ? '✅ 存在' : '❌ 不存在');
                addDebugInfo('hideWithoutReturnToMenu 方法', typeof window.levelSelect.hideWithoutReturnToMenu === 'function' ? '✅ 存在' : '❌ 不存在');
                addDebugInfo('hide 方法', typeof window.levelSelect.hide === 'function' ? '✅ 存在' : '❌ 不存在');
                addDebugInfo('show 方法', typeof window.levelSelect.show === 'function' ? '✅ 存在' : '❌ 不存在');
                
                // 检查属性
                addDebugInfo('isInitialized', window.levelSelect.isInitialized);
                addDebugInfo('isVisible', window.levelSelect.isVisible);
            }
        }

        function debugGameController() {
            addDebugInfo('window.gameController 存在', window.gameController ? '✅ 是' : '❌ 否');
            
            if (window.gameController) {
                addDebugInfo('gameController 类型', typeof window.gameController);
                addDebugInfo('gameController 构造函数', window.gameController.constructor.name);
                addDebugInfo('isInitialized', window.gameController.isInitialized);
                
                // 检查init方法
                addDebugInfo('init 方法', typeof window.gameController.init === 'function' ? '✅ 存在' : '❌ 不存在');
            }
        }

        function debugAll() {
            document.getElementById('debug-output').innerHTML = '';
            debugLevelSelect();
            debugGameController();
            
            // 检查其他全局对象
            const globalObjects = ['storageService', 'i18n', 'uiManager', 'renderEngine', 'audioManager'];
            globalObjects.forEach(obj => {
                addDebugInfo(`window.${obj}`, window[obj] ? '✅ 存在' : '❌ 不存在');
            });
        }

        // 页面加载完成后自动调试
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(debugAll, 1000);
        });
    </script>
</body>
</html>
