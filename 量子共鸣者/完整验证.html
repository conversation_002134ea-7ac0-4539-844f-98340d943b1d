<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量子共鸣者 - 完整验证</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #ffffff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            color: #00d4ff;
            margin-bottom: 30px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }
        
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.3);
            border-left: 4px solid #666;
        }
        
        .pass { 
            border-left-color: #00ff00; 
            background: rgba(0, 255, 0, 0.1);
        }
        
        .fail { 
            border-left-color: #ff0000; 
            background: rgba(255, 0, 0, 0.1);
        }
        
        .summary {
            background: rgba(0, 212, 255, 0.2);
            border: 2px solid #00d4ff;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
        }
        
        button {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
        }
        
        .status-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .status-pass { background: #00ff00; }
        .status-fail { background: #ff0000; }
        .status-loading { background: #ffaa00; animation: pulse 1s infinite; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .log-output {
            background: #000;
            color: #00ff00;
            font-family: monospace;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 量子共鸣者 - 完整验证测试</h1>
        
        <div style="text-align: center; margin-bottom: 30px;">
            <button onclick="runCompleteTest()">🚀 运行完整测试</button>
            <button onclick="clearResults()">🧹 清空结果</button>
            <button onclick="showLogs()">📋 显示日志</button>
        </div>
        
        <div id="test-status" class="summary" style="display: none;">
            <span class="status-indicator status-loading"></span>
            准备运行测试...
        </div>
        
        <div id="results"></div>
        
        <div id="logs" class="log-output" style="display: none;"></div>
        
        <div id="final-summary" style="display: none;"></div>
    </div>

    <!-- 必要的DOM元素 -->
    <div id="loadingScreen" style="display: none;">
        <div id="loadingProgress" style="width: 0%; height: 4px; background: #00d4ff;"></div>
        <div id="loadingText">0%</div>
    </div>
    
    <div id="mainMenu" style="display: none;">
        <button id="startGameButton">开始游戏</button>
        <button id="levelSelectButton">选择关卡</button>
        <button id="settingsButton">设置</button>
        <button id="aboutButton">关于</button>
    </div>
    
    <div id="settingsScreen" style="display: none;"></div>
    <div id="aboutScreen" style="display: none;"></div>
    <div id="levelSelectScreen" style="display: none;"></div>
    <canvas id="game-canvas" style="display: none;" width="800" height="600"></canvas>
    <button id="initAudioButton" style="display: none;">初始化音频</button>

    <!-- 加载关键脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/ui/level-select.js"></script>
    <script src="js/game/game-controller.js"></script>

    <script>
        let testResults = [];
        let logs = [];

        // 捕获控制台日志
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            logs.push(`[LOG] ${args.join(' ')}`);
            originalLog.apply(console, args);
        };

        console.error = function(...args) {
            logs.push(`[ERROR] ${args.join(' ')}`);
            originalError.apply(console, args);
        };

        console.warn = function(...args) {
            logs.push(`[WARN] ${args.join(' ')}`);
            originalWarn.apply(console, args);
        };

        function addResult(name, passed, message) {
            const result = { name, passed, message };
            testResults.push(result);
            
            const div = document.createElement('div');
            div.className = `test-result ${passed ? 'pass' : 'fail'}`;
            div.innerHTML = `
                <span class="status-indicator ${passed ? 'status-pass' : 'status-fail'}"></span>
                <strong>${name}:</strong> ${message}
            `;
            document.getElementById('results').appendChild(div);
        }

        function updateStatus(message, type = 'loading') {
            const statusDiv = document.getElementById('test-status');
            statusDiv.style.display = 'block';
            statusDiv.innerHTML = `
                <span class="status-indicator status-${type}"></span>
                ${message}
            `;
        }

        async function runCompleteTest() {
            testResults = [];
            logs = [];
            document.getElementById('results').innerHTML = '';
            document.getElementById('final-summary').style.display = 'none';
            
            updateStatus('正在运行完整测试...', 'loading');
            
            // 等待页面完全加载
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 运行所有测试
            runAllTests();
            
            // 生成最终报告
            generateFinalReport();
        }

        function runAllTests() {
            console.log('🔍 开始运行完整测试套件...');
            
            // 测试1: i18n 服务
            const i18nExists = typeof window.i18n !== 'undefined';
            addResult('i18n服务存在性检查', i18nExists, 
                i18nExists ? '✅ window.i18n 存在' : '❌ window.i18n 不存在');

            // 测试2: 游戏控制器存在
            const gameControllerExists = typeof window.gameController !== 'undefined';
            addResult('游戏控制器存在性检查', gameControllerExists, 
                gameControllerExists ? '✅ window.gameController 存在' : '❌ window.gameController 不存在');

            // 测试3: 游戏控制器已初始化
            if (gameControllerExists) {
                const initialized = window.gameController.isInitialized === true;
                addResult('游戏控制器初始化检查', initialized, 
                    initialized ? '✅ 游戏控制器已初始化' : '❌ 游戏控制器未初始化');
            } else {
                addResult('游戏控制器初始化检查', false, '❌ 游戏控制器不存在');
            }

            // 测试4: 关卡选择界面存在
            const levelSelectExists = typeof window.levelSelect !== 'undefined';
            addResult('关卡选择界面存在性检查', levelSelectExists, 
                levelSelectExists ? '✅ window.levelSelect 存在' : '❌ window.levelSelect 不存在');

            // 测试5: startLevel方法存在
            if (levelSelectExists) {
                const hasStartLevel = typeof window.levelSelect.startLevel === 'function';
                addResult('startLevel方法检查', hasStartLevel, 
                    hasStartLevel ? '✅ startLevel方法存在' : '❌ startLevel方法不存在');
            } else {
                addResult('startLevel方法检查', false, '❌ 关卡选择界面不存在');
            }

            // 测试6: hideWithoutReturnToMenu方法存在
            if (levelSelectExists) {
                const hasHideMethod = typeof window.levelSelect.hideWithoutReturnToMenu === 'function';
                addResult('hideWithoutReturnToMenu方法检查', hasHideMethod, 
                    hasHideMethod ? '✅ hideWithoutReturnToMenu方法存在' : '❌ hideWithoutReturnToMenu方法不存在');
                
                // 如果方法存在，测试调用
                if (hasHideMethod) {
                    try {
                        window.levelSelect.hideWithoutReturnToMenu();
                        addResult('hideWithoutReturnToMenu方法调用', true, '✅ 方法调用成功');
                    } catch (error) {
                        addResult('hideWithoutReturnToMenu方法调用', false, `❌ 方法调用失败: ${error.message}`);
                    }
                }
            } else {
                addResult('hideWithoutReturnToMenu方法检查', false, '❌ 关卡选择界面不存在');
            }
        }

        function generateFinalReport() {
            const passed = testResults.filter(r => r.passed).length;
            const total = testResults.length;
            const success = passed === total;
            
            const summaryDiv = document.getElementById('final-summary');
            summaryDiv.style.display = 'block';
            summaryDiv.className = `summary ${success ? 'pass' : 'fail'}`;
            summaryDiv.innerHTML = `
                <span class="status-indicator ${success ? 'status-pass' : 'status-fail'}"></span>
                <strong>最终测试结果: ${passed}/${total} 个测试通过</strong>
                <br><br>
                ${success ? 
                    '🎉 所有测试通过！量子共鸣者关卡选择功能修复成功！' : 
                    '😞 部分测试失败，请查看日志了解详情。'
                }
            `;
            
            updateStatus(success ? '✅ 测试完成 - 全部通过' : '❌ 测试完成 - 部分失败', 
                        success ? 'pass' : 'fail');
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('final-summary').style.display = 'none';
            document.getElementById('test-status').style.display = 'none';
            document.getElementById('logs').style.display = 'none';
            testResults = [];
            logs = [];
        }

        function showLogs() {
            const logsDiv = document.getElementById('logs');
            logsDiv.style.display = logsDiv.style.display === 'none' ? 'block' : 'none';
            logsDiv.innerHTML = logs.join('\n');
        }

        // 页面加载完成后显示准备状态
        document.addEventListener('DOMContentLoaded', () => {
            updateStatus('页面加载完成，等待组件初始化...', 'loading');
            
            // 等待一段时间后自动运行测试
            setTimeout(() => {
                updateStatus('准备就绪，点击"运行完整测试"开始', 'loading');
            }, 3000);
        });
    </script>
</body>
</html>
