#!/usr/bin/env python3
"""
量子共鸣者游戏本地服务器
用于提供静态文件和解决 PWA manifest 图标加载问题
"""

import http.server
import socketserver
import os
import sys
from pathlib import Path

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器，添加正确的MIME类型和CORS头"""
    
    def end_headers(self):
        # 添加CORS头以允许跨域请求
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        
        # 添加PWA相关的头
        if self.path.endswith('.json'):
            self.send_header('Content-Type', 'application/json')
        elif self.path.endswith('.png'):
            self.send_header('Content-Type', 'image/png')
        elif self.path.endswith('.svg'):
            self.send_header('Content-Type', 'image/svg+xml')
        elif self.path.endswith('.ico'):
            self.send_header('Content-Type', 'image/x-icon')
        
        super().end_headers()
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[服务器] {format % args}")

def start_server(port=8080, directory=None):
    """启动本地HTTP服务器"""
    if directory:
        os.chdir(directory)
    
    # 确保在正确的目录中
    current_dir = os.getcwd()
    print(f"[服务器] 当前工作目录: {current_dir}")
    
    # 检查重要文件是否存在
    required_files = ['index.html', 'manifest.json']
    for file in required_files:
        if not os.path.exists(file):
            print(f"[警告] 找不到文件: {file}")
        else:
            print(f"[确认] 找到文件: {file}")
    
    # 检查图标目录
    icon_dir = Path('assets/images')
    if icon_dir.exists():
        icons = list(icon_dir.glob('icon-*.png'))
        print(f"[确认] 找到 {len(icons)} 个图标文件")
        for icon in icons:
            print(f"  - {icon}")
    else:
        print(f"[警告] 图标目录不存在: {icon_dir}")
    
    try:
        with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
            print(f"[服务器] 量子共鸣者游戏服务器启动成功")
            print(f"[服务器] 访问地址: http://localhost:{port}")
            print(f"[服务器] 按 Ctrl+C 停止服务器")
            print("-" * 50)
            httpd.serve_forever()
    except KeyboardInterrupt:
        print(f"\n[服务器] 服务器已停止")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"[错误] 端口 {port} 已被占用，请尝试其他端口")
            print(f"[建议] 运行: python3 server.py --port 8081")
        else:
            print(f"[错误] 启动服务器失败: {e}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='量子共鸣者游戏本地服务器')
    parser.add_argument('--port', '-p', type=int, default=8080, 
                       help='服务器端口 (默认: 8080)')
    parser.add_argument('--directory', '-d', type=str, 
                       help='服务器根目录 (默认: 当前目录)')
    
    args = parser.parse_args()
    
    start_server(args.port, args.directory)
