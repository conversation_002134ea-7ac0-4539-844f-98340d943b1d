<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单初始化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: #fff;
        }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>简单初始化测试</h1>
    <div id="console-output"></div>

    <!-- 只加载 level-manager.js -->
    <script src="js/game/level-manager.js"></script>

    <script>
        // 重写 console 方法
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📋';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        // 页面加载完成后测试
        window.addEventListener('load', () => {
            console.log('🔍 开始简单测试...');
            
            // 检查 LevelManager 类
            console.log('📋 LevelManager 类:', typeof window.LevelManager);
            
            // 检查 levelManager 实例
            console.log('📋 levelManager 实例:', typeof window.levelManager);
            
            if (window.levelManager) {
                console.log('📋 levelManager 构造函数:', window.levelManager.constructor.name);
                console.log('📋 levelManager.init 方法:', typeof window.levelManager.init);
                
                // 列出所有方法
                console.log('📋 levelManager 的所有方法:');
                const proto = Object.getPrototypeOf(window.levelManager);
                const methods = Object.getOwnPropertyNames(proto);
                methods.forEach(method => {
                    if (method !== 'constructor') {
                        console.log(`  - ${method}: ${typeof proto[method]}`);
                    }
                });
                
                // 测试 init 方法
                if (typeof window.levelManager.init === 'function') {
                    console.log('✅ init 方法存在，尝试调用...');
                    try {
                        const result = window.levelManager.init();
                        console.log('✅ init 方法调用成功，返回值:', result);
                    } catch (error) {
                        console.error('❌ init 方法调用失败:', error);
                    }
                } else {
                    console.error('❌ init 方法不存在');
                    
                    // 尝试手动绑定
                    if (window.LevelManager && window.LevelManager.prototype.init) {
                        console.log('📋 尝试手动绑定 init 方法...');
                        window.levelManager.init = window.LevelManager.prototype.init.bind(window.levelManager);
                        console.log('📋 手动绑定后的类型:', typeof window.levelManager.init);
                        
                        try {
                            const result = window.levelManager.init();
                            console.log('✅ 手动绑定的 init 方法调用成功，返回值:', result);
                        } catch (error) {
                            console.error('❌ 手动绑定的 init 方法调用失败:', error);
                        }
                    }
                }
            } else {
                console.error('❌ levelManager 实例不存在');
            }
            
            console.log('🔍 测试完成');
        });
    </script>
</body>
</html>
