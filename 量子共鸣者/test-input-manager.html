<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>输入管理器测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: #fff;
        }
        
        #test-canvas {
            border: 2px solid #00ff88;
            background: #000;
            display: block;
            margin: 20px auto;
        }
        
        .info {
            text-align: center;
            margin: 20px;
        }
        
        .status {
            padding: 10px;
            margin: 10px;
            border-radius: 5px;
            background: #333;
        }
        
        .success { background: #2d5a2d; }
        .error { background: #5a2d2d; }
        .warning { background: #5a5a2d; }
    </style>
</head>
<body>
    <div class="info">
        <h1>输入管理器测试</h1>
        <p>这个页面用于测试 InputManager 类是否正常工作</p>
    </div>
    
    <canvas id="test-canvas" width="800" height="600"></canvas>
    
    <div id="status-container">
        <div class="status" id="class-status">检查 InputManager 类...</div>
        <div class="status" id="instance-status">创建实例...</div>
        <div class="status" id="init-status">初始化...</div>
        <div class="status" id="update-status">更新测试...</div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/game/input-manager.js"></script>
    
    <script>
        // 测试脚本
        console.log('🧪 开始输入管理器测试...');
        
        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }
        
        async function testInputManager() {
            try {
                // 1. 检查类是否存在
                console.log('🔍 检查 InputManager 类...');
                if (typeof InputManager === 'undefined') {
                    throw new Error('InputManager 类未定义');
                }
                updateStatus('class-status', '✅ InputManager 类已加载', 'success');
                
                // 2. 创建实例
                console.log('🎮 创建 InputManager 实例...');
                const inputManager = new InputManager();
                updateStatus('instance-status', '✅ InputManager 实例创建成功', 'success');
                
                // 3. 初始化
                console.log('🎮 初始化 InputManager...');
                const canvas = document.getElementById('test-canvas');
                inputManager.init(canvas);
                updateStatus('init-status', '✅ InputManager 初始化成功', 'success');
                
                // 4. 测试更新方法
                console.log('🎮 测试 update 方法...');
                inputManager.update(16.67); // 模拟 60fps
                updateStatus('update-status', '✅ update 方法调用成功', 'success');
                
                console.log('✅ 所有测试通过！');
                
                // 5. 持续更新测试
                let updateCount = 0;
                const updateInterval = setInterval(() => {
                    try {
                        inputManager.update(16.67);
                        updateCount++;
                        updateStatus('update-status', `✅ update 方法已调用 ${updateCount} 次`, 'success');
                        
                        if (updateCount >= 60) { // 测试1秒
                            clearInterval(updateInterval);
                            updateStatus('update-status', '✅ 持续更新测试完成 (60次)', 'success');
                        }
                    } catch (error) {
                        clearInterval(updateInterval);
                        console.error('❌ 更新测试失败:', error);
                        updateStatus('update-status', `❌ 更新失败: ${error.message}`, 'error');
                    }
                }, 16.67);
                
            } catch (error) {
                console.error('❌ 测试失败:', error);
                
                if (error.message.includes('InputManager 类未定义')) {
                    updateStatus('class-status', '❌ InputManager 类未加载', 'error');
                } else if (error.message.includes('构造函数')) {
                    updateStatus('instance-status', `❌ 实例创建失败: ${error.message}`, 'error');
                } else if (error.message.includes('初始化')) {
                    updateStatus('init-status', `❌ 初始化失败: ${error.message}`, 'error');
                } else {
                    updateStatus('update-status', `❌ 更新失败: ${error.message}`, 'error');
                }
            }
        }
        
        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', () => {
            console.log('📄 DOM 加载完成，开始测试...');
            setTimeout(testInputManager, 100); // 稍微延迟确保脚本加载完成
        });
    </script>
</body>
</html>
