/**
 * 屏幕切换修复验证脚本
 * 用于验证屏幕ID命名不一致问题的修复效果
 */

class ScreenFixVerification {
    constructor() {
        this.testResults = [];
        this.errors = [];
        this.warnings = [];
    }

    /**
     * 运行所有验证测试
     */
    async runAllTests() {
        console.log('🔧 开始屏幕切换修复验证...');
        
        // 等待系统初始化
        await this.waitForSystemInit();
        
        // 运行各项测试
        this.testScreenRegistration();
        this.testScreenElements();
        this.testScreenSwitching();
        this.testGameFlow();
        
        // 输出测试结果
        this.outputResults();
        
        return {
            success: this.errors.length === 0,
            results: this.testResults,
            errors: this.errors,
            warnings: this.warnings
        };
    }

    /**
     * 等待系统初始化
     */
    async waitForSystemInit() {
        return new Promise(resolve => {
            const checkInit = () => {
                if (window.uiManager && window.uiManager.screens) {
                    resolve();
                } else {
                    setTimeout(checkInit, 100);
                }
            };
            checkInit();
        });
    }

    /**
     * 测试屏幕注册
     */
    testScreenRegistration() {
        console.log('📱 测试屏幕注册...');
        
        if (!window.uiManager) {
            this.addError('UI管理器未找到');
            return;
        }

        const expectedScreens = [
            'loading-screen',
            'main-menu-screen', 
            'game-screen',
            'pause-screen',
            'settings-screen',
            'player-screen',
            'levelSelectScreen',
            'levelEditorScreen',
            'gameOverScreen',
            'achievementsScreen',
            'leaderboardScreen'
        ];

        const registeredScreens = Array.from(window.uiManager.screens.keys());
        
        expectedScreens.forEach(screenId => {
            if (registeredScreens.includes(screenId)) {
                this.addResult(`✅ 屏幕已注册: ${screenId}`, 'success');
            } else {
                this.addError(`❌ 屏幕未注册: ${screenId}`);
            }
        });

        // 检查是否有多余的屏幕
        registeredScreens.forEach(screenId => {
            if (!expectedScreens.includes(screenId)) {
                this.addWarning(`⚠️ 发现未预期的屏幕: ${screenId}`);
            }
        });
    }

    /**
     * 测试屏幕DOM元素
     */
    testScreenElements() {
        console.log('🏗️ 测试屏幕DOM元素...');
        
        const screenIds = [
            'loading-screen',
            'main-menu-screen',
            'game-screen', 
            'pause-screen',
            'settings-screen',
            'player-screen',
            'levelSelectScreen',
            'levelEditorScreen',
            'gameOverScreen',
            'achievementsScreen',
            'leaderboardScreen'
        ];

        screenIds.forEach(screenId => {
            const element = document.getElementById(screenId);
            if (element) {
                this.addResult(`✅ DOM元素存在: ${screenId}`, 'success');
                
                // 检查CSS类
                if (element.classList.contains('screen')) {
                    this.addResult(`✅ CSS类正确: ${screenId}`, 'success');
                } else {
                    this.addWarning(`⚠️ 缺少screen类: ${screenId}`);
                }
            } else {
                this.addError(`❌ DOM元素不存在: ${screenId}`);
            }
        });
    }

    /**
     * 测试屏幕切换
     */
    testScreenSwitching() {
        console.log('🔄 测试屏幕切换...');
        
        if (!window.uiManager) {
            this.addError('UI管理器未找到，无法测试屏幕切换');
            return;
        }

        const testScreens = [
            'main-menu-screen',
            'game-screen',
            'settings-screen'
        ];

        testScreens.forEach(screenId => {
            try {
                // 尝试切换屏幕
                window.uiManager.showScreen(screenId);
                this.addResult(`✅ 屏幕切换成功: ${screenId}`, 'success');
            } catch (error) {
                this.addError(`❌ 屏幕切换失败: ${screenId} - ${error.message}`);
            }
        });
    }

    /**
     * 测试游戏流程
     */
    testGameFlow() {
        console.log('🎮 测试游戏流程...');
        
        // 测试开始游戏流程
        if (window.gameApp && typeof window.gameApp.startGame === 'function') {
            try {
                // 注意：这里只是测试方法调用，不实际启动游戏
                this.addResult('✅ 开始游戏方法可调用', 'success');
            } catch (error) {
                this.addError(`❌ 开始游戏方法调用失败: ${error.message}`);
            }
        } else {
            this.addWarning('⚠️ 游戏应用或开始游戏方法未找到');
        }

        // 测试关卡选择
        if (window.levelSelect && typeof window.levelSelect.show === 'function') {
            this.addResult('✅ 关卡选择组件可用', 'success');
        } else {
            this.addWarning('⚠️ 关卡选择组件未找到');
        }
    }

    /**
     * 添加测试结果
     */
    addResult(message, type = 'info') {
        this.testResults.push({ message, type, timestamp: Date.now() });
        console.log(message);
    }

    /**
     * 添加错误
     */
    addError(message) {
        this.errors.push(message);
        this.addResult(message, 'error');
    }

    /**
     * 添加警告
     */
    addWarning(message) {
        this.warnings.push(message);
        this.addResult(message, 'warning');
    }

    /**
     * 输出测试结果
     */
    outputResults() {
        console.log('\n📊 屏幕切换修复验证结果:');
        console.log(`✅ 成功: ${this.testResults.filter(r => r.type === 'success').length}`);
        console.log(`⚠️ 警告: ${this.warnings.length}`);
        console.log(`❌ 错误: ${this.errors.length}`);
        
        if (this.errors.length === 0) {
            console.log('🎉 所有测试通过！屏幕切换修复成功！');
        } else {
            console.log('💥 发现错误，需要进一步修复');
            this.errors.forEach(error => console.error(error));
        }
    }
}

// 创建全局实例
window.screenFixVerification = new ScreenFixVerification();

// 页面加载完成后自动运行验证
document.addEventListener('DOMContentLoaded', () => {
    // 延迟执行，确保所有系统都已初始化
    setTimeout(() => {
        if (window.screenFixVerification) {
            window.screenFixVerification.runAllTests();
        }
    }, 3000);
});

console.log('🔧 屏幕切换修复验证脚本已加载');
