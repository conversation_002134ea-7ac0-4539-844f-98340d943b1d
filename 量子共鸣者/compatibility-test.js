/**
 * 量子共鸣者 - 跨平台兼容性测试脚本
 * 检测浏览器和设备兼容性，提供降级方案建议
 */

class CompatibilityTest {
    constructor() {
        this.features = {
            webAudio: false,
            webGL: false,
            canvas2D: false,
            localStorage: false,
            indexedDB: false,
            touchEvents: false,
            deviceOrientation: false,
            gamepad: false,
            webWorkers: false,
            requestAnimationFrame: false
        };
        
        this.deviceInfo = {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            screenWidth: screen.width,
            screenHeight: screen.height,
            colorDepth: screen.colorDepth,
            pixelRatio: window.devicePixelRatio || 1
        };
        
        this.browserInfo = this.detectBrowser();
        
        console.log('🔍 兼容性测试器已创建');
    }

    /**
     * 运行完整的兼容性测试
     */
    runCompatibilityTest() {
        console.log('='.repeat(50));
        console.log('🔍 跨平台兼容性测试开始');
        console.log('='.repeat(50));
        
        // 检测设备信息
        this.logDeviceInfo();
        
        // 检测浏览器信息
        this.logBrowserInfo();
        
        // 测试各项功能
        this.testWebAudio();
        this.testWebGL();
        this.testCanvas2D();
        this.testLocalStorage();
        this.testIndexedDB();
        this.testTouchEvents();
        this.testDeviceOrientation();
        this.testGamepad();
        this.testWebWorkers();
        this.testRequestAnimationFrame();
        
        // 生成兼容性报告
        this.generateCompatibilityReport();
        
        console.log('='.repeat(50));
        console.log('🔍 兼容性测试完成');
        console.log('='.repeat(50));
    }

    /**
     * 检测浏览器类型和版本
     */
    detectBrowser() {
        const ua = navigator.userAgent;
        let browser = { name: 'Unknown', version: 'Unknown' };
        
        if (ua.includes('Chrome') && !ua.includes('Edg')) {
            browser.name = 'Chrome';
            browser.version = ua.match(/Chrome\/(\d+)/)?.[1] || 'Unknown';
        } else if (ua.includes('Firefox')) {
            browser.name = 'Firefox';
            browser.version = ua.match(/Firefox\/(\d+)/)?.[1] || 'Unknown';
        } else if (ua.includes('Safari') && !ua.includes('Chrome')) {
            browser.name = 'Safari';
            browser.version = ua.match(/Version\/(\d+)/)?.[1] || 'Unknown';
        } else if (ua.includes('Edg')) {
            browser.name = 'Edge';
            browser.version = ua.match(/Edg\/(\d+)/)?.[1] || 'Unknown';
        } else if (ua.includes('Opera') || ua.includes('OPR')) {
            browser.name = 'Opera';
            browser.version = ua.match(/(?:Opera|OPR)\/(\d+)/)?.[1] || 'Unknown';
        }
        
        return browser;
    }

    /**
     * 输出设备信息
     */
    logDeviceInfo() {
        console.log('\n📱 设备信息:');
        console.log(`   平台: ${this.deviceInfo.platform}`);
        console.log(`   屏幕分辨率: ${this.deviceInfo.screenWidth}x${this.deviceInfo.screenHeight}`);
        console.log(`   像素比: ${this.deviceInfo.pixelRatio}`);
        console.log(`   颜色深度: ${this.deviceInfo.colorDepth}位`);
        console.log(`   语言: ${this.deviceInfo.language}`);
        console.log(`   在线状态: ${this.deviceInfo.onLine ? '在线' : '离线'}`);
    }

    /**
     * 输出浏览器信息
     */
    logBrowserInfo() {
        console.log('\n🌐 浏览器信息:');
        console.log(`   浏览器: ${this.browserInfo.name} ${this.browserInfo.version}`);
        console.log(`   Cookie支持: ${this.deviceInfo.cookieEnabled ? '是' : '否'}`);
        console.log(`   User Agent: ${this.deviceInfo.userAgent}`);
    }

    /**
     * 测试Web Audio API支持
     */
    testWebAudio() {
        try {
            const AudioContext = window.AudioContext || window.webkitAudioContext;
            if (AudioContext) {
                const context = new AudioContext();
                this.features.webAudio = true;
                context.close();
                console.log('✅ Web Audio API: 支持');
            } else {
                console.log('❌ Web Audio API: 不支持');
            }
        } catch (error) {
            console.log('❌ Web Audio API: 不支持 -', error.message);
        }
    }

    /**
     * 测试WebGL支持
     */
    testWebGL() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            if (gl) {
                this.features.webGL = true;
                console.log('✅ WebGL: 支持');
                
                // 获取WebGL信息
                const renderer = gl.getParameter(gl.RENDERER);
                const vendor = gl.getParameter(gl.VENDOR);
                console.log(`   渲染器: ${renderer}`);
                console.log(`   供应商: ${vendor}`);
            } else {
                console.log('❌ WebGL: 不支持');
            }
        } catch (error) {
            console.log('❌ WebGL: 不支持 -', error.message);
        }
    }

    /**
     * 测试Canvas 2D支持
     */
    testCanvas2D() {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            if (ctx) {
                this.features.canvas2D = true;
                console.log('✅ Canvas 2D: 支持');
            } else {
                console.log('❌ Canvas 2D: 不支持');
            }
        } catch (error) {
            console.log('❌ Canvas 2D: 不支持 -', error.message);
        }
    }

    /**
     * 测试localStorage支持
     */
    testLocalStorage() {
        try {
            if (typeof Storage !== 'undefined' && window.localStorage) {
                // 测试读写
                localStorage.setItem('test', 'value');
                const value = localStorage.getItem('test');
                localStorage.removeItem('test');
                
                if (value === 'value') {
                    this.features.localStorage = true;
                    console.log('✅ localStorage: 支持');
                } else {
                    console.log('❌ localStorage: 读写异常');
                }
            } else {
                console.log('❌ localStorage: 不支持');
            }
        } catch (error) {
            console.log('❌ localStorage: 不支持 -', error.message);
        }
    }

    /**
     * 测试IndexedDB支持
     */
    testIndexedDB() {
        try {
            if (window.indexedDB) {
                this.features.indexedDB = true;
                console.log('✅ IndexedDB: 支持');
            } else {
                console.log('❌ IndexedDB: 不支持');
            }
        } catch (error) {
            console.log('❌ IndexedDB: 不支持 -', error.message);
        }
    }

    /**
     * 测试触摸事件支持
     */
    testTouchEvents() {
        try {
            if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
                this.features.touchEvents = true;
                console.log('✅ 触摸事件: 支持');
                console.log(`   最大触摸点: ${navigator.maxTouchPoints || '未知'}`);
            } else {
                console.log('❌ 触摸事件: 不支持');
            }
        } catch (error) {
            console.log('❌ 触摸事件: 检测异常 -', error.message);
        }
    }

    /**
     * 测试设备方向支持
     */
    testDeviceOrientation() {
        try {
            if ('DeviceOrientationEvent' in window) {
                this.features.deviceOrientation = true;
                console.log('✅ 设备方向: 支持');
            } else {
                console.log('❌ 设备方向: 不支持');
            }
        } catch (error) {
            console.log('❌ 设备方向: 检测异常 -', error.message);
        }
    }

    /**
     * 测试游戏手柄支持
     */
    testGamepad() {
        try {
            if ('getGamepads' in navigator) {
                this.features.gamepad = true;
                console.log('✅ 游戏手柄: 支持');
            } else {
                console.log('❌ 游戏手柄: 不支持');
            }
        } catch (error) {
            console.log('❌ 游戏手柄: 检测异常 -', error.message);
        }
    }

    /**
     * 测试Web Workers支持
     */
    testWebWorkers() {
        try {
            if (typeof Worker !== 'undefined') {
                this.features.webWorkers = true;
                console.log('✅ Web Workers: 支持');
            } else {
                console.log('❌ Web Workers: 不支持');
            }
        } catch (error) {
            console.log('❌ Web Workers: 检测异常 -', error.message);
        }
    }

    /**
     * 测试requestAnimationFrame支持
     */
    testRequestAnimationFrame() {
        try {
            if (window.requestAnimationFrame) {
                this.features.requestAnimationFrame = true;
                console.log('✅ requestAnimationFrame: 支持');
            } else {
                console.log('❌ requestAnimationFrame: 不支持');
            }
        } catch (error) {
            console.log('❌ requestAnimationFrame: 检测异常 -', error.message);
        }
    }

    /**
     * 生成兼容性报告
     */
    generateCompatibilityReport() {
        console.log('\n📊 兼容性报告:');
        
        const supportedFeatures = Object.values(this.features).filter(Boolean).length;
        const totalFeatures = Object.keys(this.features).length;
        const compatibilityScore = (supportedFeatures / totalFeatures) * 100;
        
        console.log(`   兼容性评分: ${compatibilityScore.toFixed(1)}% (${supportedFeatures}/${totalFeatures})`);
        
        // 评估游戏可玩性
        this.evaluateGamePlayability();
        
        // 提供降级建议
        this.provideFallbackSuggestions();
    }

    /**
     * 评估游戏可玩性
     */
    evaluateGamePlayability() {
        const criticalFeatures = ['webAudio', 'canvas2D', 'localStorage', 'requestAnimationFrame'];
        const missingCritical = criticalFeatures.filter(feature => !this.features[feature]);
        
        console.log('\n🎮 游戏可玩性评估:');
        
        if (missingCritical.length === 0) {
            console.log('   ✅ 完全兼容 - 所有核心功能都支持');
        } else if (missingCritical.length <= 1) {
            console.log('   ⚠️ 基本兼容 - 可能需要降级某些功能');
            console.log(`   缺失功能: ${missingCritical.join(', ')}`);
        } else {
            console.log('   ❌ 兼容性差 - 多个核心功能不支持');
            console.log(`   缺失功能: ${missingCritical.join(', ')}`);
        }
    }

    /**
     * 提供降级建议
     */
    provideFallbackSuggestions() {
        console.log('\n💡 降级方案建议:');
        
        const suggestions = [];
        
        if (!this.features.webAudio) {
            suggestions.push('使用HTML5 Audio元素替代Web Audio API');
        }
        
        if (!this.features.webGL) {
            suggestions.push('使用Canvas 2D渲染替代WebGL');
        }
        
        if (!this.features.localStorage) {
            suggestions.push('使用内存存储替代localStorage');
        }
        
        if (!this.features.indexedDB) {
            suggestions.push('使用localStorage替代IndexedDB');
        }
        
        if (!this.features.touchEvents) {
            suggestions.push('仅支持鼠标操作，禁用触摸功能');
        }
        
        if (!this.features.requestAnimationFrame) {
            suggestions.push('使用setTimeout替代requestAnimationFrame');
        }
        
        if (suggestions.length === 0) {
            console.log('   🎉 无需降级，所有功能都支持！');
        } else {
            suggestions.forEach((suggestion, index) => {
                console.log(`   ${index + 1}. ${suggestion}`);
            });
        }
    }

    /**
     * 导出兼容性报告
     */
    exportReport() {
        const report = {
            timestamp: new Date().toISOString(),
            deviceInfo: this.deviceInfo,
            browserInfo: this.browserInfo,
            features: this.features,
            compatibilityScore: (Object.values(this.features).filter(Boolean).length / Object.keys(this.features).length) * 100
        };
        
        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `compatibility-report-${Date.now()}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        
        console.log('📁 兼容性报告已导出');
    }

    /**
     * 检测移动设备
     */
    isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    /**
     * 检测平板设备
     */
    isTabletDevice() {
        return /iPad|Android(?!.*Mobile)/i.test(navigator.userAgent);
    }

    /**
     * 获取设备类型
     */
    getDeviceType() {
        if (this.isMobileDevice()) {
            return this.isTabletDevice() ? 'tablet' : 'mobile';
        }
        return 'desktop';
    }
}

// 创建全局兼容性测试实例
window.compatibilityTest = new CompatibilityTest();

// 添加快捷方法到控制台
window.runCompatibilityTest = () => compatibilityTest.runCompatibilityTest();
window.exportCompatibilityReport = () => compatibilityTest.exportReport();

console.log('🔍 兼容性测试器已加载');
console.log('💡 使用 runCompatibilityTest() 开始测试');
console.log('💡 使用 exportCompatibilityReport() 导出报告');

// 自动运行兼容性测试
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        compatibilityTest.runCompatibilityTest();
    }, 1000);
});
