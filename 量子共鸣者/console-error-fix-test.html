<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制台错误修复测试 - 量子共鸣者</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0f0f1e;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .test-section {
            background: #1a1a2e;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 4px solid #4a90e2;
        }
        
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .test-result.success {
            background: #2d5a2d;
            color: #90ee90;
        }
        
        .test-result.error {
            background: #5a2d2d;
            color: #ff6b6b;
        }
        
        .test-result.warning {
            background: #5a5a2d;
            color: #ffff90;
        }
        
        .test-result.info {
            background: #2d4a5a;
            color: #87ceeb;
        }
        
        .test-button {
            background: #4a90e2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #357abd;
        }
        
        .canvas-container {
            width: 100%;
            height: 300px;
            border: 2px solid #333;
            margin: 10px 0;
            position: relative;
            background: #0f0f1e;
        }
        
        #test-canvas {
            width: 100%;
            height: 100%;
            display: block;
        }
        
        .console-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .icon-test {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 10px 0;
        }
        
        .icon-item {
            text-align: center;
            padding: 10px;
            background: #2a2a3e;
            border-radius: 5px;
        }
        
        .icon-item img {
            width: 48px;
            height: 48px;
            border: 1px solid #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 控制台错误修复测试</h1>
        <p>测试并验证控制台错误的修复情况</p>
        
        <!-- 渲染引擎测试 -->
        <div class="test-section">
            <h2>🎨 渲染引擎测试</h2>
            <div id="render-test-results"></div>
            
            <div class="canvas-container">
                <canvas id="test-canvas"></canvas>
            </div>
            
            <button class="test-button" onclick="testRenderEngine()">测试渲染引擎初始化</button>
            <button class="test-button" onclick="testCanvasOperations()">测试画布操作</button>
        </div>
        
        <!-- 图标加载测试 -->
        <div class="test-section">
            <h2>🖼️ 图标加载测试</h2>
            <div id="icon-test-results"></div>
            
            <div class="icon-test" id="icon-test-container">
                <!-- 图标将动态加载到这里 -->
            </div>
            
            <button class="test-button" onclick="testIconLoading()">测试图标加载</button>
            <button class="test-button" onclick="generateMissingIcons()">生成缺失图标</button>
        </div>
        
        <!-- 系统初始化测试 -->
        <div class="test-section">
            <h2>⚙️ 系统初始化测试</h2>
            <div id="system-test-results"></div>
            
            <button class="test-button" onclick="testSystemInitialization()">测试系统初始化</button>
            <button class="test-button" onclick="simulateMainAppInit()">模拟主应用初始化</button>
        </div>
        
        <!-- 控制台输出监控 -->
        <div class="test-section">
            <h2>📊 控制台输出监控</h2>
            <div class="console-output" id="console-output"></div>
            <button class="test-button" onclick="clearConsoleOutput()">清空输出</button>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/core/render-engine.js"></script>
    
    <script>
        let consoleOutput = document.getElementById('console-output');
        let originalConsole = {};
        
        // 劫持控制台输出
        function setupConsoleCapture() {
            ['log', 'error', 'warn', 'info'].forEach(method => {
                originalConsole[method] = console[method];
                console[method] = function(...args) {
                    // 调用原始方法
                    originalConsole[method].apply(console, args);
                    
                    // 添加到我们的输出
                    const timestamp = new Date().toLocaleTimeString();
                    const message = args.map(arg => 
                        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                    ).join(' ');
                    
                    const color = {
                        log: '#0f0',
                        error: '#f00',
                        warn: '#ff0',
                        info: '#0ff'
                    }[method] || '#0f0';
                    
                    consoleOutput.innerHTML += `<div style="color: ${color}">[${timestamp}] ${method.toUpperCase()}: ${message}</div>`;
                    consoleOutput.scrollTop = consoleOutput.scrollHeight;
                };
            });
        }
        
        // 添加测试结果
        function addTestResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            container.appendChild(resultDiv);
        }
        
        // 测试渲染引擎
        async function testRenderEngine() {
            const containerId = 'render-test-results';
            document.getElementById(containerId).innerHTML = '';
            
            try {
                addTestResult(containerId, '🎨 开始测试渲染引擎...', 'info');
                
                // 检查RenderEngine类是否存在
                if (typeof RenderEngine === 'undefined') {
                    throw new Error('RenderEngine类未找到');
                }
                addTestResult(containerId, '✅ RenderEngine类已找到', 'success');
                
                // 创建渲染引擎实例
                const renderEngine = new RenderEngine();
                addTestResult(containerId, '✅ RenderEngine实例创建成功', 'success');
                
                // 获取测试画布
                const canvas = document.getElementById('test-canvas');
                if (!canvas) {
                    throw new Error('测试画布未找到');
                }
                addTestResult(containerId, '✅ 测试画布已找到', 'success');
                
                // 初始化渲染引擎
                const initResult = await renderEngine.init(canvas);
                if (initResult === false) {
                    throw new Error('渲染引擎初始化返回false');
                }
                addTestResult(containerId, '✅ 渲染引擎初始化成功', 'success');
                
                // 测试基本属性
                addTestResult(containerId, `📊 画布尺寸: ${renderEngine.width}x${renderEngine.height}`, 'info');
                addTestResult(containerId, `🖥️ 设备像素比: ${renderEngine.pixelRatio}`, 'info');
                addTestResult(containerId, `🎮 渲染模式: ${renderEngine.renderMode}`, 'info');
                addTestResult(containerId, `🌐 WebGL支持: ${renderEngine.webglSupported ? '是' : '否'}`, 'info');
                
                // 测试基本绘制
                renderEngine.clear();
                const ctx = renderEngine.ctx;
                ctx.fillStyle = '#4a90e2';
                ctx.fillRect(10, 10, 100, 50);
                ctx.fillStyle = '#fff';
                ctx.font = '16px Arial';
                ctx.fillText('渲染测试成功！', 10, 80);
                
                addTestResult(containerId, '✅ 所有渲染引擎测试通过！', 'success');
                
            } catch (error) {
                addTestResult(containerId, `❌ 渲染引擎测试失败: ${error.message}`, 'error');
                console.error('渲染引擎测试错误:', error);
            }
        }
        
        // 测试画布操作
        function testCanvasOperations() {
            const containerId = 'render-test-results';
            
            try {
                addTestResult(containerId, '🎯 开始测试画布操作...', 'info');
                
                const canvas = document.getElementById('test-canvas');
                const ctx = canvas.getContext('2d');
                
                if (!ctx) {
                    throw new Error('无法获取2D渲染上下文');
                }
                
                // 清空画布
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制测试图案
                ctx.fillStyle = '#1a1a2e';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 绘制量子粒子效果
                for (let i = 0; i < 20; i++) {
                    const x = Math.random() * canvas.width;
                    const y = Math.random() * canvas.height;
                    const size = Math.random() * 5 + 2;
                    const hue = Math.random() * 360;
                    
                    ctx.fillStyle = `hsl(${hue}, 70%, 60%)`;
                    ctx.beginPath();
                    ctx.arc(x, y, size, 0, Math.PI * 2);
                    ctx.fill();
                }
                
                addTestResult(containerId, '✅ 画布操作测试成功', 'success');
                
            } catch (error) {
                addTestResult(containerId, `❌ 画布操作测试失败: ${error.message}`, 'error');
                console.error('画布操作测试错误:', error);
            }
        }
        
        // 测试图标加载
        function testIconLoading() {
            const containerId = 'icon-test-results';
            const iconContainer = document.getElementById('icon-test-container');
            
            document.getElementById(containerId).innerHTML = '';
            iconContainer.innerHTML = '';
            
            addTestResult(containerId, '🖼️ 开始测试图标加载...', 'info');
            
            const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];
            let loadedCount = 0;
            let errorCount = 0;
            
            iconSizes.forEach(size => {
                const iconPath = `assets/images/icon-${size}x${size}.png`;
                const img = new Image();
                
                img.onload = function() {
                    loadedCount++;
                    
                    // 创建图标显示项
                    const iconItem = document.createElement('div');
                    iconItem.className = 'icon-item';
                    iconItem.innerHTML = `
                        <img src="${iconPath}" alt="${size}x${size}">
                        <div>${size}x${size}</div>
                        <div style="color: #90ee90;">✅</div>
                    `;
                    iconContainer.appendChild(iconItem);
                    
                    if (loadedCount + errorCount === iconSizes.length) {
                        addTestResult(containerId, `✅ 图标加载完成: ${loadedCount}个成功, ${errorCount}个失败`, 
                                    errorCount === 0 ? 'success' : 'warning');
                    }
                };
                
                img.onerror = function() {
                    errorCount++;
                    
                    // 创建错误图标显示项
                    const iconItem = document.createElement('div');
                    iconItem.className = 'icon-item';
                    iconItem.innerHTML = `
                        <div style="width: 48px; height: 48px; background: #5a2d2d; display: flex; align-items: center; justify-content: center; border: 1px solid #333;">❌</div>
                        <div>${size}x${size}</div>
                        <div style="color: #ff6b6b;">失败</div>
                    `;
                    iconContainer.appendChild(iconItem);
                    
                    addTestResult(containerId, `❌ 图标加载失败: ${iconPath}`, 'error');
                    
                    if (loadedCount + errorCount === iconSizes.length) {
                        addTestResult(containerId, `✅ 图标加载完成: ${loadedCount}个成功, ${errorCount}个失败`, 
                                    errorCount === 0 ? 'success' : 'warning');
                    }
                };
                
                img.src = iconPath;
            });
        }
        
        // 生成缺失图标
        function generateMissingIcons() {
            const containerId = 'icon-test-results';
            addTestResult(containerId, '🎨 打开图标生成器...', 'info');
            window.open('generate-icons.html', '_blank');
        }
        
        // 测试系统初始化
        async function testSystemInitialization() {
            const containerId = 'system-test-results';
            document.getElementById(containerId).innerHTML = '';
            
            addTestResult(containerId, '⚙️ 开始测试系统初始化...', 'info');
            
            // 检查必要的类是否存在
            const requiredClasses = [
                'RenderEngine',
                // 其他系统类可以在这里添加
            ];
            
            let missingClasses = [];
            
            requiredClasses.forEach(className => {
                if (typeof window[className] !== 'undefined') {
                    addTestResult(containerId, `✅ ${className} 类已找到`, 'success');
                } else {
                    addTestResult(containerId, `❌ ${className} 类未找到`, 'error');
                    missingClasses.push(className);
                }
            });
            
            if (missingClasses.length === 0) {
                addTestResult(containerId, '✅ 所有必要的系统类都已找到', 'success');
            } else {
                addTestResult(containerId, `⚠️ 缺少 ${missingClasses.length} 个系统类`, 'warning');
            }
        }
        
        // 模拟主应用初始化
        async function simulateMainAppInit() {
            const containerId = 'system-test-results';
            
            try {
                addTestResult(containerId, '🚀 开始模拟主应用初始化...', 'info');
                
                // 模拟渲染系统初始化（修复后的版本）
                if (typeof RenderEngine !== 'undefined') {
                    const renderEngine = new RenderEngine();
                    const canvas = document.getElementById('test-canvas');
                    
                    if (canvas) {
                        const initResult = await renderEngine.init(canvas);
                        if (initResult !== false) {
                            addTestResult(containerId, '✅ 渲染系统初始化成功（修复后）', 'success');
                        } else {
                            addTestResult(containerId, '❌ 渲染系统初始化失败', 'error');
                        }
                    } else {
                        addTestResult(containerId, '❌ 画布元素未找到', 'error');
                    }
                } else {
                    addTestResult(containerId, '❌ RenderEngine类未找到', 'error');
                }
                
                addTestResult(containerId, '✅ 主应用初始化模拟完成', 'success');
                
            } catch (error) {
                addTestResult(containerId, `❌ 主应用初始化模拟失败: ${error.message}`, 'error');
                console.error('主应用初始化模拟错误:', error);
            }
        }
        
        // 清空控制台输出
        function clearConsoleOutput() {
            consoleOutput.innerHTML = '';
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupConsoleCapture();
            console.log('🔧 控制台错误修复测试页面已加载');
            console.log('📊 控制台输出监控已启动');
            
            // 自动运行一些基础测试
            setTimeout(() => {
                testSystemInitialization();
            }, 1000);
        });
    </script>
</body>
</html>
