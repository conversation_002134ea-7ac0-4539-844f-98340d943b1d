<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量子共鸣者 - 测试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: #ffffff;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .test-output {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background-color: #4CAF50; }
        .status-error { background-color: #f44336; }
        .status-warning { background-color: #ff9800; }
        .status-info { background-color: #2196F3; }
        
        #gameCanvas {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.5);
        }
        
        .canvas-container {
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🌌 量子共鸣者 - 游戏引擎测试</h1>
            <p>测试各个模块的功能和集成</p>
        </div>

        <!-- 基础模块测试 -->
        <div class="test-section">
            <h2>📦 基础模块测试</h2>
            <button class="test-button" onclick="testStorageService()">测试存储服务</button>
            <button class="test-button" onclick="testI18nService()">测试国际化</button>
            <button class="test-button" onclick="testMathUtils()">测试数学工具</button>
            <div id="basicModulesOutput" class="test-output"></div>
        </div>

        <!-- 核心引擎测试 -->
        <div class="test-section">
            <h2>⚛️ 核心引擎测试</h2>
            <button class="test-button" onclick="testAudioEngine()">测试音频引擎</button>
            <button class="test-button" onclick="testPhysicsEngine()">测试物理引擎</button>
            <button class="test-button" onclick="testQuantumEngine()">测试量子引擎</button>
            <button class="test-button" onclick="testRenderEngine()">测试渲染引擎</button>
            <div id="coreEnginesOutput" class="test-output"></div>
        </div>

        <!-- 游戏系统测试 -->
        <div class="test-section">
            <h2>🎮 游戏系统测试</h2>
            <button class="test-button" onclick="testGameController()">测试游戏控制器</button>
            <button class="test-button" onclick="testInputManager()">测试输入管理器</button>
            <button class="test-button" onclick="testLevelSystem()">测试关卡系统</button>
            <div id="gameSystemsOutput" class="test-output"></div>
        </div>

        <!-- 集成测试 -->
        <div class="test-section">
            <h2>🔗 集成测试</h2>
            <button class="test-button" onclick="testFullIntegration()">完整集成测试</button>
            <button class="test-button" onclick="startMiniGame()">启动迷你游戏</button>
            <button class="test-button" onclick="clearCanvas()">清空画布</button>
            <div id="integrationOutput" class="test-output"></div>
            
            <div class="canvas-container">
                <canvas id="gameCanvas" width="800" height="600"></canvas>
            </div>
        </div>

        <!-- 性能测试 -->
        <div class="test-section">
            <h2>📊 性能测试</h2>
            <button class="test-button" onclick="testPerformance()">性能基准测试</button>
            <button class="test-button" onclick="stressTest()">压力测试</button>
            <div id="performanceOutput" class="test-output"></div>
        </div>
    </div>

    <!-- 引入所有必要的脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/utils/math-utils.js"></script>
    <script src="js/core/audio-engine.js"></script>
    <script src="js/core/physics-engine.js"></script>
    <script src="js/core/quantum-engine.js"></script>
    <script src="js/core/render-engine.js"></script>
    <script src="js/game/input-manager.js"></script>
    <script src="js/game/level.js"></script>
    <script src="js/game/game-controller.js"></script>

    <script>
        // 测试工具函数
        function log(message, type = 'info', outputId = 'basicModulesOutput') {
            const output = document.getElementById(outputId);
            const statusClass = `status-${type}`;
            const timestamp = new Date().toLocaleTimeString();
            
            output.innerHTML += `
                <div>
                    <span class="status-indicator ${statusClass}"></span>
                    [${timestamp}] ${message}
                </div>
            `;
            output.scrollTop = output.scrollHeight;
        }

        // 基础模块测试
        async function testStorageService() {
            log('开始测试存储服务...', 'info', 'basicModulesOutput');
            
            try {
                await storageService.init();
                log('✅ 存储服务初始化成功', 'success', 'basicModulesOutput');
                
                // 测试存储和读取
                await storageService.put('test.key', { value: 'test data', timestamp: Date.now() });
                const data = await storageService.get('test.key');
                
                if (data && data.value === 'test data') {
                    log('✅ 数据存储和读取测试通过', 'success', 'basicModulesOutput');
                } else {
                    log('❌ 数据存储和读取测试失败', 'error', 'basicModulesOutput');
                }
                
                // 测试删除
                await storageService.delete('test.key');
                const deletedData = await storageService.get('test.key');
                
                if (!deletedData) {
                    log('✅ 数据删除测试通过', 'success', 'basicModulesOutput');
                } else {
                    log('❌ 数据删除测试失败', 'error', 'basicModulesOutput');
                }
                
            } catch (error) {
                log(`❌ 存储服务测试失败: ${error.message}`, 'error', 'basicModulesOutput');
            }
        }

        async function testI18nService() {
            log('开始测试国际化服务...', 'info', 'basicModulesOutput');
            
            try {
                await i18n.init();
                log('✅ 国际化服务初始化成功', 'success', 'basicModulesOutput');
                
                // 测试翻译
                const chineseText = i18n.t('game.title');
                log(`中文标题: ${chineseText}`, 'info', 'basicModulesOutput');
                
                // 切换语言
                i18n.setLanguage('en');
                const englishText = i18n.t('game.title');
                log(`英文标题: ${englishText}`, 'info', 'basicModulesOutput');
                
                // 切换回中文
                i18n.setLanguage('zh');
                log('✅ 语言切换测试通过', 'success', 'basicModulesOutput');
                
            } catch (error) {
                log(`❌ 国际化服务测试失败: ${error.message}`, 'error', 'basicModulesOutput');
            }
        }

        function testMathUtils() {
            log('开始测试数学工具...', 'info', 'basicModulesOutput');
            
            try {
                // 测试基本数学函数
                const lerp = MathUtils.lerp(0, 100, 0.5);
                log(`线性插值测试: lerp(0, 100, 0.5) = ${lerp}`, 'info', 'basicModulesOutput');
                
                const distance = MathUtils.distance(0, 0, 3, 4);
                log(`距离计算测试: distance(0,0,3,4) = ${distance}`, 'info', 'basicModulesOutput');
                
                const clamped = MathUtils.clamp(150, 0, 100);
                log(`数值限制测试: clamp(150, 0, 100) = ${clamped}`, 'info', 'basicModulesOutput');
                
                // 测试音频相关函数
                const wavelength = MathUtils.frequencyToWavelength(440);
                log(`频率转波长测试: 440Hz = ${wavelength.toFixed(2)}m`, 'info', 'basicModulesOutput');
                
                log('✅ 数学工具测试通过', 'success', 'basicModulesOutput');
                
            } catch (error) {
                log(`❌ 数学工具测试失败: ${error.message}`, 'error', 'basicModulesOutput');
            }
        }

        // 核心引擎测试
        async function testAudioEngine() {
            log('开始测试音频引擎...', 'info', 'coreEnginesOutput');
            
            try {
                if (!audioEngine.isReady()) {
                    log('⚠️ 音频引擎需要用户交互才能初始化', 'warning', 'coreEnginesOutput');
                    log('请点击页面任意位置后重试', 'info', 'coreEnginesOutput');
                    
                    // 添加一次性点击监听器
                    document.addEventListener('click', async function initAudio() {
                        document.removeEventListener('click', initAudio);
                        await audioEngine.init();
                        log('✅ 音频引擎初始化成功', 'success', 'coreEnginesOutput');
                        
                        // 测试音频功能
                        audioEngine.playResonanceSound(440, 0.5);
                        log('🎵 播放共鸣音效测试', 'info', 'coreEnginesOutput');
                    }, { once: true });
                } else {
                    log('✅ 音频引擎已就绪', 'success', 'coreEnginesOutput');
                }
                
            } catch (error) {
                log(`❌ 音频引擎测试失败: ${error.message}`, 'error', 'coreEnginesOutput');
            }
        }

        function testPhysicsEngine() {
            log('开始测试物理引擎...', 'info', 'coreEnginesOutput');
            
            try {
                // 清空现有粒子
                physicsEngine.clear();
                
                // 创建测试粒子
                const particle1 = physicsEngine.createParticle({
                    x: 100, y: 100, radius: 10, mass: 1, frequency: 440
                });
                
                const particle2 = physicsEngine.createParticle({
                    x: 200, y: 100, radius: 10, mass: 1, frequency: 440
                });
                
                log(`✅ 创建了 ${physicsEngine.particles.length} 个测试粒子`, 'success', 'coreEnginesOutput');
                
                // 测试物理更新
                physicsEngine.update(0.016); // 60 FPS
                log('✅ 物理引擎更新测试通过', 'success', 'coreEnginesOutput');
                
                // 测试碰撞检测
                const collisions = physicsEngine.detectCollisions();
                log(`碰撞检测结果: ${collisions.length} 个碰撞`, 'info', 'coreEnginesOutput');
                
            } catch (error) {
                log(`❌ 物理引擎测试失败: ${error.message}`, 'error', 'coreEnginesOutput');
            }
        }

        function testQuantumEngine() {
            log('开始测试量子引擎...', 'info', 'coreEnginesOutput');
            
            try {
                quantumEngine.init();
                log('✅ 量子引擎初始化成功', 'success', 'coreEnginesOutput');
                
                // 测试频率设置
                quantumEngine.setTargetFrequency(440);
                log(`目标频率设置为: ${quantumEngine.targetFrequency} Hz`, 'info', 'coreEnginesOutput');
                
                // 测试粒子激活
                if (physicsEngine.particles.length > 0) {
                    const particle = physicsEngine.particles[0];
                    const success = quantumEngine.activateParticle(particle, 440);
                    log(`粒子激活测试: ${success ? '成功' : '失败'}`, success ? 'success' : 'warning', 'coreEnginesOutput');
                }
                
                // 测试量子引擎更新
                quantumEngine.update(0.016);
                log('✅ 量子引擎更新测试通过', 'success', 'coreEnginesOutput');
                
            } catch (error) {
                log(`❌ 量子引擎测试失败: ${error.message}`, 'error', 'coreEnginesOutput');
            }
        }

        function testRenderEngine() {
            log('开始测试渲染引擎...', 'info', 'coreEnginesOutput');
            
            try {
                const canvas = document.getElementById('gameCanvas');
                const success = renderEngine.init(canvas);
                
                if (success) {
                    log('✅ 渲染引擎初始化成功', 'success', 'coreEnginesOutput');
                    
                    // 测试渲染
                    renderEngine.render(0);
                    log('✅ 渲染测试通过', 'success', 'coreEnginesOutput');
                    
                    // 测试画布清空
                    renderEngine.clear();
                    log('✅ 画布清空测试通过', 'success', 'coreEnginesOutput');
                    
                } else {
                    log('❌ 渲染引擎初始化失败', 'error', 'coreEnginesOutput');
                }
                
            } catch (error) {
                log(`❌ 渲染引擎测试失败: ${error.message}`, 'error', 'coreEnginesOutput');
            }
        }

        // 游戏系统测试
        async function testGameController() {
            log('开始测试游戏控制器...', 'info', 'gameSystemsOutput');
            
            try {
                await gameController.init();
                log('✅ 游戏控制器初始化成功', 'success', 'gameSystemsOutput');
                
                log(`游戏状态: ${gameController.gameState}`, 'info', 'gameSystemsOutput');
                log(`是否已初始化: ${gameController.isInitialized}`, 'info', 'gameSystemsOutput');
                
            } catch (error) {
                log(`❌ 游戏控制器测试失败: ${error.message}`, 'error', 'gameSystemsOutput');
            }
        }

        function testInputManager() {
            log('开始测试输入管理器...', 'info', 'gameSystemsOutput');
            
            try {
                const canvas = document.getElementById('gameCanvas');
                const inputManager = new InputManager();
                inputManager.init(canvas);
                
                log('✅ 输入管理器初始化成功', 'success', 'gameSystemsOutput');
                
                // 测试频率设置
                inputManager.setFrequency(440);
                log(`当前频率: ${inputManager.currentFrequency} Hz`, 'info', 'gameSystemsOutput');
                
                inputManager.destroy();
                log('✅ 输入管理器销毁成功', 'success', 'gameSystemsOutput');
                
            } catch (error) {
                log(`❌ 输入管理器测试失败: ${error.message}`, 'error', 'gameSystemsOutput');
            }
        }

        function testLevelSystem() {
            log('开始测试关卡系统...', 'info', 'gameSystemsOutput');
            
            try {
                // 创建测试关卡
                const levelConfig = {
                    name: '测试关卡',
                    description: '这是一个测试关卡',
                    particles: [
                        { x: 100, y: 100, frequency: 440, radius: 10 },
                        { x: 200, y: 100, frequency: 880, radius: 10 }
                    ],
                    targetScore: 500,
                    timeLimit: 30
                };
                
                const level = new Level(levelConfig);
                log('✅ 关卡创建成功', 'success', 'gameSystemsOutput');
                
                // 测试关卡加载
                level.load();
                log('✅ 关卡加载成功', 'success', 'gameSystemsOutput');
                
                // 测试关卡状态
                const stats = level.getStats();
                log(`关卡统计: 粒子数量 ${stats.particleCount}, 目标分数 ${stats.targetScore}`, 'info', 'gameSystemsOutput');
                
            } catch (error) {
                log(`❌ 关卡系统测试失败: ${error.message}`, 'error', 'gameSystemsOutput');
            }
        }

        // 集成测试
        async function testFullIntegration() {
            log('开始完整集成测试...', 'info', 'integrationOutput');
            
            try {
                // 初始化所有系统
                await storageService.init();
                await i18n.init();
                
                const canvas = document.getElementById('gameCanvas');
                renderEngine.init(canvas);
                
                physicsEngine.clear();
                quantumEngine.init();
                
                log('✅ 所有系统初始化完成', 'success', 'integrationOutput');
                
                // 创建测试场景
                createTestScene();
                log('✅ 测试场景创建完成', 'success', 'integrationOutput');
                
                // 开始渲染循环
                startRenderLoop();
                log('✅ 渲染循环已启动', 'success', 'integrationOutput');
                
            } catch (error) {
                log(`❌ 集成测试失败: ${error.message}`, 'error', 'integrationOutput');
            }
        }

        function createTestScene() {
            // 创建一些测试粒子
            for (let i = 0; i < 5; i++) {
                physicsEngine.createParticle({
                    x: 150 + i * 120,
                    y: 200 + Math.sin(i) * 50,
                    radius: 8 + Math.random() * 4,
                    mass: 1,
                    frequency: 220 + i * 110,
                    color: `hsl(${i * 60}, 70%, 60%)`
                });
            }
            
            // 添加一些随机速度
            physicsEngine.particles.forEach(particle => {
                particle.vx = (Math.random() - 0.5) * 50;
                particle.vy = (Math.random() - 0.5) * 50;
            });
        }

        let renderLoopId = null;
        function startRenderLoop() {
            if (renderLoopId) {
                cancelAnimationFrame(renderLoopId);
            }
            
            function loop() {
                // 更新物理
                physicsEngine.update(0.016);
                
                // 更新量子引擎
                quantumEngine.update(0.016);
                
                // 渲染
                renderEngine.render(0);
                
                renderLoopId = requestAnimationFrame(loop);
            }
            
            loop();
        }

        function startMiniGame() {
            log('启动迷你游戏...', 'info', 'integrationOutput');
            
            // 清空现有场景
            physicsEngine.clear();
            
            // 创建游戏粒子
            const particles = [
                { x: 200, y: 300, frequency: 440, radius: 15, isTarget: true },
                { x: 400, y: 300, frequency: 440, radius: 15, isTarget: true },
                { x: 600, y: 300, frequency: 440, radius: 15, isTarget: true }
            ];
            
            particles.forEach(config => {
                const particle = physicsEngine.createParticle(config);
                if (config.isTarget) {
                    particle.userData = { isTarget: true };
                    particle.color = '#ff6b6b';
                }
            });
            
            // 设置点击事件
            const canvas = document.getElementById('gameCanvas');
            canvas.addEventListener('click', handleCanvasClick);
            
            log('✅ 迷你游戏已启动，点击红色粒子来激活它们！', 'success', 'integrationOutput');
        }

        function handleCanvasClick(event) {
            const rect = event.target.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            
            // 查找点击的粒子
            for (const particle of physicsEngine.particles) {
                const distance = MathUtils.distance(x, y, particle.x, particle.y);
                if (distance <= particle.radius) {
                    // 激活粒子
                    particle.isActive = true;
                    particle.color = '#4CAF50';
                    
                    log(`✅ 激活了粒子 (${particle.x.toFixed(0)}, ${particle.y.toFixed(0)})`, 'success', 'integrationOutput');
                    
                    // 检查是否所有目标粒子都被激活
                    const targetParticles = physicsEngine.particles.filter(p => p.userData && p.userData.isTarget);
                    const allActivated = targetParticles.every(p => p.isActive);
                    
                    if (allActivated) {
                        log('🎉 恭喜！所有目标粒子都已激活！', 'success', 'integrationOutput');
                    }
                    
                    break;
                }
            }
        }

        function clearCanvas() {
            if (renderLoopId) {
                cancelAnimationFrame(renderLoopId);
                renderLoopId = null;
            }
            
            physicsEngine.clear();
            renderEngine.clear();
            
            log('✅ 画布已清空', 'success', 'integrationOutput');
        }

        // 性能测试
        function testPerformance() {
            log('开始性能基准测试...', 'info', 'performanceOutput');
            
            const startTime = performance.now();
            
            // 测试大量粒子创建
            physicsEngine.clear();
            for (let i = 0; i < 100; i++) {
                physicsEngine.createParticle({
                    x: Math.random() * 800,
                    y: Math.random() * 600,
                    radius: 5,
                    mass: 1,
                    frequency: 220 + Math.random() * 880
                });
            }
            
            const createTime = performance.now() - startTime;
            log(`创建100个粒子耗时: ${createTime.toFixed(2)}ms`, 'info', 'performanceOutput');
            
            // 测试物理更新性能
            const updateStartTime = performance.now();
            for (let i = 0; i < 60; i++) {
                physicsEngine.update(0.016);
            }
            const updateTime = performance.now() - updateStartTime;
            log(`60次物理更新耗时: ${updateTime.toFixed(2)}ms`, 'info', 'performanceOutput');
            
            // 测试渲染性能
            const renderStartTime = performance.now();
            for (let i = 0; i < 60; i++) {
                renderEngine.render(0);
            }
            const renderTime = performance.now() - renderStartTime;
            log(`60次渲染耗时: ${renderTime.toFixed(2)}ms`, 'info', 'performanceOutput');
            
            log('✅ 性能基准测试完成', 'success', 'performanceOutput');
        }

        function stressTest() {
            log('开始压力测试...', 'info', 'performanceOutput');
            
            // 创建大量粒子
            physicsEngine.clear();
            const particleCount = 500;
            
            for (let i = 0; i < particleCount; i++) {
                physicsEngine.createParticle({
                    x: Math.random() * 800,
                    y: Math.random() * 600,
                    radius: 2 + Math.random() * 3,
                    mass: 1,
                    frequency: 220 + Math.random() * 880,
                    vx: (Math.random() - 0.5) * 100,
                    vy: (Math.random() - 0.5) * 100
                });
            }
            
            log(`创建了 ${particleCount} 个粒子进行压力测试`, 'info', 'performanceOutput');
            
            // 开始高频更新
            let frameCount = 0;
            const startTime = performance.now();
            
            function stressLoop() {
                physicsEngine.update(0.016);
                renderEngine.render(0);
                frameCount++;
                
                if (frameCount < 300) { // 5秒测试
                    requestAnimationFrame(stressLoop);
                } else {
                    const endTime = performance.now();
                    const totalTime = endTime - startTime;
                    const fps = frameCount / (totalTime / 1000);
                    
                    log(`压力测试完成: ${frameCount}帧, 耗时${totalTime.toFixed(2)}ms`, 'info', 'performanceOutput');
                    log(`平均FPS: ${fps.toFixed(2)}`, fps > 30 ? 'success' : 'warning', 'performanceOutput');
                }
            }
            
            stressLoop();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('测试页面加载完成', 'success', 'basicModulesOutput');
            log('点击上方按钮开始测试各个模块', 'info', 'basicModulesOutput');
        });
    </script>
</body>
</html>
