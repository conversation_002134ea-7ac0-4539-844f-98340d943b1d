<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量子共鸣者 - 快速验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: rgba(255,255,255,0.1);
        }
        .pass { border-left: 4px solid #00ff00; }
        .fail { border-left: 4px solid #ff0000; }
        button {
            background: #00d4ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
    </style>
</head>
<body>
    <h1>🎮 量子共鸣者 - 快速验证</h1>
    <button onclick="runTests()">运行测试</button>
    <div id="results"></div>

    <!-- 必要的DOM元素 -->
    <div id="loadingScreen" style="display: none;">
        <div id="loadingProgress" style="width: 0%; height: 4px; background: #00d4ff;"></div>
        <div id="loadingText">0%</div>
    </div>
    <div id="mainMenu" style="display: none;">
        <button id="startGameButton">开始游戏</button>
        <button id="levelSelectButton">选择关卡</button>
        <button id="settingsButton">设置</button>
        <button id="aboutButton">关于</button>
    </div>
    <div id="settingsScreen" style="display: none;"></div>
    <div id="aboutScreen" style="display: none;"></div>
    <div id="levelSelectScreen" style="display: none;"></div>
    <canvas id="game-canvas" style="display: none;" width="800" height="600"></canvas>
    <button id="initAudioButton" style="display: none;">初始化音频</button>

    <!-- 只加载关键脚本 -->
    <script src="js/utils/i18n.js"></script>
    <script src="js/ui/level-select.js"></script>
    <script src="js/game/game-controller.js"></script>

    <script>
        function addResult(name, passed, message) {
            const div = document.createElement('div');
            div.className = `test-result ${passed ? 'pass' : 'fail'}`;
            div.innerHTML = `<strong>${name}:</strong> ${message}`;
            document.getElementById('results').appendChild(div);
        }

        function runTests() {
            document.getElementById('results').innerHTML = '';
            
            console.log('🔍 开始快速验证测试...');
            
            // 测试1: i18n 服务
            const i18nExists = typeof window.i18n !== 'undefined';
            addResult('i18n服务检查', i18nExists, 
                i18nExists ? '✅ window.i18n 存在' : '❌ window.i18n 不存在');
            
            if (i18nExists) {
                const hasInit = typeof window.i18n.init === 'function';
                addResult('i18n初始化方法', hasInit, 
                    hasInit ? '✅ i18n.init 方法存在' : '❌ i18n.init 方法不存在');
            }

            // 测试2: 游戏控制器
            const gameControllerExists = typeof window.gameController !== 'undefined';
            addResult('游戏控制器存在性检查', gameControllerExists, 
                gameControllerExists ? '✅ window.gameController 存在' : '❌ window.gameController 不存在');

            // 测试3: 关卡选择界面
            const levelSelectExists = typeof window.levelSelect !== 'undefined';
            addResult('关卡选择界面存在性检查', levelSelectExists, 
                levelSelectExists ? '✅ window.levelSelect 存在' : '❌ window.levelSelect 不存在');

            if (levelSelectExists) {
                // 测试方法存在性
                const hasStartLevel = typeof window.levelSelect.startLevel === 'function';
                addResult('startLevel方法检查', hasStartLevel, 
                    hasStartLevel ? '✅ startLevel方法存在' : '❌ startLevel方法不存在');

                // 关键测试：hideWithoutReturnToMenu方法
                const hasHideMethod = typeof window.levelSelect.hideWithoutReturnToMenu === 'function';
                addResult('hideWithoutReturnToMenu方法检查', hasHideMethod, 
                    hasHideMethod ? '✅ hideWithoutReturnToMenu方法存在' : '❌ hideWithoutReturnToMenu方法不存在');
                
                // 如果方法存在，尝试调用
                if (hasHideMethod) {
                    try {
                        // 先初始化
                        if (typeof window.levelSelect.init === 'function') {
                            window.levelSelect.init();
                        }
                        
                        // 调用方法
                        window.levelSelect.hideWithoutReturnToMenu();
                        addResult('方法调用测试', true, '✅ hideWithoutReturnToMenu方法调用成功');
                    } catch (error) {
                        addResult('方法调用测试', false, `❌ 方法调用失败: ${error.message}`);
                    }
                }
                
                // 检查对象的所有方法
                const methods = Object.getOwnPropertyNames(Object.getPrototypeOf(window.levelSelect))
                    .filter(name => typeof window.levelSelect[name] === 'function' && name !== 'constructor');
                console.log('📋 levelSelect 所有方法:', methods);
                addResult('方法列表', true, `共找到 ${methods.length} 个方法: ${methods.slice(0, 5).join(', ')}${methods.length > 5 ? '...' : ''}`);
            }

            // 计算总结果
            const results = document.querySelectorAll('.test-result');
            const passed = document.querySelectorAll('.test-result.pass').length;
            const total = results.length;
            
            console.log(`📊 测试完成: ${passed}/${total} 通过`);
            
            const summary = document.createElement('div');
            summary.className = `test-result ${passed === total ? 'pass' : 'fail'}`;
            summary.innerHTML = `<strong>总体测试结果:</strong> ${passed}/${total} 个测试通过 ${passed === total ? '🎉' : '😞'}`;
            document.getElementById('results').appendChild(summary);
        }

        // 页面加载完成后等待一段时间再自动测试
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                console.log('🚀 页面加载完成，开始自动测试...');
                runTests();
            }, 1000);
        });
    </script>
</body>
</html>
