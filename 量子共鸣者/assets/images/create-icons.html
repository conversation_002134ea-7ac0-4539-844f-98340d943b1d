<!DOCTYPE html>
<html>
<head>
    <title>创建图标</title>
</head>
<body>
    <canvas id="canvas" width="512" height="512" style="border: 1px solid #ccc;"></canvas>
    <br>
    <button onclick="downloadIcon(72)">下载 72x72</button>
    <button onclick="downloadIcon(96)">下载 96x96</button>
    <button onclick="downloadIcon(128)">下载 128x128</button>
    <button onclick="downloadIcon(144)">下载 144x144</button>
    <button onclick="downloadIcon(152)">下载 152x152</button>
    <button onclick="downloadIcon(192)">下载 192x192</button>
    <button onclick="downloadIcon(384)">下载 384x384</button>
    <button onclick="downloadIcon(512)">下载 512x512</button>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');

        // 绘制量子共鸣者图标
        function drawIcon() {
            // 清除画布
            ctx.clearRect(0, 0, 512, 512);
            
            // 背景渐变
            const gradient = ctx.createRadialGradient(256, 256, 0, 256, 256, 256);
            gradient.addColorStop(0, '#1a1a2e');
            gradient.addColorStop(1, '#0f0f1e');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 512, 512);
            
            // 中心原子核
            ctx.beginPath();
            ctx.arc(256, 256, 30, 0, Math.PI * 2);
            ctx.fillStyle = '#00d4ff';
            ctx.fill();
            
            // 电子轨道
            for (let i = 0; i < 3; i++) {
                const radius = 80 + i * 40;
                const rotation = (i * Math.PI) / 3;
                
                ctx.save();
                ctx.translate(256, 256);
                ctx.rotate(rotation);
                
                // 轨道线
                ctx.beginPath();
                ctx.ellipse(0, 0, radius, radius * 0.3, 0, 0, Math.PI * 2);
                ctx.strokeStyle = '#ff6b6b';
                ctx.lineWidth = 2;
                ctx.stroke();
                
                // 电子
                ctx.beginPath();
                ctx.arc(radius, 0, 8, 0, Math.PI * 2);
                ctx.fillStyle = '#ff6b6b';
                ctx.fill();
                
                ctx.restore();
            }
            
            // 量子波纹效果
            for (let i = 0; i < 5; i++) {
                ctx.beginPath();
                ctx.arc(256, 256, 50 + i * 30, 0, Math.PI * 2);
                ctx.strokeStyle = `rgba(0, 212, 255, ${0.3 - i * 0.05})`;
                ctx.lineWidth = 1;
                ctx.stroke();
            }
        }

        // 下载指定尺寸的图标
        function downloadIcon(size) {
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = size;
            tempCanvas.height = size;
            const tempCtx = tempCanvas.getContext('2d');
            
            // 缩放绘制
            tempCtx.drawImage(canvas, 0, 0, size, size);
            
            // 下载
            const link = document.createElement('a');
            link.download = `icon-${size}x${size}.png`;
            link.href = tempCanvas.toDataURL();
            link.click();
        }

        // 初始绘制
        drawIcon();
    </script>
</body>
</html>
