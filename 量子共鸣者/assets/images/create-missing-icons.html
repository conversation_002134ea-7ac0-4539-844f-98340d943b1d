<!DOCTYPE html>
<html>
<head>
    <title>创建缺失的图标</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>量子共鸣者 - 创建缺失的图标</h1>
    
    <div style="margin: 20px 0;">
        <h2>快捷方式图标</h2>
        <canvas id="shortcut-canvas" width="96" height="96" style="border: 1px solid #ccc; margin: 10px;"></canvas>
        <div>
            <button onclick="createShortcutIcon('start', '🎮', '#4CAF50')">开始游戏图标</button>
            <button onclick="createShortcutIcon('editor', '🛠️', '#FF9800')">编辑器图标</button>
            <button onclick="createShortcutIcon('achievements', '🏅', '#FFD700')">成就图标</button>
        </div>
    </div>
    
    <div style="margin: 20px 0;">
        <h2>截图占位符</h2>
        <canvas id="screenshot-canvas" width="1280" height="720" style="border: 1px solid #ccc; margin: 10px; max-width: 400px;"></canvas>
        <div>
            <button onclick="createScreenshot(1280, 720, 'wide')">创建宽屏截图</button>
            <button onclick="createScreenshot(720, 1280, 'narrow')">创建窄屏截图</button>
        </div>
    </div>

    <script>
        // 创建快捷方式图标
        function createShortcutIcon(type, emoji, color) {
            const canvas = document.getElementById('shortcut-canvas');
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, 96, 96);
            
            // 背景圆形
            ctx.beginPath();
            ctx.arc(48, 48, 40, 0, Math.PI * 2);
            ctx.fillStyle = color;
            ctx.fill();
            
            // 内圆阴影
            ctx.beginPath();
            ctx.arc(48, 48, 35, 0, Math.PI * 2);
            ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
            ctx.fill();
            
            // 表情符号/图标
            ctx.font = '32px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillStyle = 'white';
            ctx.fillText(emoji, 48, 48);
            
            // 下载
            downloadCanvas(canvas, `shortcut-${type}.png`);
        }
        
        // 创建截图占位符
        function createScreenshot(width, height, orientation) {
            const canvas = document.getElementById('screenshot-canvas');
            canvas.width = width;
            canvas.height = height;
            const ctx = canvas.getContext('2d');
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, width, height);
            gradient.addColorStop(0, '#1a1a2e');
            gradient.addColorStop(0.5, '#16213e');
            gradient.addColorStop(1, '#0f0f1e');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);
            
            // 游戏界面模拟
            const centerX = width / 2;
            const centerY = height / 2;
            
            // 中心原子
            ctx.beginPath();
            ctx.arc(centerX, centerY, Math.min(width, height) * 0.05, 0, Math.PI * 2);
            ctx.fillStyle = '#00d4ff';
            ctx.fill();
            
            // 轨道
            for (let i = 0; i < 3; i++) {
                const radius = Math.min(width, height) * (0.1 + i * 0.08);
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
                ctx.strokeStyle = '#ff6b6b';
                ctx.lineWidth = 2;
                ctx.stroke();
                
                // 粒子
                for (let j = 0; j < 4; j++) {
                    const angle = (j * Math.PI) / 2;
                    const x = centerX + Math.cos(angle) * radius;
                    const y = centerY + Math.sin(angle) * radius;
                    
                    ctx.beginPath();
                    ctx.arc(x, y, 4, 0, Math.PI * 2);
                    ctx.fillStyle = '#ff6b6b';
                    ctx.fill();
                }
            }
            
            // UI元素模拟
            if (orientation === 'wide') {
                // 顶部HUD
                ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
                ctx.fillRect(20, 20, width - 40, 60);
                
                // 分数显示
                ctx.font = '24px Arial';
                ctx.fillStyle = 'white';
                ctx.textAlign = 'left';
                ctx.fillText('分数: 12,450', 40, 55);
                
                // 关卡显示
                ctx.textAlign = 'right';
                ctx.fillText('关卡 3', width - 40, 55);
            } else {
                // 移动端UI
                ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
                ctx.fillRect(20, 20, width - 40, 80);
                
                ctx.font = '32px Arial';
                ctx.fillStyle = 'white';
                ctx.textAlign = 'center';
                ctx.fillText('量子共鸣者', centerX, 70);
            }
            
            // 标题文字
            ctx.font = `${Math.min(width, height) * 0.04}px Arial`;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.textAlign = 'center';
            ctx.fillText('量子共鸣者游戏截图', centerX, height - 50);
            
            // 下载
            downloadCanvas(canvas, `screenshot-${orientation === 'wide' ? '1' : '2'}.png`);
        }
        
        // 下载画布内容
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 页面加载完成后的提示
        window.onload = function() {
            console.log('图标创建工具已加载');
            console.log('点击按钮创建并下载对应的图标文件');
        };
    </script>
    
    <div style="margin-top: 30px; padding: 20px; background: #f5f5f5; border-radius: 8px;">
        <h3>使用说明：</h3>
        <ol>
            <li>点击对应按钮创建图标</li>
            <li>图标会自动下载到您的下载文件夹</li>
            <li>将下载的图标文件移动到 <code>assets/images/</code> 目录</li>
            <li>确保文件名与 manifest.json 中的路径匹配</li>
        </ol>
        
        <h3>需要创建的文件：</h3>
        <ul>
            <li><code>shortcut-start.png</code> - 开始游戏快捷方式图标</li>
            <li><code>shortcut-editor.png</code> - 关卡编辑器快捷方式图标</li>
            <li><code>shortcut-achievements.png</code> - 成就系统快捷方式图标</li>
            <li><code>screenshot-1.png</code> - 宽屏游戏截图 (1280x720)</li>
            <li><code>screenshot-2.png</code> - 窄屏游戏截图 (720x1280)</li>
        </ul>
    </div>
</body>
</html>
