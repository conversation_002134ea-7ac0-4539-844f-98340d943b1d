<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关卡选择修复测试 - 量子共鸣者</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: #3a3a3a;
            border-radius: 5px;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #4CAF50;
            color: white;
        }
        .error {
            background: #f44336;
            color: white;
        }
        .warning {
            background: #ff9800;
            color: white;
        }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎮 关卡选择修复测试</h1>
        
        <div class="test-section">
            <h2>📋 测试项目</h2>
            <button class="test-button" onclick="testGameControllerExists()">1. 检查游戏控制器是否存在</button>
            <button class="test-button" onclick="testGameControllerInitialized()">2. 检查游戏控制器是否已初始化</button>
            <button class="test-button" onclick="testLevelSelectExists()">3. 检查关卡选择界面是否存在</button>
            <button class="test-button" onclick="testStartLevelMethod()">4. 检查startLevel方法</button>
            <button class="test-button" onclick="testHideWithoutReturnToMenu()">5. 检查hideWithoutReturnToMenu方法</button>
            <button class="test-button" onclick="runAllTests()">🚀 运行所有测试</button>
        </div>
        
        <div class="test-section">
            <h2>📊 测试结果</h2>
            <div id="test-results"></div>
        </div>
        
        <div class="test-section">
            <h2>🖥️ 控制台输出</h2>
            <div id="console-output"></div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/utils/math-utils.js"></script>
    <script src="js/core/audio-engine.js"></script>
    <script src="js/core/physics-engine.js"></script>
    <script src="js/core/quantum-engine.js"></script>
    <script src="js/audio/synthesizer.js"></script>
    <script src="js/audio/sequencer.js"></script>
    <script src="js/audio/effects.js"></script>
    <script src="js/audio/visualizer.js"></script>
    <script src="js/audio/audio-manager.js"></script>
    <script src="js/render/webgl-renderer.js"></script>
    <script src="js/render/particle-system-3d.js"></script>
    <script src="js/render/scene-manager-3d.js"></script>
    <script src="js/core/render-engine.js"></script>
    <script src="js/render/renderer.js"></script>
    <script src="js/render/particle-system.js"></script>
    <script src="js/ui/notification-system.js"></script>
    <script src="js/ui/ui-manager.js"></script>
    <script src="js/ui/ui-animations.js"></script>
    <script src="js/ui/game-hud.js"></script>
    <script src="js/ui/settings-panel.js"></script>
    <script src="js/ui/level-select.js"></script>
    <script src="js/ui/level-editor.js"></script>
    <script src="js/ui/game-over.js"></script>
    <script src="js/ui/achievements.js"></script>
    <script src="js/ui/leaderboard.js"></script>
    <script src="js/game/player-manager.js"></script>
    <script src="js/game/game-state.js"></script>
    <script src="js/game/level-manager.js"></script>
    <script src="js/game/input-manager.js"></script>
    <script src="js/game/game-controller.js"></script>
    <script src="js/ui/screen-manager.js"></script>
    <script src="js/ui/input-handler.js"></script>
    <script src="js/ui/ui-components.js"></script>
    <script src="js/app.js"></script>

    <script>
        // 重定向控制台输出到页面
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📝';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        // 测试函数
        function addTestResult(testName, success, message) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${success ? 'success' : 'error'}`;
            resultDiv.innerHTML = `<strong>${testName}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
        }

        function testGameControllerExists() {
            const exists = typeof window.gameController !== 'undefined';
            addTestResult(
                '游戏控制器存在性检查',
                exists,
                exists ? '✅ window.gameController 存在' : '❌ window.gameController 不存在'
            );
            return exists;
        }

        function testGameControllerInitialized() {
            if (!window.gameController) {
                addTestResult('游戏控制器初始化检查', false, '❌ 游戏控制器不存在');
                return false;
            }
            
            const initialized = window.gameController.isInitialized;
            addTestResult(
                '游戏控制器初始化检查',
                initialized,
                initialized ? '✅ 游戏控制器已初始化' : '❌ 游戏控制器未初始化'
            );
            return initialized;
        }

        function testLevelSelectExists() {
            const exists = typeof window.levelSelect !== 'undefined';
            addTestResult(
                '关卡选择界面存在性检查',
                exists,
                exists ? '✅ window.levelSelect 存在' : '❌ window.levelSelect 不存在'
            );
            return exists;
        }

        function testStartLevelMethod() {
            if (!window.levelSelect) {
                addTestResult('startLevel方法检查', false, '❌ 关卡选择界面不存在');
                return false;
            }
            
            const hasMethod = typeof window.levelSelect.startLevel === 'function';
            addTestResult(
                'startLevel方法检查',
                hasMethod,
                hasMethod ? '✅ startLevel方法存在' : '❌ startLevel方法不存在'
            );
            return hasMethod;
        }

        function testHideWithoutReturnToMenu() {
            if (!window.levelSelect) {
                addTestResult('hideWithoutReturnToMenu方法检查', false, '❌ 关卡选择界面不存在');
                return false;
            }
            
            const hasMethod = typeof window.levelSelect.hideWithoutReturnToMenu === 'function';
            addTestResult(
                'hideWithoutReturnToMenu方法检查',
                hasMethod,
                hasMethod ? '✅ hideWithoutReturnToMenu方法存在' : '❌ hideWithoutReturnToMenu方法不存在'
            );
            return hasMethod;
        }

        function runAllTests() {
            document.getElementById('test-results').innerHTML = '';
            console.log('🧪 开始运行所有测试...');
            
            const tests = [
                testGameControllerExists,
                testGameControllerInitialized,
                testLevelSelectExists,
                testStartLevelMethod,
                testHideWithoutReturnToMenu
            ];
            
            let passedTests = 0;
            tests.forEach(test => {
                if (test()) passedTests++;
            });
            
            const totalTests = tests.length;
            const success = passedTests === totalTests;
            
            addTestResult(
                '总体测试结果',
                success,
                `${passedTests}/${totalTests} 个测试通过 ${success ? '🎉' : '😞'}`
            );
            
            console.log(`🧪 测试完成: ${passedTests}/${totalTests} 个测试通过`);
        }

        // 页面加载完成后初始化应用程序并运行测试
        window.addEventListener('load', async () => {
            console.log('📄 页面加载完成，开始初始化应用程序...');

            try {
                // 初始化应用程序
                if (typeof QuantumResonanceApp !== 'undefined') {
                    window.quantumApp = new QuantumResonanceApp();
                    await quantumApp.init();
                    console.log('✅ 应用程序初始化完成');
                } else {
                    console.warn('⚠️ QuantumResonanceApp 类未找到，跳过应用程序初始化');
                }

                // 等待3秒后运行测试
                setTimeout(() => {
                    runAllTests();
                }, 3000);

            } catch (error) {
                console.error('❌ 应用程序初始化失败:', error);
                // 即使初始化失败也运行测试
                setTimeout(() => {
                    runAllTests();
                }, 3000);
            }
        });
    </script>
</body>
</html>
