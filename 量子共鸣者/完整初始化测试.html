<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整初始化测试 - 量子共鸣者</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
        }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .game-canvas {
            width: 100%;
            height: 300px;
            background: #000;
            border: 1px solid #333;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 完整初始化测试</h1>
        
        <button class="test-button" onclick="testGameController()">测试游戏控制器</button>
        <button class="test-button" onclick="testLevelSelect()">测试关卡选择</button>
        <button class="test-button" onclick="initializeApp()">手动初始化应用</button>
        <button class="test-button" onclick="testStartLevel()">测试启动关卡</button>
        
        <h2>🖥️ 控制台输出</h2>
        <div id="console-output"></div>
    </div>

    <!-- 必要的HTML元素 -->
    <div class="hidden">
        <canvas id="game-canvas" class="game-canvas"></canvas>
        <canvas id="ui-canvas" class="game-canvas"></canvas>
        <div id="levelSelectScreen" class="screen">
            <div class="level-select-container">
                <!-- 关卡选择内容 -->
            </div>
        </div>
        <div id="gameScreen" class="screen">
            <!-- 游戏屏幕内容 -->
        </div>
        <div id="main-menu-screen" class="screen">
            <!-- 主菜单内容 -->
        </div>
    </div>

    <!-- 加载所有必要的脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/utils/math-utils.js"></script>
    <script src="js/core/audio-engine.js"></script>
    <script src="js/core/physics-engine.js"></script>
    <script src="js/core/quantum-engine.js"></script>
    <script src="js/audio/synthesizer.js"></script>
    <script src="js/audio/sequencer.js"></script>
    <script src="js/audio/effects.js"></script>
    <script src="js/audio/visualizer.js"></script>
    <script src="js/audio/audio-manager.js"></script>
    <script src="js/render/webgl-renderer.js"></script>
    <script src="js/render/particle-system-3d.js"></script>
    <script src="js/render/scene-manager-3d.js"></script>
    <script src="js/core/render-engine.js"></script>
    <script src="js/render/renderer.js"></script>
    <script src="js/render/particle-system.js"></script>
    <script src="js/ui/notification-system.js"></script>
    <script src="js/ui/ui-manager.js"></script>
    <script src="js/ui/ui-animations.js"></script>
    <script src="js/ui/game-hud.js"></script>
    <script src="js/ui/settings-panel.js"></script>
    <script src="js/ui/level-select.js"></script>
    <script src="js/ui/level-editor.js"></script>
    <script src="js/ui/game-over.js"></script>
    <script src="js/ui/achievements.js"></script>
    <script src="js/ui/leaderboard.js"></script>
    <script src="js/game/player-manager.js"></script>
    <script src="js/game/game-state.js"></script>
    <script src="js/game/level-manager.js"></script>
    <script src="js/game/input-manager.js"></script>
    <script src="js/game/game-controller.js"></script>
    <script src="js/ui/screen-manager.js"></script>
    <script src="js/ui/input-handler.js"></script>
    <script src="js/ui/ui-components.js"></script>
    <script src="js/app.js"></script>

    <script>
        // 重定向控制台输出到页面
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📝';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        function testGameController() {
            console.log('🧪 测试游戏控制器...');
            console.log('window.gameController 存在:', typeof window.gameController !== 'undefined');
            if (window.gameController) {
                console.log('gameController.isInitialized:', window.gameController.isInitialized);
                console.log('gameController.init 方法存在:', typeof window.gameController.init === 'function');
                console.log('gameController.startLevel 方法存在:', typeof window.gameController.startLevel === 'function');
            }
        }

        function testLevelSelect() {
            console.log('🧪 测试关卡选择...');
            console.log('window.levelSelect 存在:', typeof window.levelSelect !== 'undefined');
            if (window.levelSelect) {
                console.log('levelSelect.startLevel 方法存在:', typeof window.levelSelect.startLevel === 'function');
                console.log('levelSelect.hideWithoutReturnToMenu 方法存在:', typeof window.levelSelect.hideWithoutReturnToMenu === 'function');
                console.log('levelSelect.isInitialized:', window.levelSelect.isInitialized);
            }
        }

        async function initializeApp() {
            console.log('🚀 手动初始化应用程序...');
            
            try {
                if (typeof QuantumResonanceApp !== 'undefined') {
                    if (!window.quantumApp) {
                        window.quantumApp = new QuantumResonanceApp();
                    }
                    await quantumApp.init();
                    console.log('✅ 应用程序初始化完成');
                } else {
                    console.error('❌ QuantumResonanceApp 类未找到');
                }
                
                // 重新测试
                testGameController();
                testLevelSelect();
                
            } catch (error) {
                console.error('❌ 应用程序初始化失败:', error);
            }
        }

        function testStartLevel() {
            console.log('🧪 测试启动关卡功能...');
            
            if (!window.levelSelect) {
                console.error('❌ levelSelect 不存在');
                return;
            }
            
            if (!window.gameController) {
                console.error('❌ gameController 不存在');
                return;
            }
            
            if (!window.gameController.isInitialized) {
                console.error('❌ gameController 未初始化');
                return;
            }
            
            // 模拟选择关卡
            window.levelSelect.selectedLevel = { id: 'test-level', name: '测试关卡' };
            window.levelSelect.selectedDifficulty = 'easy';
            
            console.log('🎮 模拟启动关卡...');
            window.levelSelect.startLevel();
        }

        // 页面加载完成后自动测试
        window.addEventListener('load', () => {
            console.log('📄 页面加载完成');
            setTimeout(() => {
                testGameController();
                testLevelSelect();
            }, 1000);
        });
    </script>
</body>
</html>
