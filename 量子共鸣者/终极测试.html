<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量子共鸣者 - 终极测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #ffffff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            color: #00d4ff;
            margin-bottom: 30px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            font-size: 3em;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .test-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #00d4ff;
        }
        
        .test-result {
            margin: 8px 0;
            padding: 12px;
            border-radius: 6px;
            background: rgba(0, 0, 0, 0.3);
            border-left: 4px solid #666;
            display: flex;
            align-items: center;
            font-size: 14px;
        }
        
        .pass { 
            border-left-color: #00ff00; 
            background: rgba(0, 255, 0, 0.1);
        }
        
        .fail { 
            border-left-color: #ff0000; 
            background: rgba(255, 0, 0, 0.1);
        }
        
        .status-indicator {
            display: inline-block;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .status-pass { background: #00ff00; box-shadow: 0 0 8px rgba(0, 255, 0, 0.5); }
        .status-fail { background: #ff0000; box-shadow: 0 0 8px rgba(255, 0, 0, 0.5); }
        
        .summary {
            background: rgba(0, 212, 255, 0.2);
            border: 2px solid #00d4ff;
            padding: 25px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
        }
        
        button {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        
        button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
        }
        
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        
        .progress-bar {
            width: 100%;
            height: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4ff, #00ff88);
            width: 0%;
            transition: width 0.5s ease;
        }
        
        .emoji {
            font-size: 1.2em;
            margin-right: 8px;
        }
        
        h3 {
            color: #00d4ff;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 量子共鸣者 - 终极测试</h1>
        
        <div class="controls">
            <button onclick="runUltimateTest()">🚀 运行终极测试</button>
            <button onclick="runQuickTest()">⚡ 快速测试</button>
            <button onclick="clearResults()">🧹 清空结果</button>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progress"></div>
        </div>
        
        <div class="test-grid" id="test-grid"></div>
        
        <div id="final-summary" style="display: none;"></div>
    </div>

    <!-- 必要的DOM元素 -->
    <div id="loadingScreen" style="display: none;">
        <div id="loadingProgress" style="width: 0%; height: 4px; background: #00d4ff;"></div>
        <div id="loadingText">0%</div>
    </div>
    
    <div id="mainMenu" style="display: none;">
        <button id="startGameButton">开始游戏</button>
        <button id="levelSelectButton">选择关卡</button>
        <button id="settingsButton">设置</button>
        <button id="aboutButton">关于</button>
    </div>
    
    <div id="settingsScreen" style="display: none;"></div>
    <div id="aboutScreen" style="display: none;"></div>
    <div id="levelSelectScreen" style="display: none;"></div>
    <canvas id="game-canvas" style="display: none;" width="800" height="600"></canvas>
    <button id="initAudioButton" style="display: none;">初始化音频</button>

    <!-- 加载所有关键脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/ui/level-select.js"></script>
    <script src="js/game/game-controller.js"></script>

    <script>
        let testResults = [];
        let currentProgress = 0;
        let totalTests = 0;

        function updateProgress(current, total) {
            const percentage = (current / total) * 100;
            document.getElementById('progress').style.width = percentage + '%';
        }

        function createTestSection(title, emoji) {
            const section = document.createElement('div');
            section.className = 'test-section';
            section.innerHTML = `<h3><span class="emoji">${emoji}</span>${title}</h3>`;
            document.getElementById('test-grid').appendChild(section);
            return section;
        }

        function addResult(section, name, passed, message) {
            const result = { name, passed, message };
            testResults.push(result);
            
            const div = document.createElement('div');
            div.className = `test-result ${passed ? 'pass' : 'fail'}`;
            div.innerHTML = `
                <span class="status-indicator ${passed ? 'status-pass' : 'status-fail'}"></span>
                <div>
                    <strong>${name}:</strong><br>
                    <span style="opacity: 0.9; font-size: 12px;">${message}</span>
                </div>
            `;
            section.appendChild(div);
            
            currentProgress++;
            updateProgress(currentProgress, totalTests);
        }

        async function runUltimateTest() {
            testResults = [];
            currentProgress = 0;
            totalTests = 15; // 预计测试数量
            
            document.getElementById('test-grid').innerHTML = '';
            document.getElementById('final-summary').style.display = 'none';
            updateProgress(0, totalTests);
            
            console.log('🎯 开始运行终极测试...');
            
            // 等待页面完全加载和组件初始化
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 运行所有测试
            await testI18nService();
            await testGameController();
            await testLevelSelect();
            await testIntegration();
            
            // 生成最终报告
            generateFinalReport();
        }

        async function runQuickTest() {
            testResults = [];
            currentProgress = 0;
            totalTests = 8;
            
            document.getElementById('test-grid').innerHTML = '';
            document.getElementById('final-summary').style.display = 'none';
            updateProgress(0, totalTests);
            
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testI18nService();
            await testGameController();
            
            generateFinalReport();
        }

        async function testI18nService() {
            const section = createTestSection('国际化服务', '🌐');
            
            const i18nExists = typeof window.i18n !== 'undefined';
            addResult(section, 'i18n 存在', i18nExists, 
                i18nExists ? '✅ window.i18n 已创建' : '❌ window.i18n 不存在');
            
            if (i18nExists) {
                const hasInit = typeof window.i18n.init === 'function';
                addResult(section, 'init 方法', hasInit, 
                    hasInit ? '✅ init 方法存在' : '❌ init 方法不存在');
                
                if (hasInit) {
                    try {
                        await window.i18n.init();
                        addResult(section, 'init 调用', true, '✅ init 方法调用成功');
                    } catch (error) {
                        addResult(section, 'init 调用', false, `❌ init 调用失败: ${error.message}`);
                    }
                }
            }
        }

        async function testGameController() {
            const section = createTestSection('游戏控制器', '🎮');
            
            const gameControllerExists = typeof window.gameController !== 'undefined';
            addResult(section, '控制器存在', gameControllerExists, 
                gameControllerExists ? '✅ window.gameController 已创建' : '❌ window.gameController 不存在');
            
            if (gameControllerExists) {
                // 等待自动初始化完成
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                const initialized = window.gameController.isInitialized === true;
                addResult(section, '控制器初始化', initialized, 
                    initialized ? '✅ 游戏控制器已初始化' : `❌ 游戏控制器未初始化 (状态: ${window.gameController.isInitialized})`);
                
                // 如果未初始化，尝试手动初始化
                if (!initialized) {
                    try {
                        console.log('🔧 尝试手动初始化游戏控制器...');
                        await window.gameController.init();
                        const manualInit = window.gameController.isInitialized === true;
                        addResult(section, '手动初始化', manualInit, 
                            manualInit ? '✅ 手动初始化成功' : '❌ 手动初始化失败');
                    } catch (error) {
                        addResult(section, '手动初始化', false, `❌ 手动初始化异常: ${error.message}`);
                    }
                }
                
                const hasStartGame = typeof window.gameController.startGame === 'function';
                addResult(section, 'startGame 方法', hasStartGame, 
                    hasStartGame ? '✅ startGame 方法存在' : '❌ startGame 方法不存在');
            }
        }

        async function testLevelSelect() {
            const section = createTestSection('关卡选择', '🎯');
            
            const levelSelectExists = typeof window.levelSelect !== 'undefined';
            addResult(section, '关卡选择存在', levelSelectExists, 
                levelSelectExists ? '✅ window.levelSelect 已创建' : '❌ window.levelSelect 不存在');
            
            if (levelSelectExists) {
                const hasStartLevel = typeof window.levelSelect.startLevel === 'function';
                addResult(section, 'startLevel 方法', hasStartLevel, 
                    hasStartLevel ? '✅ startLevel 方法存在' : '❌ startLevel 方法不存在');
                
                const hasHideMethod = typeof window.levelSelect.hideWithoutReturnToMenu === 'function';
                addResult(section, 'hideWithoutReturnToMenu', hasHideMethod, 
                    hasHideMethod ? '✅ hideWithoutReturnToMenu 方法存在' : '❌ hideWithoutReturnToMenu 方法不存在');
                
                if (hasHideMethod) {
                    try {
                        window.levelSelect.hideWithoutReturnToMenu();
                        addResult(section, '方法调用测试', true, '✅ hideWithoutReturnToMenu 调用成功');
                    } catch (error) {
                        addResult(section, '方法调用测试', false, `❌ 方法调用失败: ${error.message}`);
                    }
                }
            }
        }

        async function testIntegration() {
            const section = createTestSection('集成测试', '🔗');
            
            // 测试所有核心组件是否都存在
            const components = [
                { name: 'i18n', obj: window.i18n },
                { name: 'gameController', obj: window.gameController },
                { name: 'levelSelect', obj: window.levelSelect }
            ];
            
            const allExist = components.every(comp => comp.obj !== undefined);
            addResult(section, '核心组件完整性', allExist, 
                allExist ? '✅ 所有核心组件都存在' : '❌ 部分核心组件缺失');
            
            // 测试初始化状态
            const i18nReady = window.i18n && typeof window.i18n.init === 'function';
            const gameControllerReady = window.gameController && window.gameController.isInitialized === true;
            const levelSelectReady = window.levelSelect && typeof window.levelSelect.startLevel === 'function';
            
            const allReady = i18nReady && gameControllerReady && levelSelectReady;
            addResult(section, '系统就绪状态', allReady, 
                allReady ? '✅ 所有系统组件已就绪' : '❌ 部分系统组件未就绪');
        }

        function generateFinalReport() {
            const passed = testResults.filter(r => r.passed).length;
            const total = testResults.length;
            const success = passed === total;
            const percentage = Math.round((passed / total) * 100);
            
            const summaryDiv = document.getElementById('final-summary');
            summaryDiv.style.display = 'block';
            summaryDiv.className = `summary`;
            
            let emoji, status, color, message;
            
            if (success) {
                emoji = '🎉';
                status = '完美通过';
                color = '#00ff00';
                message = '🎊 恭喜！量子共鸣者所有功能已完全修复！<br>游戏已准备就绪，可以开始体验了！';
            } else if (percentage >= 80) {
                emoji = '🎯';
                status = '基本通过';
                color = '#ffaa00';
                message = '⚡ 很好！大部分功能正常工作。<br>少数问题不影响核心游戏体验。';
            } else {
                emoji = '😞';
                status = '需要修复';
                color = '#ff6600';
                message = '⚠️ 部分核心功能仍需修复。<br>请查看详细测试结果进行调试。';
            }
            
            summaryDiv.innerHTML = `
                <div style="font-size: 4em; margin-bottom: 20px;">${emoji}</div>
                <div style="color: ${color}; font-size: 1.8em; margin-bottom: 15px;">
                    终极测试结果: ${passed}/${total} (${percentage}%) ${status}
                </div>
                <div style="font-size: 1.2em; opacity: 0.9; line-height: 1.5;">
                    ${message}
                </div>
            `;
            
            updateProgress(total, total);
        }

        function clearResults() {
            document.getElementById('test-grid').innerHTML = '';
            document.getElementById('final-summary').style.display = 'none';
            updateProgress(0, 1);
            testResults = [];
            currentProgress = 0;
        }

        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 终极测试页面加载完成');
            
            setTimeout(() => {
                console.log('⏰ 开始自动运行终极测试...');
                runUltimateTest();
            }, 4000);
        });
    </script>
</body>
</html>
