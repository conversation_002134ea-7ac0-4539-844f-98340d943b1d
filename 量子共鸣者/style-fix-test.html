<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>样式修复测试 - 量子共鸣者</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/ui-components.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .test-controls {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 2000;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #333;
        }
        
        .test-btn {
            display: block;
            width: 200px;
            padding: 10px;
            margin: 5px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 6px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            font-family: monospace;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4caf50;
            color: #4caf50;
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: #f44336;
        }
        
        .status.info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196f3;
            color: #2196f3;
        }
    </style>
</head>
<body>
    <div class="test-controls">
        <h3>🎯 样式修复测试</h3>
        
        <button class="test-btn" onclick="testScreenVisibility()">测试屏幕可见性</button>
        <button class="test-btn" onclick="testActiveClass()">测试 Active 类</button>
        <button class="test-btn" onclick="testLevelSelectShow()">测试关卡选择显示</button>
        <button class="test-btn" onclick="simulateUIManager()">模拟 UI Manager</button>
        <button class="test-btn" onclick="resetScreen()">重置屏幕状态</button>
        
        <div id="status" class="status info">
            准备进行样式测试...
        </div>
    </div>
    
    <!-- 关卡选择屏幕 -->
    <div id="levelSelectScreen" class="screen level-select-screen">
        <div style="text-align: center; color: white;">
            <h2>🎯 关卡选择界面</h2>
            <p>这是测试内容，如果你能看到这个文字，说明样式修复成功！</p>
            <div style="margin-top: 20px;">
                <div style="display: inline-block; margin: 10px; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                    <h3>关卡 1</h3>
                    <p>教程关卡</p>
                </div>
                <div style="display: inline-block; margin: 10px; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                    <h3>关卡 2</h3>
                    <p>基础共鸣</p>
                </div>
                <div style="display: inline-block; margin: 10px; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                    <h3>关卡 3</h3>
                    <p>连锁反应</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/ui/level-select.js"></script>
    <script src="js/ui/ui-manager.js"></script>
    
    <script>
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        function testScreenVisibility() {
            updateStatus('测试屏幕可见性...', 'info');
            
            const screen = document.getElementById('levelSelectScreen');
            const computedStyle = window.getComputedStyle(screen);
            
            const opacity = computedStyle.opacity;
            const visibility = computedStyle.visibility;
            const display = computedStyle.display;
            
            console.log('屏幕样式状态:', { opacity, visibility, display });
            
            if (opacity === '0' && visibility === 'hidden') {
                updateStatus(`✅ 默认状态正确: opacity=${opacity}, visibility=${visibility}`, 'success');
            } else {
                updateStatus(`❌ 默认状态异常: opacity=${opacity}, visibility=${visibility}`, 'error');
            }
        }
        
        function testActiveClass() {
            updateStatus('测试 Active 类效果...', 'info');
            
            const screen = document.getElementById('levelSelectScreen');
            
            // 添加 active 类
            screen.classList.add('active');
            
            setTimeout(() => {
                const computedStyle = window.getComputedStyle(screen);
                const opacity = computedStyle.opacity;
                const visibility = computedStyle.visibility;
                
                console.log('添加 active 类后:', { opacity, visibility });
                
                if (opacity === '1' && visibility === 'visible') {
                    updateStatus(`✅ Active 类生效: opacity=${opacity}, visibility=${visibility}`, 'success');
                } else {
                    updateStatus(`❌ Active 类无效: opacity=${opacity}, visibility=${visibility}`, 'error');
                }
                
                // 2秒后移除 active 类
                setTimeout(() => {
                    screen.classList.remove('active');
                    updateStatus('已移除 active 类', 'info');
                }, 2000);
            }, 100);
        }
        
        function testLevelSelectShow() {
            updateStatus('测试关卡选择显示...', 'info');
            
            if (window.levelSelect) {
                try {
                    if (!window.levelSelect.isInitialized) {
                        window.levelSelect.init();
                    }
                    
                    window.levelSelect.show();
                    
                    setTimeout(() => {
                        const screen = document.getElementById('levelSelectScreen');
                        const hasActive = screen.classList.contains('active');
                        const display = screen.style.display;
                        
                        if (display === 'flex' && hasActive) {
                            updateStatus('✅ 关卡选择显示成功！', 'success');
                        } else {
                            updateStatus(`❌ 关卡选择显示失败: display=${display}, active=${hasActive}`, 'error');
                        }
                    }, 500);
                    
                } catch (error) {
                    updateStatus(`❌ 关卡选择显示出错: ${error.message}`, 'error');
                }
            } else {
                updateStatus('❌ levelSelect 实例不存在', 'error');
            }
        }
        
        function simulateUIManager() {
            updateStatus('模拟 UI Manager 显示屏幕...', 'info');
            
            if (window.uiManager && window.uiManager.showScreen) {
                try {
                    window.uiManager.showScreen('levelSelectScreen');
                    
                    setTimeout(() => {
                        const screen = document.getElementById('levelSelectScreen');
                        const hasActive = screen.classList.contains('active');
                        const display = screen.style.display;
                        const computedStyle = window.getComputedStyle(screen);
                        const opacity = computedStyle.opacity;
                        const visibility = computedStyle.visibility;
                        
                        console.log('UI Manager 显示后状态:', {
                            display, hasActive, opacity, visibility
                        });
                        
                        if (display === 'flex' && hasActive && opacity === '1' && visibility === 'visible') {
                            updateStatus('✅ UI Manager 显示完全成功！', 'success');
                        } else {
                            updateStatus(`❌ UI Manager 显示不完整: display=${display}, active=${hasActive}, opacity=${opacity}, visibility=${visibility}`, 'error');
                        }
                    }, 1000);
                    
                } catch (error) {
                    updateStatus(`❌ UI Manager 显示出错: ${error.message}`, 'error');
                }
            } else {
                updateStatus('❌ uiManager 不存在或 showScreen 方法不存在', 'error');
            }
        }
        
        function resetScreen() {
            updateStatus('重置屏幕状态...', 'info');
            
            const screen = document.getElementById('levelSelectScreen');
            screen.style.display = 'none';
            screen.classList.remove('active', 'screen-enter', 'screen-exit');
            
            setTimeout(() => {
                updateStatus('✅ 屏幕状态已重置', 'success');
            }, 100);
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('页面加载完成，可以开始测试', 'info');
            
            // 自动运行基础测试
            setTimeout(testScreenVisibility, 500);
        });
    </script>
</body>
</html>
