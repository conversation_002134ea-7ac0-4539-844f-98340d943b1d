/**
 * 量子共鸣者关卡选择修复验证测试
 * 验证游戏控制器初始化和关卡选择功能是否正常工作
 */

class FixVerificationTest {
    constructor() {
        this.testResults = [];
        this.passedTests = 0;
        this.totalTests = 0;
    }

    /**
     * 运行所有验证测试
     */
    async runAllTests() {
        console.log('🧪 开始运行修复验证测试...');
        console.log('='.repeat(50));

        // 等待页面完全加载
        await this.waitForPageLoad();

        // 运行各项测试
        this.testGameControllerExists();
        this.testGameControllerInitialized();
        this.testLevelSelectExists();
        this.testStartLevelMethod();
        this.testHideWithoutReturnToMenuMethod();

        // 生成测试报告
        this.generateTestReport();
    }

    /**
     * 等待页面加载完成
     */
    async waitForPageLoad() {
        return new Promise((resolve) => {
            if (document.readyState === 'complete') {
                resolve();
            } else {
                window.addEventListener('load', resolve);
            }
        });
    }

    /**
     * 测试游戏控制器是否存在
     */
    testGameControllerExists() {
        const exists = typeof window.gameController !== 'undefined';
        this.addTestResult(
            '游戏控制器存在性检查',
            exists,
            exists ? '✅ window.gameController 存在' : '❌ window.gameController 不存在'
        );
    }

    /**
     * 测试游戏控制器是否已初始化
     */
    testGameControllerInitialized() {
        if (!window.gameController) {
            this.addTestResult('游戏控制器初始化检查', false, '❌ 游戏控制器不存在');
            return;
        }

        const initialized = window.gameController.isInitialized === true;
        this.addTestResult(
            '游戏控制器初始化检查',
            initialized,
            initialized ? '✅ 游戏控制器已初始化' : '❌ 游戏控制器未初始化'
        );
    }

    /**
     * 测试关卡选择界面是否存在
     */
    testLevelSelectExists() {
        const exists = typeof window.levelSelect !== 'undefined';
        this.addTestResult(
            '关卡选择界面存在性检查',
            exists,
            exists ? '✅ window.levelSelect 存在' : '❌ window.levelSelect 不存在'
        );
    }

    /**
     * 测试startLevel方法是否存在
     */
    testStartLevelMethod() {
        if (!window.levelSelect) {
            this.addTestResult('startLevel方法检查', false, '❌ 关卡选择界面不存在');
            return;
        }

        const hasMethod = typeof window.levelSelect.startLevel === 'function';
        this.addTestResult(
            'startLevel方法检查',
            hasMethod,
            hasMethod ? '✅ startLevel方法存在' : '❌ startLevel方法不存在'
        );
    }

    /**
     * 测试hideWithoutReturnToMenu方法是否存在
     */
    testHideWithoutReturnToMenuMethod() {
        if (!window.levelSelect) {
            this.addTestResult('hideWithoutReturnToMenu方法检查', false, '❌ 关卡选择界面不存在');
            return;
        }

        const hasMethod = typeof window.levelSelect.hideWithoutReturnToMenu === 'function';
        this.addTestResult(
            'hideWithoutReturnToMenu方法检查',
            hasMethod,
            hasMethod ? '✅ hideWithoutReturnToMenu方法存在' : '❌ hideWithoutReturnToMenu方法不存在'
        );
    }

    /**
     * 添加测试结果
     * @param {string} testName - 测试名称
     * @param {boolean} passed - 是否通过
     * @param {string} message - 结果消息
     */
    addTestResult(testName, passed, message) {
        this.totalTests++;
        if (passed) {
            this.passedTests++;
        }

        this.testResults.push({
            name: testName,
            passed: passed,
            message: message
        });

        console.log(`${passed ? '✅' : '❌'} ${testName}: ${message}`);
    }

    /**
     * 生成测试报告
     */
    generateTestReport() {
        console.log('='.repeat(50));
        console.log('📊 测试报告');
        console.log('='.repeat(50));

        this.testResults.forEach(result => {
            console.log(`${result.passed ? '✅' : '❌'} ${result.name}: ${result.message}`);
        });

        console.log('='.repeat(50));
        const success = this.passedTests === this.totalTests;
        const emoji = success ? '🎉' : '😞';
        console.log(`总体测试结果: ${this.passedTests}/${this.totalTests} 个测试通过 ${emoji}`);

        if (success) {
            console.log('🎉 所有测试通过！关卡选择修复成功！');
        } else {
            console.log('😞 部分测试失败，需要进一步修复。');
        }

        return success;
    }
}

// 创建测试实例并运行
const fixVerificationTest = new FixVerificationTest();

// 页面加载完成后自动运行测试
document.addEventListener('DOMContentLoaded', async () => {
    // 等待一段时间确保所有模块都已加载
    setTimeout(async () => {
        await fixVerificationTest.runAllTests();
    }, 2000);
});

// 导出到全局作用域以便手动调用
window.fixVerificationTest = fixVerificationTest;
