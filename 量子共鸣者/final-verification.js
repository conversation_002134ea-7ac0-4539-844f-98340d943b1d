/**
 * 最终验证脚本 - 验证关卡选择界面修复的完整性
 * 包括初始化修复和样式可见性修复
 */

console.log('🔧 开始最终验证...');

// 等待页面完全加载
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(runFinalVerification, 2000);
});

function runFinalVerification() {
    console.log('🎯 执行最终验证测试');
    
    const results = {
        passed: 0,
        failed: 0,
        tests: []
    };
    
    // 测试1: 基础组件检查
    console.log('\n📋 测试1: 基础组件检查');
    
    if (typeof LevelSelect !== 'undefined') {
        logTest(results, '✅ LevelSelect 类存在', true);
    } else {
        logTest(results, '❌ LevelSelect 类不存在', false);
    }
    
    if (window.levelSelect instanceof LevelSelect) {
        logTest(results, '✅ levelSelect 全局实例存在', true);
    } else {
        logTest(results, '❌ levelSelect 全局实例不存在', false);
    }
    
    if (window.uiManager && typeof window.uiManager.showScreen === 'function') {
        logTest(results, '✅ uiManager 和 showScreen 方法存在', true);
    } else {
        logTest(results, '❌ uiManager 或 showScreen 方法不存在', false);
    }
    
    const container = document.getElementById('levelSelectScreen');
    if (container) {
        logTest(results, '✅ levelSelectScreen DOM 容器存在', true);
    } else {
        logTest(results, '❌ levelSelectScreen DOM 容器不存在', false);
        return; // 无法继续测试
    }
    
    // 测试2: 样式状态检查
    console.log('\n🎨 测试2: 样式状态检查');
    
    const computedStyle = window.getComputedStyle(container);
    const defaultOpacity = computedStyle.opacity;
    const defaultVisibility = computedStyle.visibility;
    
    if (defaultOpacity === '0' && defaultVisibility === 'hidden') {
        logTest(results, '✅ 默认样式状态正确 (opacity: 0, visibility: hidden)', true);
    } else {
        logTest(results, `❌ 默认样式状态异常 (opacity: ${defaultOpacity}, visibility: ${defaultVisibility})`, false);
    }
    
    // 测试3: Active 类效果测试
    console.log('\n✨ 测试3: Active 类效果测试');
    
    container.classList.add('active');
    
    setTimeout(() => {
        const activeStyle = window.getComputedStyle(container);
        const activeOpacity = activeStyle.opacity;
        const activeVisibility = activeStyle.visibility;
        
        if (activeOpacity === '1' && activeVisibility === 'visible') {
            logTest(results, '✅ Active 类效果正确 (opacity: 1, visibility: visible)', true);
        } else {
            logTest(results, `❌ Active 类效果异常 (opacity: ${activeOpacity}, visibility: ${activeVisibility})`, false);
        }
        
        container.classList.remove('active');
        
        // 测试4: UI Manager 集成测试
        setTimeout(() => runUIManagerTest(results, container), 500);
        
    }, 100);
}

function runUIManagerTest(results, container) {
    console.log('\n🔧 测试4: UI Manager 集成测试');
    
    try {
        // 保存原始状态
        const originalDisplay = container.style.display;
        const originalClasses = Array.from(container.classList);
        
        // 调用 UI Manager
        window.uiManager.showScreen('levelSelectScreen');
        
        setTimeout(() => {
            const display = container.style.display;
            const hasActive = container.classList.contains('active');
            const computedStyle = window.getComputedStyle(container);
            const opacity = computedStyle.opacity;
            const visibility = computedStyle.visibility;
            
            console.log('UI Manager 调用后状态:', {
                display, hasActive, opacity, visibility
            });
            
            if (display === 'flex') {
                logTest(results, '✅ UI Manager 设置 display: flex', true);
            } else {
                logTest(results, `❌ UI Manager 未正确设置 display (${display})`, false);
            }
            
            if (hasActive) {
                logTest(results, '✅ UI Manager 添加 active 类', true);
            } else {
                logTest(results, '❌ UI Manager 未添加 active 类', false);
            }
            
            if (opacity === '1' && visibility === 'visible') {
                logTest(results, '✅ 界面完全可见', true);
            } else {
                logTest(results, `❌ 界面不可见 (opacity: ${opacity}, visibility: ${visibility})`, false);
            }
            
            // 测试5: 内容生成测试
            setTimeout(() => runContentTest(results, container, originalDisplay, originalClasses), 1000);
            
        }, 500);
        
    } catch (error) {
        logTest(results, `❌ UI Manager 调用失败: ${error.message}`, false);
        showFinalResults(results);
    }
}

function runContentTest(results, container, originalDisplay, originalClasses) {
    console.log('\n📝 测试5: 内容生成测试');
    
    try {
        const levelGrid = document.getElementById('levelGrid');
        if (levelGrid) {
            logTest(results, '✅ 关卡网格容器存在', true);
            
            if (levelGrid.children.length > 0) {
                logTest(results, `✅ 关卡网格有内容 (${levelGrid.children.length} 个关卡)`, true);
            } else {
                logTest(results, '❌ 关卡网格为空', false);
            }
        } else {
            logTest(results, '❌ 关卡网格容器不存在', false);
        }
        
        const previewElement = document.getElementById('levelPreview');
        if (previewElement) {
            logTest(results, '✅ 关卡预览容器存在', true);
        } else {
            logTest(results, '❌ 关卡预览容器不存在', false);
        }
        
        const startButton = document.getElementById('startLevelButton');
        if (startButton) {
            logTest(results, '✅ 开始游戏按钮存在', true);
        } else {
            logTest(results, '❌ 开始游戏按钮不存在', false);
        }
        
    } catch (error) {
        logTest(results, `❌ 内容检查失败: ${error.message}`, false);
    }
    
    // 恢复原始状态
    setTimeout(() => {
        container.style.display = originalDisplay;
        container.className = '';
        originalClasses.forEach(cls => container.classList.add(cls));
        
        console.log('🔄 已恢复原始状态');
        showFinalResults(results);
    }, 2000);
}

function logTest(results, message, passed) {
    console.log(message);
    results.tests.push({ message, passed });
    if (passed) {
        results.passed++;
    } else {
        results.failed++;
    }
}

function showFinalResults(results) {
    console.log('\n' + '='.repeat(60));
    console.log('🎯 最终验证结果');
    console.log('='.repeat(60));
    
    console.log(`✅ 通过测试: ${results.passed}`);
    console.log(`❌ 失败测试: ${results.failed}`);
    console.log(`📊 总计测试: ${results.tests.length}`);
    console.log(`📈 成功率: ${Math.round((results.passed / results.tests.length) * 100)}%`);
    
    console.log('\n📋 详细结果:');
    results.tests.forEach((test, index) => {
        console.log(`${index + 1}. ${test.message}`);
    });
    
    if (results.failed === 0) {
        console.log('\n🎉 所有测试通过！关卡选择界面修复完全成功！');
        console.log('💡 现在点击"开始游戏"按钮应该能正常显示关卡选择界面');
        
        // 显示成功通知
        if (window.notificationSystem) {
            setTimeout(() => {
                window.notificationSystem.success(
                    '所有测试都通过了，现在可以正常使用关卡选择功能。',
                    { title: '关卡选择界面修复验证成功', duration: 5000 }
                );
            }, 1000);
        }
    } else {
        console.log('\n⚠️ 部分测试失败，可能仍有问题需要解决');
        
        // 显示警告通知
        if (window.notificationSystem) {
            setTimeout(() => {
                window.notificationSystem.warning(
                    `${results.failed} 个测试失败，请检查控制台了解详情。`,
                    { title: '验证发现问题', duration: 6000 }
                );
            }, 1000);
        }
    }
    
    console.log('='.repeat(60));
}

// 添加手动验证函数
window.manualVerification = function() {
    console.log('🔧 开始手动验证...');
    runFinalVerification();
};

console.log('🔧 最终验证脚本已加载');
console.log('💡 可以在控制台运行 manualVerification() 进行手动验证');
