/* 量子共鸣者 - 响应式样式 */

/* 平板设备 (768px - 1024px) */
@media screen and (max-width: 1024px) {
    .main-title {
        font-size: 3rem;
    }
    
    .menu-container {
        max-width: 450px;
        padding: 1.5rem;
    }
    
    .settings-container {
        max-width: 500px;
        padding: 1.5rem;
    }
    
    .frequency-controls {
        gap: 1.5rem;
    }
    
    .hud-top {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .level-info,
    .score-info,
    .combo-info {
        min-width: 80px;
    }
}

/* 移动设备 (最大宽度 768px) */
@media screen and (max-width: 768px) {
    :root {
        font-size: 14px;
    }
    
    /* 主菜单移动端适配 */
    .main-title {
        font-size: 2.5rem;
    }
    
    .main-subtitle {
        font-size: 1rem;
    }
    
    .menu-container {
        max-width: 100%;
        padding: 1rem;
        margin: 0 1rem;
    }
    
    .menu-btn {
        padding: 0.8rem 1rem;
        font-size: 1rem;
    }
    
    .btn-icon {
        font-size: 1.2rem;
    }
    
    .menu-footer {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .player-info,
    .language-selector {
        justify-content: center;
    }
    
    /* 游戏界面移动端适配 */
    .game-hud {
        padding: 0.5rem;
    }
    
    .hud-top {
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
    }
    
    .level-info,
    .score-info,
    .combo-info {
        flex-direction: row;
        gap: 0.5rem;
        align-items: center;
    }
    
    .level-number,
    .score-value,
    .combo-value {
        font-size: 1.2rem;
    }
    
    .hud-controls {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
    }
    
    .control-btn {
        width: 36px;
        height: 36px;
    }
    
    /* 频率控制面板移动端适配 */
    .frequency-panel {
        padding: 0.8rem;
    }
    
    .frequency-controls {
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 0.8rem;
    }
    
    .frequency-slider-container {
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
    }
    
    .frequency-label {
        text-align: center;
        min-width: auto;
    }
    
    .frequency-value {
        text-align: center;
        min-width: auto;
    }
    
    .resonance-indicator {
        min-width: auto;
        width: 100%;
    }
    
    .resonance-meter {
        width: 100%;
    }
    
    .audio-visualizer {
        height: 50px;
    }
    
    /* 模态框移动端适配 */
    .pause-container,
    .settings-container {
        max-width: 100%;
        margin: 1rem;
        padding: 1.5rem;
        max-height: calc(100vh - 2rem);
    }
    
    .pause-title,
    .settings-title {
        font-size: 1.5rem;
    }
    
    .pause-btn {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }
    
    .settings-footer {
        flex-direction: column;
    }
    
    .setting-item {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }
    
    .setting-label {
        min-width: auto;
        text-align: center;
    }
    
    .setting-slider {
        max-width: 100%;
    }
    
    .setting-value {
        text-align: center;
        min-width: auto;
    }
    
    /* 加载屏幕移动端适配 */
    .loading-container {
        padding: 1rem;
        max-width: 100%;
        margin: 0 1rem;
    }
    
    .game-title {
        font-size: 2rem;
    }
    
    .quantum-logo {
        width: 100px;
        height: 100px;
    }
}

/* 小屏幕移动设备 (最大宽度 480px) */
@media screen and (max-width: 480px) {
    :root {
        font-size: 13px;
    }
    
    .main-title {
        font-size: 2rem;
    }
    
    .game-title {
        font-size: 1.8rem;
    }
    
    .quantum-logo {
        width: 80px;
        height: 80px;
        margin-bottom: 1.5rem;
    }
    
    .menu-btn {
        padding: 0.7rem 0.8rem;
        font-size: 0.9rem;
    }
    
    .btn-icon {
        font-size: 1.1rem;
        min-width: 1.2rem;
    }
    
    .frequency-panel {
        padding: 0.6rem;
    }
    
    .audio-visualizer {
        height: 40px;
    }
    
    .pause-container,
    .settings-container {
        margin: 0.5rem;
        padding: 1rem;
    }
    
    .pause-title,
    .settings-title {
        font-size: 1.3rem;
    }
    
    .section-title {
        font-size: 1rem;
    }
}

/* 横屏模式适配 */
@media screen and (max-height: 600px) and (orientation: landscape) {
    .menu-container {
        padding: 1rem;
    }
    
    .main-title {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }
    
    .main-subtitle {
        font-size: 0.9rem;
    }
    
    .game-header {
        margin-bottom: 1.5rem;
    }
    
    .main-menu {
        margin-bottom: 1.5rem;
    }
    
    .menu-btn {
        padding: 0.6rem 1rem;
    }
    
    .frequency-panel {
        padding: 0.5rem;
    }
    
    .frequency-controls {
        margin-bottom: 0.5rem;
    }
    
    .audio-visualizer {
        height: 40px;
    }
    
    .game-hud {
        padding: 0.5rem;
    }
    
    .hud-top {
        margin-bottom: 0.5rem;
    }
}

/* 超小屏幕设备 (最大宽度 320px) */
@media screen and (max-width: 320px) {
    :root {
        font-size: 12px;
    }
    
    .main-title {
        font-size: 1.8rem;
    }
    
    .game-title {
        font-size: 1.5rem;
    }
    
    .quantum-logo {
        width: 70px;
        height: 70px;
    }
    
    .menu-container,
    .pause-container,
    .settings-container {
        margin: 0.25rem;
        padding: 0.8rem;
    }
    
    .menu-btn {
        padding: 0.6rem;
        font-size: 0.8rem;
    }
    
    .control-btn {
        width: 32px;
        height: 32px;
    }
    
    .frequency-panel {
        padding: 0.4rem;
    }
}

/* 高分辨率屏幕适配 */
@media screen and (min-width: 1440px) {
    .menu-container {
        max-width: 600px;
        padding: 3rem;
    }
    
    .main-title {
        font-size: 4rem;
    }
    
    .settings-container {
        max-width: 700px;
        padding: 3rem;
    }
    
    .frequency-controls {
        gap: 3rem;
    }
    
    .audio-visualizer {
        height: 80px;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .menu-btn,
    .pause-btn,
    .settings-btn,
    .control-btn {
        min-height: 44px; /* iOS推荐的最小触摸目标 */
    }
    
    .frequency-slider::-webkit-slider-thumb {
        width: 24px;
        height: 24px;
    }
    
    .setting-slider::-webkit-slider-thumb {
        width: 20px;
        height: 20px;
    }
    
    .setting-checkbox {
        width: 24px;
        height: 24px;
    }
    
    /* 移除悬停效果，避免触摸设备的粘滞问题 */
    .menu-btn:hover,
    .pause-btn:hover,
    .settings-btn:hover,
    .control-btn:hover {
        transform: none;
    }
    
    /* 添加触摸反馈 */
    .menu-btn:active,
    .pause-btn:active,
    .settings-btn:active,
    .control-btn:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .particle-orbit,
    .particle,
    .resonance-wave {
        animation: none !important;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    /* 已经是深色主题，无需额外调整 */
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --text-primary: #ffffff;
        --text-secondary: #e0e0e0;
        --primary-color: #8a7fff;
        --secondary-color: #00e5d6;
        --bg-primary: #000000;
        --bg-secondary: #1a1a1a;
    }
    
    .menu-btn,
    .pause-btn,
    .settings-btn {
        border-width: 2px;
    }
}
