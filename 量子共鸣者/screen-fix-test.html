<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>屏幕显示修复测试 - 量子共鸣者</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/game.css">
    <link rel="stylesheet" href="styles/ui-components.css">
    <link rel="stylesheet" href="styles/responsive.css">
    <style>
        .test-panel {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            z-index: 1000;
            max-width: 400px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .test-button {
            margin: 5px;
            padding: 8px 12px;
            background: #4a90e2;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .test-button:hover {
            background: #357abd;
        }
        
        .test-log {
            background: #1a1a1a;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 11px;
        }
        
        .status-good { color: #4CAF50; }
        .status-warning { color: #FF9800; }
        .status-error { color: #F44336; }
    </style>
</head>
<body>
    <div class="test-panel">
        <h3>屏幕显示修复测试</h3>
        
        <div>
            <button class="test-button" onclick="testGameScreenDisplay()">测试游戏屏幕显示</button>
            <button class="test-button" onclick="testScreenSwitching()">测试屏幕切换</button>
            <button class="test-button" onclick="debugAllScreens()">调试所有屏幕</button>
            <button class="test-button" onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="test-log" id="test-log">等待测试...</div>
    </div>

    <!-- 游戏屏幕 -->
    <div id="game-screen" class="screen">
        <div class="game-hud">
            <div class="hud-top">
                <div class="level-info">
                    <span class="level-label">关卡</span>
                    <span class="level-number">1</span>
                </div>
                <div class="score-info">
                    <span class="score-label">分数</span>
                    <span class="score-value">0</span>
                </div>
                <div class="time-info">
                    <span class="time-label">时间</span>
                    <span class="time-value">00:00</span>
                </div>
            </div>
        </div>
        
        <div class="game-content">
            <h1 style="color: white; text-align: center; margin-top: 100px;">
                量子共鸣者 - 游戏屏幕
            </h1>
            <p style="color: white; text-align: center; margin: 20px;">
                如果您能看到这个文本，说明游戏屏幕显示正常！
            </p>
            <div style="text-align: center;">
                <button class="test-button" onclick="hideGameScreen()">隐藏游戏屏幕</button>
            </div>
        </div>
    </div>

    <!-- 测试屏幕 -->
    <div id="test-screen" class="screen">
        <div style="text-align: center; color: white; padding: 50px;">
            <h1>测试屏幕</h1>
            <p>这是用于测试的屏幕</p>
            <button class="test-button" onclick="showGameScreen()">显示游戏屏幕</button>
        </div>
    </div>

    <script>
        let testLog = document.getElementById('test-log');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'status-error' : 
                            type === 'warning' ? 'status-warning' : 
                            type === 'success' ? 'status-good' : '';
            
            testLog.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            testLog.scrollTop = testLog.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            testLog.innerHTML = '';
        }
        
        function getScreenInfo(screenId) {
            const screen = document.getElementById(screenId);
            if (!screen) return null;
            
            const computedStyle = window.getComputedStyle(screen);
            const hasActiveClass = screen.classList.contains('active');
            
            return {
                element: screen,
                hasActiveClass,
                opacity: computedStyle.opacity,
                visibility: computedStyle.visibility,
                display: computedStyle.display,
                position: computedStyle.position,
                zIndex: computedStyle.zIndex,
                isVisible: computedStyle.opacity === '1' && computedStyle.visibility === 'visible'
            };
        }
        
        function testGameScreenDisplay() {
            log('🧪 开始测试游戏屏幕显示', 'info');
            
            const gameScreen = document.getElementById('game-screen');
            if (!gameScreen) {
                log('❌ 游戏屏幕元素不存在', 'error');
                return;
            }
            
            // 测试1：检查初始状态
            log('📋 检查初始状态...', 'info');
            let info = getScreenInfo('game-screen');
            log(`  - 有active类: ${info.hasActiveClass}`, info.hasActiveClass ? 'success' : 'warning');
            log(`  - opacity: ${info.opacity}`, info.opacity === '1' ? 'success' : 'warning');
            log(`  - visibility: ${info.visibility}`, info.visibility === 'visible' ? 'success' : 'warning');
            log(`  - display: ${info.display}`, info.display === 'flex' ? 'success' : 'warning');
            
            // 测试2：尝试显示屏幕
            log('🎬 尝试显示游戏屏幕...', 'info');
            showGameScreen();
            
            // 测试3：延迟检查结果
            setTimeout(() => {
                info = getScreenInfo('game-screen');
                log('📋 显示后状态检查:', 'info');
                log(`  - 有active类: ${info.hasActiveClass}`, info.hasActiveClass ? 'success' : 'error');
                log(`  - opacity: ${info.opacity}`, info.opacity === '1' ? 'success' : 'error');
                log(`  - visibility: ${info.visibility}`, info.visibility === 'visible' ? 'success' : 'error');
                log(`  - 整体可见: ${info.isVisible}`, info.isVisible ? 'success' : 'error');
                
                if (info.isVisible) {
                    log('✅ 游戏屏幕显示测试通过', 'success');
                } else {
                    log('❌ 游戏屏幕显示测试失败', 'error');
                }
            }, 300);
        }
        
        function testScreenSwitching() {
            log('🔄 开始测试屏幕切换', 'info');
            
            // 显示测试屏幕
            showTestScreen();
            
            setTimeout(() => {
                log('切换到游戏屏幕...', 'info');
                showGameScreen();
                
                setTimeout(() => {
                    const gameInfo = getScreenInfo('game-screen');
                    const testInfo = getScreenInfo('test-screen');
                    
                    log('屏幕切换结果:', 'info');
                    log(`  - 游戏屏幕可见: ${gameInfo.isVisible}`, gameInfo.isVisible ? 'success' : 'error');
                    log(`  - 测试屏幕隐藏: ${!testInfo.isVisible}`, !testInfo.isVisible ? 'success' : 'error');
                    
                    if (gameInfo.isVisible && !testInfo.isVisible) {
                        log('✅ 屏幕切换测试通过', 'success');
                    } else {
                        log('❌ 屏幕切换测试失败', 'error');
                    }
                }, 300);
            }, 300);
        }
        
        function debugAllScreens() {
            log('🔍 调试所有屏幕状态', 'info');
            
            const screens = document.querySelectorAll('.screen');
            screens.forEach(screen => {
                const info = getScreenInfo(screen.id);
                log(`屏幕 ${screen.id}:`, 'info');
                log(`  - active类: ${info.hasActiveClass}`, 'info');
                log(`  - opacity: ${info.opacity}`, 'info');
                log(`  - visibility: ${info.visibility}`, 'info');
                log(`  - display: ${info.display}`, 'info');
                log(`  - position: ${info.position}`, 'info');
                log(`  - z-index: ${info.zIndex}`, 'info');
                log(`  - 可见: ${info.isVisible}`, info.isVisible ? 'success' : 'warning');
                log('---', 'info');
            });
        }
        
        function showGameScreen() {
            log('🎮 显示游戏屏幕', 'info');
            hideAllScreens();
            
            const gameScreen = document.getElementById('game-screen');
            if (gameScreen) {
                gameScreen.classList.add('active');
                gameScreen.style.opacity = '1';
                gameScreen.style.visibility = 'visible';
                gameScreen.style.display = 'flex';
                gameScreen.style.zIndex = '10';
                log('✅ 游戏屏幕显示命令执行完成', 'success');
            }
        }
        
        function showTestScreen() {
            log('🧪 显示测试屏幕', 'info');
            hideAllScreens();
            
            const testScreen = document.getElementById('test-screen');
            if (testScreen) {
                testScreen.classList.add('active');
                testScreen.style.opacity = '1';
                testScreen.style.visibility = 'visible';
                testScreen.style.display = 'flex';
                testScreen.style.zIndex = '10';
            }
        }
        
        function hideGameScreen() {
            log('🚫 隐藏游戏屏幕', 'info');
            const gameScreen = document.getElementById('game-screen');
            if (gameScreen) {
                gameScreen.classList.remove('active');
            }
        }
        
        function hideAllScreens() {
            const screens = document.querySelectorAll('.screen');
            screens.forEach(screen => {
                screen.classList.remove('active');
            });
        }
        
        // 初始化
        log('🚀 屏幕显示修复测试工具已加载', 'success');
        log('💡 点击按钮开始测试', 'info');
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case '1':
                    testGameScreenDisplay();
                    break;
                case '2':
                    testScreenSwitching();
                    break;
                case '3':
                    debugAllScreens();
                    break;
                case 'g':
                    showGameScreen();
                    break;
                case 'h':
                    hideAllScreens();
                    break;
            }
        });
        
        log('⌨️ 快捷键: 1-测试显示, 2-测试切换, 3-调试状态, g-显示游戏屏幕, h-隐藏所有', 'info');
    </script>
</body>
</html>
