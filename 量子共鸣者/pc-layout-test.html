<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PC端布局测试 - 量子共鸣者</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/ui-components.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
            overflow: hidden; /* 模拟全屏应用 */
        }
        
        .test-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 3000;
            background: rgba(0, 0, 0, 0.9);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #333;
            max-width: 300px;
        }
        
        .test-btn {
            display: block;
            width: 100%;
            padding: 8px 12px;
            margin: 5px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 4px;
            color: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-size: 11px;
            font-family: monospace;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4caf50;
            color: #4caf50;
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: #f44336;
        }
        
        .status.info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196f3;
            color: #2196f3;
        }
        
        .screen-info {
            position: fixed;
            bottom: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            color: #ccc;
            z-index: 3000;
        }
    </style>
</head>
<body>
    <div class="test-controls">
        <h4>🖥️ PC端布局测试</h4>
        
        <button class="test-btn" onclick="showLevelSelect()">显示关卡选择界面</button>
        <button class="test-btn" onclick="testScrolling()">测试滚动功能</button>
        <button class="test-btn" onclick="testDifficultySelector()">测试难度选择器</button>
        <button class="test-btn" onclick="measureLayout()">测量布局尺寸</button>
        <button class="test-btn" onclick="simulateSmallScreen()">模拟小屏幕</button>
        <button class="test-btn" onclick="resetLayout()">重置布局</button>
        
        <div id="status" class="status info">
            准备进行PC端布局测试...
        </div>
    </div>
    
    <div class="screen-info" id="screenInfo">
        屏幕尺寸: <span id="screenSize">--</span><br>
        视口尺寸: <span id="viewportSize">--</span>
    </div>
    
    <!-- 关卡选择屏幕 -->
    <div id="levelSelectScreen" class="screen level-select-screen active" style="display: flex;">
        <div class="level-select-header">
            <h2>🎯 关卡选择</h2>
            <button class="level-back-button" id="levelBackButton">返回主菜单</button>
        </div>
        
        <div class="level-select-content">
            <div class="level-grid" id="levelGrid">
                <!-- 模拟关卡卡片 -->
                <div class="level-card">
                    <div class="level-card-header">
                        <h3>教程关卡</h3>
                        <div class="level-difficulty-badges">
                            <span class="difficulty-badge easy">简单</span>
                        </div>
                    </div>
                    <div class="level-description">学习基本的量子共鸣机制</div>
                    <div class="level-stats">
                        <span>粒子: 5</span>
                        <span>连接: 3</span>
                    </div>
                </div>
                
                <div class="level-card">
                    <div class="level-card-header">
                        <h3>基础共鸣</h3>
                        <div class="level-difficulty-badges">
                            <span class="difficulty-badge normal">普通</span>
                            <span class="difficulty-badge hard">困难</span>
                        </div>
                    </div>
                    <div class="level-description">掌握基本的共鸣技巧</div>
                    <div class="level-stats">
                        <span>粒子: 8</span>
                        <span>连接: 6</span>
                    </div>
                </div>
                
                <div class="level-card">
                    <div class="level-card-header">
                        <h3>连锁反应</h3>
                        <div class="level-difficulty-badges">
                            <span class="difficulty-badge normal">普通</span>
                            <span class="difficulty-badge hard">困难</span>
                            <span class="difficulty-badge expert">专家</span>
                        </div>
                    </div>
                    <div class="level-description">创造复杂的连锁反应</div>
                    <div class="level-stats">
                        <span>粒子: 12</span>
                        <span>连接: 10</span>
                    </div>
                </div>
                
                <!-- 添加更多卡片来测试滚动 -->
                <div class="level-card">
                    <div class="level-card-header">
                        <h3>高级挑战</h3>
                        <div class="level-difficulty-badges">
                            <span class="difficulty-badge expert">专家</span>
                        </div>
                    </div>
                    <div class="level-description">终极挑战关卡</div>
                    <div class="level-stats">
                        <span>粒子: 20</span>
                        <span>连接: 15</span>
                    </div>
                </div>
            </div>
            
            <div class="level-preview" id="levelPreview">
                <div class="preview-header">
                    <h3 id="previewTitle">基础共鸣</h3>
                    <div class="preview-stars">
                        <span class="star filled">★</span>
                        <span class="star filled">★</span>
                        <span class="star">★</span>
                    </div>
                </div>
                
                <div class="preview-description" id="previewDescription">
                    这是一个测试关卡，用于验证PC端布局是否正常。在这个关卡中，你需要掌握基本的量子共鸣技巧，创建稳定的粒子连接。
                </div>
                
                <div class="preview-canvas-container">
                    <canvas id="levelPreviewCanvas" width="300" height="200" style="background: #1a1a1a; border: 1px solid #333;"></canvas>
                </div>
                
                <div class="preview-stats" id="previewStats">
                    <div class="stat-item">
                        <span class="stat-label">最佳分数</span>
                        <span class="stat-value" id="previewBestScore">1250</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">粒子数量</span>
                        <span class="stat-value" id="previewParticles">8</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">连接数量</span>
                        <span class="stat-value" id="previewConnections">6</span>
                    </div>
                </div>
                
                <div class="difficulty-selector" id="difficultySelector">
                    <h4>选择难度</h4>
                    <div class="difficulty-buttons" id="difficultyButtons">
                        <button class="difficulty-button normal">普通</button>
                        <button class="difficulty-button hard selected">困难</button>
                        <button class="difficulty-button expert">专家</button>
                    </div>
                </div>
                
                <div class="preview-actions">
                    <button class="quantum-button primary" id="startLevelButton">
                        开始游戏
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        function updateScreenInfo() {
            const screenSize = `${screen.width}x${screen.height}`;
            const viewportSize = `${window.innerWidth}x${window.innerHeight}`;
            
            document.getElementById('screenSize').textContent = screenSize;
            document.getElementById('viewportSize').textContent = viewportSize;
        }
        
        function showLevelSelect() {
            updateStatus('显示关卡选择界面...', 'info');
            
            const screen = document.getElementById('levelSelectScreen');
            screen.style.display = 'flex';
            screen.classList.add('active');
            
            setTimeout(() => {
                updateStatus('✅ 关卡选择界面已显示', 'success');
            }, 100);
        }
        
        function testScrolling() {
            updateStatus('测试滚动功能...', 'info');
            
            const preview = document.getElementById('levelPreview');
            const difficultySelector = document.getElementById('difficultySelector');
            
            // 检查预览区域是否可滚动
            const isScrollable = preview.scrollHeight > preview.clientHeight;
            const difficultyRect = difficultySelector.getBoundingClientRect();
            const previewRect = preview.getBoundingClientRect();
            
            console.log('滚动测试结果:', {
                isScrollable,
                previewHeight: preview.clientHeight,
                scrollHeight: preview.scrollHeight,
                difficultyTop: difficultyRect.top,
                difficultyBottom: difficultyRect.bottom,
                previewBottom: previewRect.bottom,
                isVisible: difficultyRect.bottom <= previewRect.bottom
            });
            
            if (isScrollable) {
                updateStatus('✅ 预览区域可滚动', 'success');
                
                // 测试滚动到难度选择器
                setTimeout(() => {
                    difficultySelector.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    updateStatus('🔄 已滚动到难度选择器', 'info');
                }, 1000);
            } else {
                updateStatus('ℹ️ 预览区域无需滚动', 'info');
            }
        }
        
        function testDifficultySelector() {
            updateStatus('测试难度选择器...', 'info');
            
            const difficultySelector = document.getElementById('difficultySelector');
            const rect = difficultySelector.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            
            const isVisible = rect.top >= 0 && rect.bottom <= viewportHeight;
            const isPartiallyVisible = rect.top < viewportHeight && rect.bottom > 0;
            
            console.log('难度选择器位置:', {
                top: rect.top,
                bottom: rect.bottom,
                viewportHeight,
                isVisible,
                isPartiallyVisible
            });
            
            if (isVisible) {
                updateStatus('✅ 难度选择器完全可见', 'success');
            } else if (isPartiallyVisible) {
                updateStatus('⚠️ 难度选择器部分可见', 'error');
                
                // 滚动到可见位置
                setTimeout(() => {
                    difficultySelector.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    updateStatus('🔄 已调整到可见位置', 'info');
                }, 1000);
            } else {
                updateStatus('❌ 难度选择器不可见', 'error');
            }
        }
        
        function measureLayout() {
            updateStatus('测量布局尺寸...', 'info');
            
            const content = document.querySelector('.level-select-content');
            const preview = document.getElementById('levelPreview');
            const grid = document.getElementById('levelGrid');
            
            const measurements = {
                viewport: `${window.innerWidth}x${window.innerHeight}`,
                content: `${content.offsetWidth}x${content.offsetHeight}`,
                preview: `${preview.offsetWidth}x${preview.offsetHeight}`,
                previewScroll: `${preview.scrollHeight}`,
                grid: `${grid.offsetWidth}x${grid.offsetHeight}`
            };
            
            console.log('布局测量结果:', measurements);
            
            let message = `视口: ${measurements.viewport}, 预览: ${measurements.preview}`;
            if (preview.scrollHeight > preview.clientHeight) {
                message += ` (可滚动: ${preview.scrollHeight}px)`;
            }
            
            updateStatus(message, 'info');
        }
        
        function simulateSmallScreen() {
            updateStatus('模拟小屏幕...', 'info');
            
            document.body.style.transform = 'scale(0.7)';
            document.body.style.transformOrigin = 'top left';
            
            setTimeout(() => {
                updateStatus('🔍 已缩放到70%', 'info');
                testDifficultySelector();
            }, 500);
        }
        
        function resetLayout() {
            updateStatus('重置布局...', 'info');
            
            document.body.style.transform = '';
            document.body.style.transformOrigin = '';
            
            const preview = document.getElementById('levelPreview');
            preview.scrollTop = 0;
            
            setTimeout(() => {
                updateStatus('✅ 布局已重置', 'success');
            }, 100);
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateScreenInfo();
            updateStatus('PC端布局测试已准备就绪', 'info');
            
            // 监听窗口大小变化
            window.addEventListener('resize', updateScreenInfo);
            
            // 自动运行基础测试
            setTimeout(() => {
                measureLayout();
                setTimeout(testDifficultySelector, 1000);
            }, 500);
        });
    </script>
</body>
</html>
