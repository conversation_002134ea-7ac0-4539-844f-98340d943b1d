<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关卡选择测试 - 量子共鸣者</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/ui-components.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a0a0a;
            color: #fff;
            font-family: 'Arial', sans-serif;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .test-btn {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .debug-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 14px;
        }
        
        .screen {
            display: none;
            position: relative;
            width: 100%;
            min-height: 500px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 20px;
        }
        
        .screen.active {
            display: flex;
            flex-direction: column;
        }
        
        .level-select-screen {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🎯 关卡选择界面测试</h1>
            <p>测试关卡选择界面的初始化和渲染功能</p>
        </div>
        
        <div class="test-buttons">
            <button class="test-btn" onclick="testLevelSelectInit()">初始化关卡选择</button>
            <button class="test-btn" onclick="testLevelSelectShow()">显示关卡选择</button>
            <button class="test-btn" onclick="testLevelSelectHide()">隐藏关卡选择</button>
            <button class="test-btn" onclick="showDebugInfo()">显示调试信息</button>
        </div>
        
        <div id="debugInfo" class="debug-info">
            <h3>🔍 调试信息</h3>
            <div id="debugContent">点击"显示调试信息"按钮查看状态</div>
        </div>
        
        <!-- 关卡选择屏幕 -->
        <div id="levelSelectScreen" class="screen level-select-screen">
            <!-- 关卡选择内容将由JavaScript动态生成 -->
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/ui/level-select.js"></script>
    
    <script>
        // 测试脚本
        let testResults = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            testResults.push(logEntry);
            console.log(logEntry);
        }
        
        function testLevelSelectInit() {
            log('开始测试关卡选择初始化');
            
            try {
                if (window.levelSelect) {
                    log('✅ levelSelect 实例存在');
                    
                    const result = window.levelSelect.init();
                    if (result) {
                        log('✅ 关卡选择初始化成功');
                    } else {
                        log('❌ 关卡选择初始化失败', 'error');
                    }
                } else {
                    log('❌ levelSelect 实例不存在', 'error');
                }
            } catch (error) {
                log(`❌ 初始化过程中发生错误: ${error.message}`, 'error');
            }
        }
        
        function testLevelSelectShow() {
            log('开始测试关卡选择显示');
            
            try {
                if (window.levelSelect) {
                    window.levelSelect.show();
                    
                    // 检查界面是否显示
                    const container = document.getElementById('levelSelectScreen');
                    if (container && container.style.display === 'flex') {
                        log('✅ 关卡选择界面显示成功');
                        container.classList.add('active');
                    } else {
                        log('❌ 关卡选择界面显示失败', 'error');
                    }
                } else {
                    log('❌ levelSelect 实例不存在', 'error');
                }
            } catch (error) {
                log(`❌ 显示过程中发生错误: ${error.message}`, 'error');
            }
        }
        
        function testLevelSelectHide() {
            log('开始测试关卡选择隐藏');
            
            try {
                if (window.levelSelect) {
                    window.levelSelect.hide();
                    
                    // 检查界面是否隐藏
                    setTimeout(() => {
                        const container = document.getElementById('levelSelectScreen');
                        if (container && container.style.display === 'none') {
                            log('✅ 关卡选择界面隐藏成功');
                            container.classList.remove('active');
                        } else {
                            log('❌ 关卡选择界面隐藏失败', 'error');
                        }
                    }, 600);
                } else {
                    log('❌ levelSelect 实例不存在', 'error');
                }
            } catch (error) {
                log(`❌ 隐藏过程中发生错误: ${error.message}`, 'error');
            }
        }
        
        function showDebugInfo() {
            const debugContent = document.getElementById('debugContent');
            
            let info = '<h4>🔧 系统状态</h4>';
            info += `<p><strong>levelSelect 实例:</strong> ${window.levelSelect ? '✅ 存在' : '❌ 不存在'}</p>`;
            
            if (window.levelSelect) {
                info += `<p><strong>已初始化:</strong> ${window.levelSelect.isInitialized ? '✅ 是' : '❌ 否'}</p>`;
                info += `<p><strong>可见状态:</strong> ${window.levelSelect.isVisible ? '✅ 可见' : '❌ 隐藏'}</p>`;
                info += `<p><strong>关卡数量:</strong> ${window.levelSelect.levels ? window.levelSelect.levels.length : '未知'}</p>`;
                info += `<p><strong>选中关卡:</strong> ${window.levelSelect.selectedLevel ? window.levelSelect.selectedLevel.name : '无'}</p>`;
            }
            
            const container = document.getElementById('levelSelectScreen');
            info += `<p><strong>DOM 容器:</strong> ${container ? '✅ 存在' : '❌ 不存在'}</p>`;
            if (container) {
                info += `<p><strong>显示状态:</strong> ${container.style.display || 'default'}</p>`;
                info += `<p><strong>子元素数量:</strong> ${container.children.length}</p>`;
            }
            
            info += '<h4>📋 测试日志</h4>';
            info += '<div style="max-height: 200px; overflow-y: auto;">';
            testResults.forEach(result => {
                const color = result.includes('ERROR') ? '#ff6b6b' : 
                             result.includes('✅') ? '#51cf66' : '#74c0fc';
                info += `<p style="color: ${color}; margin: 5px 0;">${result}</p>`;
            });
            info += '</div>';
            
            debugContent.innerHTML = info;
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始初始化测试环境');
            
            // 检查必要的依赖
            if (typeof LevelSelect === 'undefined') {
                log('❌ LevelSelect 类未定义', 'error');
            } else {
                log('✅ LevelSelect 类已加载');
            }
            
            // 显示初始调试信息
            setTimeout(showDebugInfo, 500);
        });
    </script>
</body>
</html>
