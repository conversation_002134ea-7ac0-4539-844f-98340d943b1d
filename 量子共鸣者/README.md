# 🌌 量子共鸣者 (Quantum Resonance)

一款创新的音乐节奏 + 物理模拟 + 策略布局游戏，玩家通过控制量子粒子的共鸣频率来创建连锁反应，在多维空间中体验独特的量子物理游戏机制。

## 🎮 游戏特色

### 核心玩法
- **量子共鸣机制**: 通过调节频率来激活具有相同或谐波频率的粒子
- **连锁反应系统**: 激活的粒子会影响周围粒子，创造壮观的连锁反应
- **多维空间**: 在3D量子场中进行策略性的粒子布局和激活
- **音乐节奏**: 游戏节奏与背景音乐同步，增强沉浸感

### 技术特色
- **Web Audio API**: 实时音频处理和频率分析
- **物理引擎**: 真实的粒子物理模拟和碰撞检测
- **量子引擎**: 独特的量子共鸣和能量传播机制
- **响应式设计**: 支持PC、平板和手机多平台
- **国际化**: 完整的中英文双语支持

## 🚀 快速开始

### 在线体验
直接在浏览器中打开 `index.html` 文件即可开始游戏。

### 测试模式
打开 `test.html` 可以测试各个游戏模块的功能。

### 系统要求
- 现代浏览器 (Chrome 80+, Firefox 75+, Safari 13+)
- 支持 Web Audio API 和 Canvas 2D
- 建议使用耳机或音响设备以获得最佳音频体验

## 🎯 游戏玩法

### 基本操作
1. **点击粒子**: 使用鼠标或触摸点击粒子来尝试激活
2. **调节频率**: 使用频率滑块或键盘方向键调节目标频率
3. **麦克风输入**: 启用麦克风，通过声音频率控制游戏
4. **观察共鸣**: 当频率匹配时，粒子会发光并激活

### 游戏目标
- **激活目标粒子**: 每个关卡都有特定的目标粒子需要激活
- **创建连锁反应**: 利用粒子间的共鸣创造更长的连锁反应
- **获得高分**: 通过连击和完美时机获得更高分数
- **时间挑战**: 在限定时间内完成关卡目标

### 高级技巧
- **频率谐波**: 利用谐波关系（2倍、3倍频率）激活更多粒子
- **能量传播**: 观察能量波的传播路径，预测连锁反应
- **时机掌握**: 在音乐节拍点激活粒子可获得额外分数
- **空间布局**: 合理利用粒子的空间位置创造最佳连锁效果

## 🛠️ 技术架构

### 模块结构
```
量子共鸣者/
├── index.html              # 主游戏页面
├── test.html              # 测试页面
├── styles/                # 样式文件
│   ├── main.css          # 主要样式
│   ├── game.css          # 游戏界面样式
│   └── responsive.css    # 响应式样式
├── js/                   # JavaScript模块
│   ├── utils/           # 工具模块
│   │   ├── storage.js   # 存储服务
│   │   ├── i18n.js      # 国际化
│   │   └── math-utils.js # 数学工具
│   ├── core/            # 核心引擎
│   │   ├── audio-engine.js    # 音频引擎
│   │   ├── physics-engine.js  # 物理引擎
│   │   ├── quantum-engine.js  # 量子引擎
│   │   └── render-engine.js   # 渲染引擎
│   ├── game/            # 游戏系统
│   │   ├── game-controller.js # 游戏控制器
│   │   ├── input-manager.js   # 输入管理器
│   │   └── level.js          # 关卡系统
│   └── app.js           # 主应用程序
├── assets/              # 资源文件
├── docs/               # 文档
└── tests/              # 测试文件
```

### 核心系统

#### 音频引擎 (Audio Engine)
- **Web Audio API集成**: 实时音频处理和分析
- **频率检测**: 麦克风输入的频率识别
- **音效系统**: 动态生成的共鸣音效和背景音乐
- **音频可视化**: 实时频谱显示

#### 物理引擎 (Physics Engine)
- **粒子系统**: 高性能的粒子物理模拟
- **碰撞检测**: 空间网格优化的碰撞检测
- **力场模拟**: 支持径向、定向和涡旋力场
- **空间优化**: 四叉树空间分割提升性能

#### 量子引擎 (Quantum Engine)
- **共鸣计算**: 基于频率差异的共鸣强度计算
- **能量传播**: 量子能量波的传播和衰减
- **连锁反应**: 复杂的粒子激活连锁机制
- **量子场**: 全局量子场强度和相位模拟

#### 渲染引擎 (Render Engine)
- **Canvas 2D渲染**: 高性能的2D图形渲染
- **粒子效果**: 发光、拖尾、共鸣可视化
- **相机系统**: 支持缩放、平移和跟随
- **后处理效果**: 辉光、模糊、色彩调整

## 🎨 视觉设计

### 量子主题
- **深空配色**: 深蓝紫色调营造神秘的量子空间感
- **粒子效果**: 发光粒子和能量波动画
- **共鸣可视化**: 频率匹配时的视觉反馈
- **UI动画**: 流畅的界面过渡和交互动画

### 响应式设计
- **多设备支持**: 自适应PC、平板、手机屏幕
- **触摸优化**: 针对触摸设备的交互优化
- **性能调节**: 根据设备性能自动调整效果质量
- **无障碍支持**: 支持键盘导航和屏幕阅读器

## 🔧 开发指南

### 本地开发
1. 克隆项目到本地
2. 使用本地服务器运行（推荐使用 Live Server 扩展）
3. 打开浏览器访问 `http://localhost:5500/index.html`

### 测试
- 运行 `test.html` 进行模块功能测试
- 使用浏览器开发者工具查看控制台日志
- 性能测试可以评估在不同设备上的运行效果

### 自定义关卡
可以通过修改 `js/app.js` 中的关卡配置来创建自定义关卡：

```javascript
levelManager.registerLevel('custom', {
    name: '自定义关卡',
    description: '这是一个自定义关卡',
    particles: [
        { x: 200, y: 200, frequency: 440, radius: 10 },
        { x: 400, y: 200, frequency: 880, radius: 10 }
    ],
    targetScore: 1000,
    timeLimit: 60
});
```

### 扩展功能
- **新的粒子类型**: 在 `physics-engine.js` 中添加新的粒子属性
- **特殊效果**: 在 `render-engine.js` 中实现新的视觉效果
- **音频效果**: 在 `audio-engine.js` 中添加新的音效
- **游戏模式**: 在 `game-controller.js` 中实现新的游戏模式

## 📱 控制方式

### 鼠标/触摸控制
- **点击**: 激活粒子
- **拖拽**: 移动视角（如果启用）
- **滚轮**: 调节频率或缩放

### 键盘控制
- **方向键**: 调节频率
- **空格键**: 特殊操作
- **ESC键**: 暂停/恢复游戏
- **R键**: 重启关卡

### 音频控制
- **麦克风**: 通过声音频率控制游戏
- **频率滑块**: 精确调节目标频率
- **预设按钮**: 快速选择常用频率

## 🌟 特色功能

### 教育价值
- **物理概念**: 通过游戏学习声波、频率、共鸣等物理概念
- **音乐理论**: 了解音程、和声、泛音等音乐理论
- **空间思维**: 培养三维空间的策略思维能力

### 创新玩法
- **实时音频**: 真实的音频输入和处理
- **物理模拟**: 基于真实物理规律的游戏机制
- **策略深度**: 需要思考和规划的策略性玩法
- **艺术表现**: 将科学概念转化为艺术体验

## 🔮 未来计划

### 短期目标
- [ ] 完善音频系统集成
- [ ] 实现3D渲染系统
- [ ] 添加更多关卡和挑战
- [ ] 优化移动端体验

### 长期愿景
- [ ] 多人协作模式
- [ ] 关卡编辑器
- [ ] 社区分享功能
- [ ] VR/AR支持
- [ ] 教育版本开发

## 📚 文档

### 开发文档
- [API文档](docs/api.md) - 详细的API接口文档和使用说明
- [部署指南](docs/deployment.md) - 本地开发、测试和生产环境部署
- [贡献指南](docs/contributing.md) - 开发者贡献指南和代码规范

### 用户文档
- [用户手册](docs/user-manual.md) - 完整的游戏使用指南和教程
- [故障排除](docs/troubleshooting.md) - 常见问题解决方案和诊断工具

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进游戏！

### 贡献指南
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 项目 Issues
- 邮箱: [<EMAIL>]

---

🌌 **量子共鸣者** - 在量子的世界中，发现音乐与物理的完美融合！
