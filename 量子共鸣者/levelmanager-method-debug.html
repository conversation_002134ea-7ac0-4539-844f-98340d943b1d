<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LevelManager 方法调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: #fff;
        }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 500px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .test-button {
            background: #e94560;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>LevelManager 方法调试</h1>
    <button class="test-button" onclick="debugLevelManager()">调试 LevelManager</button>
    <button class="test-button" onclick="clearConsole()">清空控制台</button>
    <div id="console-output"></div>

    <!-- 加载 LevelManager -->
    <script src="js/game/level-manager.js"></script>

    <script>
        // 重写 console 方法
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📋';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        function clearConsole() {
            consoleOutput.textContent = '';
        }

        function debugLevelManager() {
            console.log('🔍 开始深度调试 LevelManager...');
            
            // 检查 LevelManager 类
            console.log('📋 LevelManager 类:', typeof window.LevelManager);
            if (window.LevelManager) {
                console.log('📋 LevelManager 原型:', window.LevelManager.prototype);
                console.log('📋 LevelManager 原型方法:');
                const protoMethods = Object.getOwnPropertyNames(window.LevelManager.prototype);
                protoMethods.forEach(method => {
                    if (method !== 'constructor') {
                        console.log(`  - ${method}: ${typeof window.LevelManager.prototype[method]}`);
                    }
                });
            }
            
            // 检查 levelManager 实例
            console.log('📋 levelManager 实例:', typeof window.levelManager);
            if (window.levelManager) {
                console.log('📋 levelManager 构造函数:', window.levelManager.constructor.name);
                console.log('📋 levelManager 原型链:', Object.getPrototypeOf(window.levelManager));
                
                // 检查实例的所有属性和方法
                console.log('📋 levelManager 实例的所有属性:');
                for (let key in window.levelManager) {
                    console.log(`  - ${key}: ${typeof window.levelManager[key]}`);
                }
                
                // 检查原型上的方法
                console.log('📋 levelManager 原型上的方法:');
                const proto = Object.getPrototypeOf(window.levelManager);
                const methods = Object.getOwnPropertyNames(proto);
                methods.forEach(method => {
                    if (method !== 'constructor') {
                        console.log(`  - ${method}: ${typeof proto[method]}`);
                        console.log(`    实例上是否可访问: ${typeof window.levelManager[method]}`);
                    }
                });
                
                // 特别检查 init 方法
                console.log('📋 特别检查 init 方法:');
                console.log(`  - window.levelManager.init: ${typeof window.levelManager.init}`);
                console.log(`  - window.levelManager.hasOwnProperty('init'): ${window.levelManager.hasOwnProperty('init')}`);
                console.log(`  - 'init' in window.levelManager: ${'init' in window.levelManager}`);
                
                // 尝试直接从原型调用
                if (proto.init) {
                    console.log('📋 尝试从原型调用 init 方法...');
                    try {
                        const result = proto.init.call(window.levelManager);
                        console.log('✅ 原型方法调用成功，返回值:', result);
                    } catch (error) {
                        console.error('❌ 原型方法调用失败:', error);
                    }
                }
                
                // 尝试重新绑定方法
                console.log('📋 尝试重新绑定 init 方法...');
                if (window.LevelManager.prototype.init) {
                    window.levelManager.init = window.LevelManager.prototype.init.bind(window.levelManager);
                    console.log('✅ 方法重新绑定完成');
                    console.log(`  - 重新绑定后的类型: ${typeof window.levelManager.init}`);
                    
                    // 测试重新绑定的方法
                    try {
                        const result = window.levelManager.init();
                        console.log('✅ 重新绑定的方法调用成功，返回值:', result);
                    } catch (error) {
                        console.error('❌ 重新绑定的方法调用失败:', error);
                    }
                }
            }
            
            console.log('🔍 调试完成');
        }

        // 页面加载完成后自动调试
        window.addEventListener('load', () => {
            setTimeout(() => {
                debugLevelManager();
            }, 100);
        });
    </script>
</body>
</html>
