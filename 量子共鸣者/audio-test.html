<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量子共鸣者 - 音频系统测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #0a0f23 0%, #1a1f3a 100%);
            color: #ffffff;
            font-family: 'Arial', sans-serif;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #00ffff;
            text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
            margin-bottom: 30px;
        }

        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(0, 255, 255, 0.3);
        }

        .test-section h2 {
            color: #ff00ff;
            margin-top: 0;
        }

        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }

        button {
            background: linear-gradient(45deg, #00ffff, #ff00ff);
            border: none;
            border-radius: 5px;
            color: white;
            padding: 10px 20px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 255, 0.4);
        }

        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .slider-container {
            margin: 10px 0;
        }

        .slider-container label {
            display: block;
            margin-bottom: 5px;
            color: #ffff00;
        }

        input[type="range"] {
            width: 100%;
            margin-bottom: 10px;
        }

        .value-display {
            color: #00ffff;
            font-weight: bold;
        }

        .visualizer-container {
            width: 100%;
            height: 300px;
            border: 2px solid rgba(0, 255, 255, 0.5);
            border-radius: 10px;
            overflow: hidden;
            background: rgba(0, 0, 0, 0.3);
        }

        #visualizerCanvas {
            width: 100%;
            height: 100%;
            display: block;
        }

        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .status.success {
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid #00ff00;
        }

        .status.error {
            background: rgba(255, 0, 0, 0.2);
            border: 1px solid #ff0000;
        }

        .status.info {
            background: rgba(0, 255, 255, 0.2);
            border: 1px solid #00ffff;
        }

        .theme-selector {
            display: flex;
            gap: 10px;
            margin: 10px 0;
        }

        .theme-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid transparent;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-btn.active {
            border-color: #00ffff;
            background: rgba(0, 255, 255, 0.2);
        }

        .frequency-display {
            font-size: 1.2em;
            color: #ffff00;
            text-align: center;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌌 量子共鸣者 - 音频系统测试</h1>

        <!-- 初始化状态 -->
        <div class="test-section">
            <h2>🎵 音频系统初始化</h2>
            <div id="initStatus" class="status info">等待初始化...</div>
            <button id="initAudioBtn">初始化音频系统</button>
        </div>

        <!-- 音频引擎测试 -->
        <div class="test-section">
            <h2>🎛️ 音频引擎测试</h2>
            <div class="controls">
                <button id="playToneBtn" disabled>播放测试音调</button>
                <button id="playResonanceBtn" disabled>播放量子共鸣</button>
                <button id="playChainBtn" disabled>播放连锁反应</button>
                <button id="stopAllBtn" disabled>停止所有音频</button>
            </div>
            
            <div class="slider-container">
                <label for="frequencySlider">频率控制:</label>
                <input type="range" id="frequencySlider" min="100" max="2000" value="440" disabled>
                <div class="value-display">频率: <span id="frequencyValue">440</span> Hz</div>
            </div>
            
            <div class="slider-container">
                <label for="volumeSlider">音量控制:</label>
                <input type="range" id="volumeSlider" min="0" max="100" value="50" disabled>
                <div class="value-display">音量: <span id="volumeValue">50</span>%</div>
            </div>
        </div>

        <!-- 合成器测试 -->
        <div class="test-section">
            <h2>🎹 音频合成器测试</h2>
            <div class="controls">
                <button id="synthNoteBtn" disabled>播放合成器音符</button>
                <button id="synthChordBtn" disabled>播放和弦</button>
                <button id="synthAmbientBtn" disabled>播放环境音</button>
                <button id="synthStopBtn" disabled>停止合成器</button>
            </div>
            
            <div class="slider-container">
                <label for="attackSlider">起音时间:</label>
                <input type="range" id="attackSlider" min="0.01" max="1" value="0.1" step="0.01" disabled>
                <div class="value-display">起音: <span id="attackValue">0.1</span>s</div>
            </div>
            
            <div class="slider-container">
                <label for="releaseSlider">释音时间:</label>
                <input type="range" id="releaseSlider" min="0.1" max="3" value="0.5" step="0.1" disabled>
                <div class="value-display">释音: <span id="releaseValue">0.5</span>s</div>
            </div>
        </div>

        <!-- 序列器测试 -->
        <div class="test-section">
            <h2>🎼 音频序列器测试</h2>
            <div class="controls">
                <button id="sequencerStartBtn" disabled>开始序列</button>
                <button id="sequencerStopBtn" disabled>停止序列</button>
                <button id="sequencerRandomBtn" disabled>随机序列</button>
            </div>
            
            <div class="slider-container">
                <label for="bpmSlider">BPM (节拍):</label>
                <input type="range" id="bpmSlider" min="60" max="180" value="120" disabled>
                <div class="value-display">BPM: <span id="bpmValue">120</span></div>
            </div>
        </div>

        <!-- 效果器测试 -->
        <div class="test-section">
            <h2>🎛️ 音频效果器测试</h2>
            <div class="controls">
                <button id="reverbBtn" disabled>混响效果</button>
                <button id="delayBtn" disabled>延迟效果</button>
                <button id="chorusBtn" disabled>合唱效果</button>
                <button id="distortionBtn" disabled>失真效果</button>
                <button id="clearEffectsBtn" disabled>清除效果</button>
            </div>
        </div>

        <!-- 主题测试 -->
        <div class="test-section">
            <h2>🎨 音频主题测试</h2>
            <div class="theme-selector">
                <div class="theme-btn active" data-theme="quantum">量子主题</div>
                <div class="theme-btn" data-theme="space">太空主题</div>
                <div class="theme-btn" data-theme="energy">能量主题</div>
            </div>
        </div>

        <!-- 可视化器 -->
        <div class="test-section">
            <h2>📊 音频可视化器</h2>
            <div class="controls">
                <button id="visualizerToggleBtn" disabled>启动/停止可视化</button>
                <button id="micToggleBtn" disabled>启用/禁用麦克风</button>
            </div>
            <div class="frequency-display">
                主要频率: <span id="dominantFreq">0</span> Hz
            </div>
            <div class="visualizer-container">
                <canvas id="visualizerCanvas"></canvas>
            </div>
        </div>
    </div>

    <!-- 引入所有必要的脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/utils/math-utils.js"></script>
    <script src="js/core/audio-engine.js"></script>
    <script src="js/audio/synthesizer.js"></script>
    <script src="js/audio/effects.js"></script>
    <script src="js/audio/visualizer.js"></script>
    <script src="js/audio/audio-manager.js"></script>

    <script>
        // 音频测试脚本
        let audioInitialized = false;
        let currentSynthVoices = [];
        let currentEffects = [];
        let visualizerActive = false;

        // 初始化音频系统
        document.getElementById('initAudioBtn').addEventListener('click', async () => {
            const statusDiv = document.getElementById('initStatus');
            const initBtn = document.getElementById('initAudioBtn');
            
            try {
                statusDiv.textContent = '正在初始化音频系统...';
                statusDiv.className = 'status info';
                initBtn.disabled = true;
                
                // 初始化音频管理器
                const success = await audioManager.init();
                
                if (success) {
                    statusDiv.textContent = '✅ 音频系统初始化成功！';
                    statusDiv.className = 'status success';
                    audioInitialized = true;
                    enableAllControls();
                    
                    // 设置可视化器
                    const canvas = document.getElementById('visualizerCanvas');
                    audioManager.setupVisualizer(canvas);
                } else {
                    throw new Error('音频系统初始化失败');
                }
            } catch (error) {
                statusDiv.textContent = `❌ 初始化失败: ${error.message}`;
                statusDiv.className = 'status error';
                initBtn.disabled = false;
            }
        });

        // 启用所有控件
        function enableAllControls() {
            const buttons = document.querySelectorAll('button:not(#initAudioBtn)');
            const sliders = document.querySelectorAll('input[type="range"]');
            
            buttons.forEach(btn => btn.disabled = false);
            sliders.forEach(slider => slider.disabled = false);
        }

        // 音频引擎测试
        document.getElementById('playToneBtn').addEventListener('click', () => {
            const frequency = parseInt(document.getElementById('frequencySlider').value);
            audioManager.synthesizer.playNote(frequency, 0.5);
        });

        document.getElementById('playResonanceBtn').addEventListener('click', () => {
            const frequency = parseInt(document.getElementById('frequencySlider').value);
            audioManager.playQuantumResonance(frequency, 0.8, 2.0);
        });

        document.getElementById('playChainBtn').addEventListener('click', () => {
            const baseFreq = parseInt(document.getElementById('frequencySlider').value);
            const frequencies = [baseFreq, baseFreq * 1.5, baseFreq * 2, baseFreq * 2.5];
            audioManager.playChainReaction(frequencies, 0.2);
        });

        document.getElementById('stopAllBtn').addEventListener('click', () => {
            if (audioManager.synthesizer) {
                audioManager.synthesizer.stopAllNotes();
            }
        });

        // 滑块事件
        document.getElementById('frequencySlider').addEventListener('input', (e) => {
            document.getElementById('frequencyValue').textContent = e.target.value;
        });

        document.getElementById('volumeSlider').addEventListener('input', (e) => {
            const volume = e.target.value / 100;
            document.getElementById('volumeValue').textContent = e.target.value;
            audioManager.setMasterVolume(volume);
        });

        // 可视化器控制
        document.getElementById('visualizerToggleBtn').addEventListener('click', () => {
            if (visualizerActive) {
                audioManager.visualizer.stop();
                visualizerActive = false;
                document.getElementById('visualizerToggleBtn').textContent = '启动可视化';
            } else {
                audioManager.visualizer.start();
                visualizerActive = true;
                document.getElementById('visualizerToggleBtn').textContent = '停止可视化';
                
                // 开始更新频率显示
                updateFrequencyDisplay();
            }
        });

        // 更新频率显示
        function updateFrequencyDisplay() {
            if (visualizerActive && audioManager.visualizer) {
                const freq = Math.round(audioManager.getCurrentFrequency());
                document.getElementById('dominantFreq').textContent = freq;
                
                setTimeout(updateFrequencyDisplay, 100);
            }
        }

        // 主题切换
        document.querySelectorAll('.theme-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.theme-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                const theme = btn.dataset.theme;
                if (audioInitialized) {
                    audioManager.setTheme(theme);
                }
            });
        });

        console.log('🎵 音频测试页面已加载');
    </script>
</body>
</html>
