<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空指针错误调试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff88;
            font-size: 14px;
        }
        
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background: #222;
        }
        
        .debug-title {
            color: #ffaa00;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .status {
            padding: 5px 10px;
            margin: 5px 0;
            border-radius: 3px;
        }
        
        .success { background: #2d5a2d; color: #90ee90; }
        .error { background: #5a2d2d; color: #ff6b6b; }
        .warning { background: #5a5a2d; color: #ffeb3b; }
        .info { background: #2d2d5a; color: #87ceeb; }
        
        .code-block {
            background: #111;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            overflow-x: auto;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
        }
        
        button {
            background: #333;
            color: #00ff88;
            border: 1px solid #555;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #444;
        }
        
        #log-output {
            height: 300px;
            overflow-y: auto;
            background: #111;
            padding: 10px;
            border: 1px solid #333;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 量子共鸣者空指针错误调试</h1>
        
        <div class="debug-section">
            <div class="debug-title">1. 全局对象检查</div>
            <div id="global-objects-status">检查中...</div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">2. 游戏控制器状态</div>
            <div id="game-controller-status">检查中...</div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">3. 引擎状态检查</div>
            <div id="engines-status">检查中...</div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">4. 关卡对象检查</div>
            <div id="level-status">检查中...</div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">5. 模拟游戏循环测试</div>
            <button onclick="testGameLoop()">开始游戏循环测试</button>
            <button onclick="testLevelUpdate()">测试关卡更新</button>
            <button onclick="stopTest()">停止测试</button>
            <div id="game-loop-status">未开始</div>
        </div>
        
        <div class="debug-section">
            <div class="debug-title">6. 实时日志</div>
            <div id="log-output"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <!-- 加载所有必要的脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/utils/math-utils.js"></script>
    <script src="js/core/audio-engine.js"></script>
    <script src="js/core/physics-engine.js"></script>
    <script src="js/core/quantum-engine.js"></script>
    <script src="js/audio/synthesizer.js"></script>
    <script src="js/audio/sequencer.js"></script>
    <script src="js/audio/effects.js"></script>
    <script src="js/audio/visualizer.js"></script>
    <script src="js/audio/audio-manager.js"></script>
    <script src="js/render/webgl-renderer.js"></script>
    <script src="js/render/particle-system-3d.js"></script>
    <script src="js/render/scene-manager-3d.js"></script>
    <script src="js/core/render-engine.js"></script>
    <script src="js/render/renderer.js"></script>
    <script src="js/render/particle-system.js"></script>
    <script src="js/ui/notification-system.js"></script>
    <script src="js/ui/ui-manager.js"></script>
    <script src="js/ui/ui-animations.js"></script>
    <script src="js/ui/game-hud.js"></script>
    <script src="js/ui/settings-panel.js"></script>
    <script src="js/ui/level-select.js"></script>
    <script src="js/ui/level-editor.js"></script>
    <script src="js/ui/game-over.js"></script>
    <script src="js/ui/achievements.js"></script>
    <script src="js/ui/leaderboard.js"></script>
    <script src="js/game/player-manager.js"></script>
    <script src="js/game/game-state.js"></script>
    <script src="js/game/level-manager.js"></script>
    <script src="js/game/level.js"></script>
    <script src="js/game/input-manager.js"></script>
    <script src="js/game/game-controller.js"></script>
    <script src="js/ui/screen-manager.js"></script>
    <script src="js/ui/input-handler.js"></script>
    <script src="js/ui/ui-components.js"></script>
    <script src="js/app.js"></script>

    <script>
        let testInterval = null;
        let logCount = 0;
        
        function log(message, type = 'info') {
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logCount++;
            
            const logEntry = document.createElement('div');
            logEntry.className = `status ${className}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;
            
            // 限制日志条数
            if (logCount > 100) {
                logOutput.removeChild(logOutput.firstChild);
                logCount--;
            }
        }
        
        function clearLog() {
            document.getElementById('log-output').innerHTML = '';
            logCount = 0;
        }
        
        function checkGlobalObjects() {
            const objects = [
                'audioEngine', 'physicsEngine', 'quantumEngine', 'renderEngine',
                'InputManager', 'GameController', 'Level', 'gameController'
            ];
            
            let html = '';
            objects.forEach(obj => {
                const exists = typeof window[obj] !== 'undefined';
                const type = typeof window[obj];
                const className = exists ? 'success' : 'error';
                html += `<div class="status ${className}">${obj}: ${exists ? '✅' : '❌'} (${type})</div>`;
            });
            
            document.getElementById('global-objects-status').innerHTML = html;
        }
        
        function checkGameController() {
            let html = '';
            
            if (typeof window.gameController !== 'undefined') {
                const gc = window.gameController;
                html += `<div class="status success">gameController: ✅ 存在</div>`;
                html += `<div class="status info">isInitialized: ${gc.isInitialized}</div>`;
                html += `<div class="status info">gameState: ${gc.gameState}</div>`;
                html += `<div class="status info">isRunning: ${gc.isRunning}</div>`;
                html += `<div class="status info">inputManager: ${gc.inputManager ? '✅' : '❌'}</div>`;
                html += `<div class="status info">currentLevel: ${gc.currentLevel ? '✅' : '❌'}</div>`;
            } else {
                html += `<div class="status error">gameController: ❌ 不存在</div>`;
            }
            
            document.getElementById('game-controller-status').innerHTML = html;
        }
        
        function checkEngines() {
            const engines = ['audioEngine', 'physicsEngine', 'quantumEngine', 'renderEngine'];
            let html = '';
            
            engines.forEach(engineName => {
                if (typeof window[engineName] !== 'undefined') {
                    const engine = window[engineName];
                    html += `<div class="status success">${engineName}: ✅ 存在</div>`;
                    
                    // 检查常用方法
                    const methods = ['update', 'init', 'reset', 'clear'];
                    methods.forEach(method => {
                        if (typeof engine[method] === 'function') {
                            html += `<div class="status info">  ${method}(): ✅</div>`;
                        } else {
                            html += `<div class="status warning">  ${method}(): ❌</div>`;
                        }
                    });
                } else {
                    html += `<div class="status error">${engineName}: ❌ 不存在</div>`;
                }
            });
            
            document.getElementById('engines-status').innerHTML = html;
        }
        
        function checkLevel() {
            let html = '';
            
            if (typeof window.Level !== 'undefined') {
                html += `<div class="status success">Level 类: ✅ 存在</div>`;
                
                if (window.gameController && window.gameController.currentLevel) {
                    const level = window.gameController.currentLevel;
                    html += `<div class="status success">currentLevel: ✅ 存在</div>`;
                    html += `<div class="status info">类型: ${typeof level}</div>`;
                    html += `<div class="status info">构造函数: ${level.constructor.name}</div>`;
                    
                    if (typeof level.update === 'function') {
                        html += `<div class="status success">update 方法: ✅ 存在</div>`;
                    } else {
                        html += `<div class="status error">update 方法: ❌ 不存在</div>`;
                    }
                } else {
                    html += `<div class="status warning">currentLevel: ❌ 不存在</div>`;
                }
            } else {
                html += `<div class="status error">Level 类: ❌ 不存在</div>`;
            }
            
            document.getElementById('level-status').innerHTML = html;
        }
        
        function testGameLoop() {
            if (testInterval) {
                clearInterval(testInterval);
            }
            
            log('开始游戏循环测试...', 'info');
            
            let frameCount = 0;
            testInterval = setInterval(() => {
                try {
                    frameCount++;
                    
                    if (window.gameController) {
                        // 模拟 update 调用
                        if (typeof window.gameController.update === 'function') {
                            window.gameController.update(0.016); // 60fps
                            
                            if (frameCount % 60 === 0) { // 每秒记录一次
                                log(`游戏循环正常运行 - 帧数: ${frameCount}`, 'success');
                            }
                        } else {
                            log('gameController.update 方法不存在', 'error');
                            clearInterval(testInterval);
                        }
                    } else {
                        log('gameController 不存在', 'error');
                        clearInterval(testInterval);
                    }
                    
                    if (frameCount >= 300) { // 5秒后停止
                        log('游戏循环测试完成', 'success');
                        clearInterval(testInterval);
                        testInterval = null;
                    }
                    
                } catch (error) {
                    log(`游戏循环错误: ${error.message}`, 'error');
                    log(`错误堆栈: ${error.stack}`, 'error');
                    clearInterval(testInterval);
                    testInterval = null;
                }
            }, 16); // 60fps
            
            document.getElementById('game-loop-status').innerHTML = '<div class="status info">测试运行中...</div>';
        }
        
        function testLevelUpdate() {
            try {
                if (window.gameController && window.gameController.currentLevel) {
                    const level = window.gameController.currentLevel;
                    log('测试关卡更新...', 'info');
                    
                    if (typeof level.update === 'function') {
                        level.update(0.016);
                        log('关卡更新成功', 'success');
                    } else {
                        log('关卡没有 update 方法', 'error');
                    }
                } else {
                    log('没有当前关卡可测试', 'warning');
                }
            } catch (error) {
                log(`关卡更新错误: ${error.message}`, 'error');
                log(`错误堆栈: ${error.stack}`, 'error');
            }
        }
        
        function stopTest() {
            if (testInterval) {
                clearInterval(testInterval);
                testInterval = null;
                log('测试已停止', 'info');
                document.getElementById('game-loop-status').innerHTML = '<div class="status warning">测试已停止</div>';
            }
        }
        
        // 页面加载完成后开始检查
        document.addEventListener('DOMContentLoaded', () => {
            log('开始调试检查...', 'info');
            
            setTimeout(() => {
                checkGlobalObjects();
                checkGameController();
                checkEngines();
                checkLevel();
                log('初始检查完成', 'success');
            }, 1000);
        });
        
        // 捕获全局错误
        window.addEventListener('error', (event) => {
            log(`全局错误: ${event.error.message}`, 'error');
            log(`文件: ${event.filename}:${event.lineno}:${event.colno}`, 'error');
            log(`堆栈: ${event.error.stack}`, 'error');
        });
        
        // 捕获未处理的 Promise 拒绝
        window.addEventListener('unhandledrejection', (event) => {
            log(`未处理的 Promise 拒绝: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
