<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单 LevelManager 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: #fff;
        }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>简单 LevelManager 测试</h1>
    <div id="console-output"></div>

    <!-- 直接加载 LevelManager -->
    <script src="js/game/level-manager.js"></script>

    <script>
        // 重写 console 方法
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📋';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        // 页面加载完成后测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🧪 开始测试 LevelManager...');
                
                // 检查 LevelManager 类
                console.log('📋 检查 LevelManager 类...');
                if (typeof window.LevelManager === 'function') {
                    console.log('✅ LevelManager 类存在');
                } else {
                    console.error('❌ LevelManager 类不存在');
                    console.log('📋 window 对象中的所有属性:');
                    for (let key in window) {
                        if (key.includes('Level') || key.includes('Manager')) {
                            console.log(`  - ${key}: ${typeof window[key]}`);
                        }
                    }
                    return;
                }
                
                // 检查全局实例
                console.log('📋 检查 levelManager 实例...');
                if (window.levelManager) {
                    console.log('✅ levelManager 全局实例存在');
                    console.log('📋 levelManager 类型:', typeof window.levelManager);
                    console.log('📋 levelManager 构造函数:', window.levelManager.constructor.name);
                    
                    // 列出所有方法
                    console.log('📋 levelManager 的所有方法:');
                    const proto = Object.getPrototypeOf(window.levelManager);
                    const methods = Object.getOwnPropertyNames(proto).filter(name => 
                        typeof window.levelManager[name] === 'function' && name !== 'constructor'
                    );
                    methods.forEach(method => {
                        console.log(`  - ${method}(): ${typeof window.levelManager[method]}`);
                    });
                    
                } else {
                    console.error('❌ levelManager 全局实例不存在');
                    return;
                }
                
                // 检查 init 方法
                console.log('📋 检查 init 方法...');
                if (typeof window.levelManager.init === 'function') {
                    console.log('✅ levelManager.init 方法存在');
                    
                    // 尝试调用 init 方法
                    try {
                        console.log('🎯 调用 levelManager.init()...');
                        const result = window.levelManager.init();
                        console.log('📋 init 方法返回值:', result);
                        console.log('📋 isInitialized:', window.levelManager.isInitialized);
                        
                        if (result) {
                            console.log('✅ init 方法调用成功');
                        } else {
                            console.warn('⚠️ init 方法返回 false，但没有抛出异常');
                        }
                        
                    } catch (error) {
                        console.error('❌ init 方法调用失败:', error.message);
                        console.error('❌ 错误堆栈:', error.stack);
                    }
                    
                } else {
                    console.error('❌ levelManager.init 方法不存在');
                    console.log('📋 levelManager 的类型:', typeof window.levelManager.init);
                }
                
                console.log('🧪 测试完成');
                
            }, 200); // 延迟200ms确保脚本完全加载
        });
    </script>
</body>
</html>
