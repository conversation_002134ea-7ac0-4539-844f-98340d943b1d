/**
 * 量子共鸣者关卡编辑器功能验证脚本
 * 用于验证关卡编辑器的各项功能是否正常工作
 */

class LevelEditorVerification {
    constructor() {
        this.testResults = [];
        this.isRunning = false;
    }

    /**
     * 运行所有验证测试
     */
    async runAllTests() {
        if (this.isRunning) {
            console.log('⚠️ 测试已在运行中');
            return;
        }

        this.isRunning = true;
        this.testResults = [];
        
        console.log('🔍 开始验证关卡编辑器功能...');
        
        try {
            // 基础功能测试
            await this.testBasicFunctionality();
            
            // 界面元素测试
            await this.testUIElements();
            
            // 编辑功能测试
            await this.testEditingFeatures();
            
            // 保存加载测试
            await this.testSaveLoadFeatures();
            
            // 显示测试结果
            this.displayResults();
            
        } catch (error) {
            console.error('❌ 测试过程中发生错误:', error);
            this.addResult('测试执行', false, `错误: ${error.message}`);
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * 测试基础功能
     */
    async testBasicFunctionality() {
        console.log('📋 测试基础功能...');
        
        // 检查关卡编辑器类是否存在
        const editorClassExists = typeof LevelEditor !== 'undefined';
        this.addResult('LevelEditor类存在', editorClassExists);
        
        // 检查全局实例是否存在
        const globalInstanceExists = window.levelEditor instanceof LevelEditor;
        this.addResult('全局levelEditor实例存在', globalInstanceExists);
        
        if (!globalInstanceExists) return;
        
        // 检查初始化状态
        const canInitialize = typeof window.levelEditor.init === 'function';
        this.addResult('编辑器可以初始化', canInitialize);
        
        // 检查显示隐藏功能
        const hasShowHide = typeof window.levelEditor.show === 'function' && 
                           typeof window.levelEditor.hide === 'function';
        this.addResult('编辑器有显示/隐藏功能', hasShowHide);
    }

    /**
     * 测试界面元素
     */
    async testUIElements() {
        console.log('🎨 测试界面元素...');
        
        // 检查编辑器容器
        const editorContainer = document.getElementById('levelEditorScreen');
        this.addResult('编辑器容器存在', !!editorContainer);
        
        // 检查主菜单按钮
        const editorButton = document.getElementById('level-editor-btn');
        this.addResult('关卡编辑器按钮存在', !!editorButton);
        
        if (editorButton) {
            // 检查按钮事件
            const hasClickEvent = editorButton.onclick !== null || 
                                 editorButton.addEventListener !== undefined;
            this.addResult('编辑器按钮有点击事件', hasClickEvent);
        }
        
        // 尝试初始化编辑器界面
        if (window.levelEditor && !window.levelEditor.isInitialized) {
            try {
                const initResult = window.levelEditor.init();
                this.addResult('编辑器界面初始化', initResult);
                
                // 检查初始化后的元素
                if (initResult) {
                    const hasCanvas = !!document.getElementById('editorCanvas');
                    this.addResult('编辑器画布创建', hasCanvas);
                    
                    const hasToolbar = !!document.getElementById('editorToolbar');
                    this.addResult('编辑器工具栏创建', hasToolbar);
                    
                    const hasProperties = !!document.getElementById('editorProperties');
                    this.addResult('编辑器属性面板创建', hasProperties);
                }
            } catch (error) {
                this.addResult('编辑器界面初始化', false, error.message);
            }
        }
    }

    /**
     * 测试编辑功能
     */
    async testEditingFeatures() {
        console.log('✏️ 测试编辑功能...');
        
        if (!window.levelEditor || !window.levelEditor.isInitialized) {
            this.addResult('编辑功能测试', false, '编辑器未初始化');
            return;
        }
        
        const editor = window.levelEditor;
        
        // 测试新建关卡
        try {
            editor.newLevel();
            const hasNewLevel = editor.currentLevel && editor.currentLevel.name;
            this.addResult('新建关卡功能', hasNewLevel);
        } catch (error) {
            this.addResult('新建关卡功能', false, error.message);
        }
        
        // 测试编辑模式切换
        const originalMode = editor.editMode;
        try {
            editor.setEditMode('connection');
            const modeChanged = editor.editMode === 'connection';
            editor.setEditMode(originalMode); // 恢复原模式
            this.addResult('编辑模式切换', modeChanged);
        } catch (error) {
            this.addResult('编辑模式切换', false, error.message);
        }
        
        // 测试粒子添加（模拟）
        try {
            const initialParticleCount = editor.currentLevel.difficulty.normal.particles.length;
            // 模拟添加粒子
            editor.currentLevel.difficulty.normal.particles.push({
                x: 0.5, y: 0.5, frequency: 440, energy: 50
            });
            const particleAdded = editor.currentLevel.difficulty.normal.particles.length > initialParticleCount;
            this.addResult('粒子添加功能', particleAdded);
        } catch (error) {
            this.addResult('粒子添加功能', false, error.message);
        }
    }

    /**
     * 测试保存加载功能
     */
    async testSaveLoadFeatures() {
        console.log('💾 测试保存加载功能...');
        
        if (!window.levelEditor || !window.levelEditor.isInitialized) {
            this.addResult('保存加载测试', false, '编辑器未初始化');
            return;
        }
        
        const editor = window.levelEditor;
        
        // 测试保存功能存在
        const hasSaveFunction = typeof editor.saveLevel === 'function';
        this.addResult('保存功能存在', hasSaveFunction);
        
        // 测试加载功能存在
        const hasLoadFunction = typeof editor.loadLevel === 'function';
        this.addResult('加载功能存在', hasLoadFunction);
        
        // 测试关卡ID生成
        try {
            const testId = editor.generateLevelId('测试关卡');
            const hasValidId = testId && typeof testId === 'string' && testId.length > 0;
            this.addResult('关卡ID生成', hasValidId);
        } catch (error) {
            this.addResult('关卡ID生成', false, error.message);
        }
    }

    /**
     * 添加测试结果
     */
    addResult(testName, passed, details = '') {
        this.testResults.push({
            name: testName,
            passed: passed,
            details: details
        });
        
        const status = passed ? '✅' : '❌';
        const message = details ? ` (${details})` : '';
        console.log(`${status} ${testName}${message}`);
    }

    /**
     * 显示测试结果摘要
     */
    displayResults() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;
        
        console.log('\n📊 关卡编辑器功能验证结果:');
        console.log(`总测试数: ${totalTests}`);
        console.log(`通过: ${passedTests} ✅`);
        console.log(`失败: ${failedTests} ❌`);
        console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
        
        if (failedTests > 0) {
            console.log('\n❌ 失败的测试:');
            this.testResults.filter(r => !r.passed).forEach(result => {
                console.log(`  - ${result.name}: ${result.details || '未知错误'}`);
            });
        }
        
        // 总体评估
        if (passedTests === totalTests) {
            console.log('\n🎉 关卡编辑器功能完整，可以正常使用！');
        } else if (passedTests / totalTests >= 0.8) {
            console.log('\n⚠️ 关卡编辑器基本功能正常，但有部分问题需要修复');
        } else {
            console.log('\n🚨 关卡编辑器存在重大问题，需要进一步开发');
        }
    }

    /**
     * 快速测试关卡编辑器是否可用
     */
    quickTest() {
        console.log('🚀 快速测试关卡编辑器...');
        
        const hasEditor = window.levelEditor instanceof LevelEditor;
        const hasButton = !!document.getElementById('level-editor-btn');
        const hasContainer = !!document.getElementById('levelEditorScreen');
        
        if (hasEditor && hasButton && hasContainer) {
            console.log('✅ 关卡编辑器基本组件齐全');
            
            // 尝试显示编辑器
            try {
                window.levelEditor.show();
                console.log('✅ 关卡编辑器可以正常显示');
                
                setTimeout(() => {
                    window.levelEditor.hide();
                    console.log('✅ 关卡编辑器可以正常隐藏');
                }, 1000);
                
                return true;
            } catch (error) {
                console.log('❌ 关卡编辑器显示失败:', error.message);
                return false;
            }
        } else {
            console.log('❌ 关卡编辑器基本组件缺失');
            console.log(`  编辑器实例: ${hasEditor ? '✅' : '❌'}`);
            console.log(`  编辑器按钮: ${hasButton ? '✅' : '❌'}`);
            console.log(`  编辑器容器: ${hasContainer ? '✅' : '❌'}`);
            return false;
        }
    }
}

// 创建验证器实例
window.levelEditorVerification = new LevelEditorVerification();

// 提供便捷的测试函数
window.testLevelEditor = () => window.levelEditorVerification.runAllTests();
window.quickTestLevelEditor = () => window.levelEditorVerification.quickTest();

console.log('🔧 关卡编辑器验证脚本已加载');
console.log('使用 testLevelEditor() 运行完整测试');
console.log('使用 quickTestLevelEditor() 运行快速测试');
