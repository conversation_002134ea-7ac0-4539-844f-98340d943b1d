<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量子共鸣者 - 快速修复验证</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #ffffff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            color: #00d4ff;
            margin-bottom: 30px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }
        
        .status {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #00d4ff;
        }
        
        .test-button {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
        }
        
        .console-output {
            background: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 20px;
            border-radius: 10px;
            height: 400px;
            overflow-y: auto;
            margin-top: 20px;
            border: 1px solid #333;
        }
        
        .loading {
            text-align: center;
            color: #00d4ff;
            font-size: 18px;
            margin: 20px 0;
        }
        
        .success {
            color: #00ff00;
        }
        
        .error {
            color: #ff4444;
        }
        
        .warning {
            color: #ffaa00;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 量子共鸣者 - 快速修复验证</h1>
        
        <div class="status">
            <h3>📋 测试状态</h3>
            <p id="status-text">正在加载游戏系统...</p>
            <div class="loading" id="loading-indicator">⏳ 请稍候...</div>
        </div>
        
        <div style="text-align: center;">
            <button class="test-button" onclick="runQuickTest()">🚀 运行快速测试</button>
            <button class="test-button" onclick="clearConsole()">🧹 清空控制台</button>
        </div>
        
        <div class="console-output" id="console-output">
            <div class="success">🎮 量子共鸣者修复验证控制台</div>
            <div>等待测试开始...</div>
        </div>
    </div>

    <!-- 加载所有必要的脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/utils/math-utils.js"></script>
    <script src="js/core/audio-engine.js"></script>
    <script src="js/core/physics-engine.js"></script>
    <script src="js/core/quantum-engine.js"></script>
    <script src="js/audio/synthesizer.js"></script>
    <script src="js/audio/sequencer.js"></script>
    <script src="js/audio/effects.js"></script>
    <script src="js/audio/visualizer.js"></script>
    <script src="js/audio/audio-manager.js"></script>
    <script src="js/render/webgl-renderer.js"></script>
    <script src="js/render/particle-system-3d.js"></script>
    <script src="js/render/scene-manager-3d.js"></script>
    <script src="js/core/render-engine.js"></script>
    <script src="js/render/renderer.js"></script>
    <script src="js/render/particle-system.js"></script>
    <script src="js/ui/notification-system.js"></script>
    <script src="js/ui/ui-manager.js"></script>
    <script src="js/ui/ui-animations.js"></script>
    <script src="js/ui/game-hud.js"></script>
    <script src="js/ui/settings-panel.js"></script>
    <script src="js/ui/level-select.js"></script>
    <script src="js/ui/level-editor.js"></script>
    <script src="js/ui/game-over.js"></script>
    <script src="js/ui/achievements.js"></script>
    <script src="js/ui/leaderboard.js"></script>
    <script src="js/game/player-manager.js"></script>
    <script src="js/game/game-state.js"></script>
    <script src="js/game/level-manager.js"></script>
    <script src="js/game/input-manager.js"></script>
    <script src="js/game/game-controller.js"></script>
    <script src="js/ui/screen-manager.js"></script>
    <script src="js/ui/input-handler.js"></script>
    <script src="js/ui/ui-components.js"></script>
    <script src="js/app.js"></script>
    
    <!-- 修复验证测试脚本 -->
    <script src="修复验证测试.js"></script>

    <script>
        let consoleOutput = document.getElementById('console-output');
        let statusText = document.getElementById('status-text');
        let loadingIndicator = document.getElementById('loading-indicator');
        
        // 重定向console.log到页面
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const div = document.createElement('div');
            div.className = type;
            div.textContent = message;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'success');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warning');
        };
        
        // 启动应用程序
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                statusText.textContent = '正在初始化应用程序...';
                
                // 创建应用程序实例
                window.quantumApp = new QuantumResonanceApp();
                
                // 启动应用程序
                await quantumApp.init();
                
                statusText.textContent = '✅ 应用程序初始化完成';
                loadingIndicator.style.display = 'none';
                
                console.log('✅ 量子共鸣者应用程序启动完成');
                
            } catch (error) {
                console.error('❌ 应用程序启动失败:', error);
                statusText.textContent = '❌ 应用程序启动失败';
                loadingIndicator.style.display = 'none';
            }
        });
        
        function runQuickTest() {
            if (window.fixVerificationTest) {
                console.log('🧪 手动运行修复验证测试...');
                fixVerificationTest.runAllTests();
            } else {
                console.error('❌ 修复验证测试未加载');
            }
        }
        
        function clearConsole() {
            consoleOutput.innerHTML = '<div class="success">🎮 量子共鸣者修复验证控制台</div><div>控制台已清空...</div>';
        }
    </script>
</body>
</html>
