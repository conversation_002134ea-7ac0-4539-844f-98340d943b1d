<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量子共鸣者 - 游戏控制器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: rgba(255,255,255,0.1);
        }
        .pass { border-left: 4px solid #00ff00; }
        .fail { border-left: 4px solid #ff0000; }
        button {
            background: #00d4ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .log-output {
            background: #000;
            color: #00ff00;
            font-family: monospace;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🎮 量子共鸣者 - 游戏控制器专项测试</h1>
    <button onclick="testGameController()">测试游戏控制器</button>
    <button onclick="manualInit()">手动初始化</button>
    <button onclick="clearResults()">清空结果</button>
    <div id="results"></div>
    <div id="logs" class="log-output"></div>

    <!-- 必要的DOM元素 -->
    <div id="loadingScreen" style="display: none;">
        <div id="loadingProgress" style="width: 0%; height: 4px; background: #00d4ff;"></div>
        <div id="loadingText">0%</div>
    </div>
    
    <div id="mainMenu" style="display: none;">
        <button id="startGameButton">开始游戏</button>
        <button id="levelSelectButton">选择关卡</button>
        <button id="settingsButton">设置</button>
        <button id="aboutButton">关于</button>
    </div>
    
    <div id="settingsScreen" style="display: none;"></div>
    <div id="aboutScreen" style="display: none;"></div>
    <div id="levelSelectScreen" style="display: none;"></div>
    <canvas id="game-canvas" style="display: none;" width="800" height="600"></canvas>
    <button id="initAudioButton" style="display: none;">初始化音频</button>

    <!-- 只加载必要的脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/game/game-controller.js"></script>

    <script>
        let logs = [];

        // 捕获控制台日志
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            logs.push(`[LOG] ${args.join(' ')}`);
            originalLog.apply(console, args);
            updateLogs();
        };

        console.error = function(...args) {
            logs.push(`[ERROR] ${args.join(' ')}`);
            originalError.apply(console, args);
            updateLogs();
        };

        console.warn = function(...args) {
            logs.push(`[WARN] ${args.join(' ')}`);
            originalWarn.apply(console, args);
            updateLogs();
        };

        function updateLogs() {
            const logsDiv = document.getElementById('logs');
            logsDiv.innerHTML = logs.slice(-30).join('\n'); // 显示最近30条日志
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        function addResult(name, passed, message) {
            const div = document.createElement('div');
            div.className = `test-result ${passed ? 'pass' : 'fail'}`;
            div.innerHTML = `<strong>${name}:</strong> ${message}`;
            document.getElementById('results').appendChild(div);
        }

        function testGameController() {
            document.getElementById('results').innerHTML = '';
            logs = [];
            
            console.log('🔍 开始测试游戏控制器...');
            
            // 测试1: 游戏控制器存在
            const gameControllerExists = typeof window.gameController !== 'undefined';
            addResult('游戏控制器存在', gameControllerExists, 
                gameControllerExists ? '✅ window.gameController 存在' : '❌ window.gameController 不存在');
            
            if (!gameControllerExists) {
                console.error('❌ 游戏控制器不存在，无法继续测试');
                return;
            }
            
            // 测试2: 游戏控制器类型
            const controllerType = typeof window.gameController;
            const isObject = controllerType === 'object';
            addResult('游戏控制器类型', isObject, 
                isObject ? `✅ 游戏控制器是对象类型` : `❌ 游戏控制器类型错误: ${controllerType}`);
            
            // 测试3: 初始化状态
            const isInitialized = window.gameController.isInitialized === true;
            addResult('初始化状态', isInitialized, 
                isInitialized ? '✅ 游戏控制器已初始化' : `❌ 游戏控制器未初始化 (isInitialized: ${window.gameController.isInitialized})`);
            
            // 测试4: init 方法存在
            const hasInitMethod = typeof window.gameController.init === 'function';
            addResult('init 方法存在', hasInitMethod, 
                hasInitMethod ? '✅ init 方法存在' : '❌ init 方法不存在');
            
            // 测试5: 关键属性存在
            const properties = ['gameState', 'isRunning', 'settings'];
            properties.forEach(prop => {
                const hasProp = window.gameController.hasOwnProperty(prop);
                addResult(`${prop} 属性`, hasProp, 
                    hasProp ? `✅ ${prop} 属性存在: ${window.gameController[prop]}` : `❌ ${prop} 属性不存在`);
            });
            
            // 测试6: 关键方法存在
            const methods = ['startGame', 'startLevel', 'showMainMenu'];
            methods.forEach(method => {
                const hasMethod = typeof window.gameController[method] === 'function';
                addResult(`${method} 方法`, hasMethod, 
                    hasMethod ? `✅ ${method} 方法存在` : `❌ ${method} 方法不存在`);
            });
            
            // 显示详细状态
            console.log('📊 游戏控制器详细状态:');
            console.log('- gameState:', window.gameController.gameState);
            console.log('- isInitialized:', window.gameController.isInitialized);
            console.log('- isRunning:', window.gameController.isRunning);
            console.log('- settings:', window.gameController.settings);
        }

        async function manualInit() {
            console.log('🚀 开始手动初始化游戏控制器...');
            
            if (!window.gameController) {
                console.error('❌ 游戏控制器不存在');
                addResult('手动初始化', false, '❌ 游戏控制器不存在');
                return;
            }
            
            if (typeof window.gameController.init !== 'function') {
                console.error('❌ init 方法不存在');
                addResult('手动初始化', false, '❌ init 方法不存在');
                return;
            }
            
            try {
                console.log('⏳ 调用 gameController.init()...');
                await window.gameController.init();
                
                const isInitialized = window.gameController.isInitialized === true;
                addResult('手动初始化', isInitialized, 
                    isInitialized ? '✅ 手动初始化成功' : '❌ 手动初始化失败');
                
                // 重新测试状态
                setTimeout(() => {
                    testGameController();
                }, 1000);
                
            } catch (error) {
                console.error('❌ 手动初始化异常:', error);
                addResult('手动初始化', false, `❌ 初始化异常: ${error.message}`);
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            logs = [];
            updateLogs();
        }

        // 页面加载完成后等待一段时间再测试
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                console.log('🚀 页面加载完成，开始自动测试...');
                testGameController();
            }, 2000);
        });
    </script>
</body>
</html>
