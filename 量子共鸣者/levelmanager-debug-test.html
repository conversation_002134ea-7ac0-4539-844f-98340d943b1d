<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LevelManager 调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: #fff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #16213e;
            border-radius: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: #0f3460;
            border-radius: 5px;
        }
        .test-button {
            background: #e94560;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #d63447;
        }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>LevelManager 调试测试</h1>
        <p>专门测试 LevelManager 的加载和初始化</p>

        <div class="test-section">
            <h3>测试步骤</h3>
            <button class="test-button" onclick="testLevelManagerBasic()">基础检查</button>
            <button class="test-button" onclick="testLevelManagerInit()">测试 init 方法</button>
            <button class="test-button" onclick="clearConsole()">清空控制台</button>
        </div>

        <div class="test-section">
            <h3>控制台输出</h3>
            <div id="console-output"></div>
        </div>
    </div>

    <!-- 逐步加载脚本 -->
    <script>
        // 重写 console 方法
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📋';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        function clearConsole() {
            consoleOutput.textContent = '';
        }

        function testLevelManagerBasic() {
            console.log('🧪 开始基础检查...');
            
            // 检查 LevelManager 类
            if (typeof window.LevelManager === 'function') {
                console.log('✅ LevelManager 类存在');
            } else {
                console.error('❌ LevelManager 类不存在');
                return;
            }
            
            // 检查全局实例
            if (window.levelManager) {
                console.log('✅ levelManager 全局实例存在');
                console.log('📋 levelManager 类型:', typeof window.levelManager);
                console.log('📋 levelManager 构造函数:', window.levelManager.constructor.name);
            } else {
                console.error('❌ levelManager 全局实例不存在');
                return;
            }
            
            // 检查 init 方法
            if (typeof window.levelManager.init === 'function') {
                console.log('✅ levelManager.init 方法存在');
            } else {
                console.error('❌ levelManager.init 方法不存在');
                console.log('📋 levelManager 的所有属性和方法:');
                for (let key in window.levelManager) {
                    console.log(`  - ${key}: ${typeof window.levelManager[key]}`);
                }
            }
            
            console.log('🧪 基础检查完成');
        }

        function testLevelManagerInit() {
            console.log('🧪 开始测试 init 方法...');
            
            try {
                if (!window.levelManager) {
                    throw new Error('levelManager 不存在');
                }
                
                if (typeof window.levelManager.init !== 'function') {
                    throw new Error('levelManager.init 不是函数');
                }
                
                console.log('🎯 调用 levelManager.init()...');
                const result = window.levelManager.init();
                
                console.log('📋 init 方法返回值:', result);
                console.log('📋 isInitialized:', window.levelManager.isInitialized);
                
                if (result) {
                    console.log('✅ init 方法调用成功');
                } else {
                    console.warn('⚠️ init 方法返回 false');
                }
                
            } catch (error) {
                console.error('❌ 测试失败:', error.message);
                console.error('❌ 错误堆栈:', error.stack);
            }
        }

        // 动态加载脚本
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = () => {
                    console.log(`✅ 脚本加载成功: ${src}`);
                    resolve();
                };
                script.onerror = () => {
                    console.error(`❌ 脚本加载失败: ${src}`);
                    reject(new Error(`Failed to load ${src}`));
                };
                document.head.appendChild(script);
            });
        }

        // 页面加载完成后加载脚本
        window.addEventListener('load', async () => {
            console.log('🎮 页面加载完成，开始加载脚本...');
            
            try {
                // 按顺序加载脚本
                await loadScript('js/utils/math-utils.js');
                await loadScript('js/game/level-manager.js');
                
                console.log('🎮 所有脚本加载完成');
                
                // 延迟一点时间再进行检查
                setTimeout(() => {
                    testLevelManagerBasic();
                }, 100);
                
            } catch (error) {
                console.error('❌ 脚本加载失败:', error);
            }
        });
    </script>
</body>
</html>
