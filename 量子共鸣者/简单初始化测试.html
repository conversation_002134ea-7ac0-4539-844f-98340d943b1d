<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单初始化测试 - 量子共鸣者</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
        }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 简单初始化测试</h1>
        
        <button class="test-button" onclick="testGameController()">测试游戏控制器</button>
        <button class="test-button" onclick="testLevelSelect()">测试关卡选择</button>
        <button class="test-button" onclick="initializeApp()">手动初始化应用</button>
        
        <h2>🖥️ 控制台输出</h2>
        <div id="console-output"></div>
    </div>

    <!-- 只加载必要的脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/utils/math-utils.js"></script>
    <script src="js/core/audio-engine.js"></script>
    <script src="js/core/physics-engine.js"></script>
    <script src="js/core/quantum-engine.js"></script>
    <script src="js/audio/audio-manager.js"></script>
    <script src="js/core/render-engine.js"></script>
    <script src="js/ui/notification-system.js"></script>
    <script src="js/ui/ui-manager.js"></script>
    <script src="js/ui/level-select.js"></script>
    <script src="js/game/player-manager.js"></script>
    <script src="js/game/game-state.js"></script>
    <script src="js/game/level-manager.js"></script>
    <script src="js/game/input-manager.js"></script>
    <script src="js/game/game-controller.js"></script>
    <script src="js/app.js"></script>

    <script>
        // 重定向控制台输出到页面
        const consoleOutput = document.getElementById('console-output');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📝';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        function testGameController() {
            console.log('🧪 测试游戏控制器...');
            console.log('window.gameController 存在:', typeof window.gameController !== 'undefined');
            if (window.gameController) {
                console.log('gameController.isInitialized:', window.gameController.isInitialized);
                console.log('gameController.init 方法存在:', typeof window.gameController.init === 'function');
            }
        }

        function testLevelSelect() {
            console.log('🧪 测试关卡选择...');
            console.log('window.levelSelect 存在:', typeof window.levelSelect !== 'undefined');
            if (window.levelSelect) {
                console.log('levelSelect.startLevel 方法存在:', typeof window.levelSelect.startLevel === 'function');
                console.log('levelSelect.hideWithoutReturnToMenu 方法存在:', typeof window.levelSelect.hideWithoutReturnToMenu === 'function');
            }
        }

        async function initializeApp() {
            console.log('🚀 手动初始化应用程序...');
            
            try {
                if (typeof QuantumResonanceApp !== 'undefined') {
                    if (!window.quantumApp) {
                        window.quantumApp = new QuantumResonanceApp();
                    }
                    await quantumApp.init();
                    console.log('✅ 应用程序初始化完成');
                } else {
                    console.error('❌ QuantumResonanceApp 类未找到');
                }
                
                // 重新测试
                testGameController();
                testLevelSelect();
                
            } catch (error) {
                console.error('❌ 应用程序初始化失败:', error);
            }
        }

        // 页面加载完成后自动测试
        window.addEventListener('load', () => {
            console.log('📄 页面加载完成');
            testGameController();
            testLevelSelect();
        });
    </script>
</body>
</html>
