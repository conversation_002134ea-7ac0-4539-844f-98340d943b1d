<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>屏幕切换测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #0a0a0f;
            color: #ffffff;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
        }
        .test-button {
            background: #00ffff;
            color: #000;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0080ff;
        }
        .log-output {
            background: #1a1a2e;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .error { color: #ff4444; }
        .success { color: #44ff44; }
        .info { color: #4444ff; }
    </style>
</head>
<body>
    <h1>🔧 屏幕切换修复测试</h1>
    
    <div class="test-section">
        <h2>屏幕注册测试</h2>
        <button class="test-button" onclick="testScreenRegistration()">测试屏幕注册</button>
        <button class="test-button" onclick="listRegisteredScreens()">列出已注册屏幕</button>
    </div>
    
    <div class="test-section">
        <h2>屏幕切换测试</h2>
        <button class="test-button" onclick="testShowScreen('main-menu-screen')">显示主菜单</button>
        <button class="test-button" onclick="testShowScreen('game-screen')">显示游戏屏幕</button>
        <button class="test-button" onclick="testShowScreen('levelSelectScreen')">显示关卡选择</button>
        <button class="test-button" onclick="testShowScreen('nonexistent')">测试不存在屏幕</button>
    </div>
    
    <div class="test-section">
        <h2>游戏流程测试</h2>
        <button class="test-button" onclick="simulateGameStart()">模拟开始游戏</button>
        <button class="test-button" onclick="simulateLevelSelect()">模拟关卡选择</button>
    </div>
    
    <div class="test-section">
        <h2>测试日志</h2>
        <button class="test-button" onclick="clearLog()">清空日志</button>
        <div id="log-output" class="log-output"></div>
    </div>

    <script>
        // 日志输出函数
        function log(message, type = 'info') {
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log-output').innerHTML = '';
        }

        // 测试屏幕注册
        function testScreenRegistration() {
            log('🔍 开始测试屏幕注册...', 'info');
            
            // 检查UI管理器是否存在
            if (typeof window.uiManager === 'undefined') {
                log('❌ UI管理器未找到', 'error');
                return;
            }
            
            // 检查屏幕注册
            const screens = window.uiManager.screens;
            if (screens && screens.size > 0) {
                log(`✅ 找到 ${screens.size} 个已注册屏幕`, 'success');
            } else {
                log('❌ 没有找到已注册的屏幕', 'error');
            }
        }

        // 列出已注册屏幕
        function listRegisteredScreens() {
            log('📋 列出已注册屏幕:', 'info');
            
            if (typeof window.uiManager === 'undefined') {
                log('❌ UI管理器未找到', 'error');
                return;
            }
            
            const screens = window.uiManager.screens;
            if (screens) {
                for (let [screenName, screenData] of screens) {
                    const element = screenData.element || screenData;
                    const exists = element ? '✅' : '❌';
                    log(`  ${exists} ${screenName}`, exists === '✅' ? 'success' : 'error');
                }
            }
        }

        // 测试显示屏幕
        function testShowScreen(screenName) {
            log(`🎯 测试显示屏幕: ${screenName}`, 'info');
            
            if (typeof window.uiManager === 'undefined') {
                log('❌ UI管理器未找到', 'error');
                return;
            }
            
            try {
                window.uiManager.showScreen(screenName);
                log(`✅ 屏幕切换请求已发送: ${screenName}`, 'success');
            } catch (error) {
                log(`❌ 屏幕切换失败: ${error.message}`, 'error');
            }
        }

        // 模拟开始游戏
        function simulateGameStart() {
            log('🎮 模拟开始游戏流程...', 'info');
            
            if (typeof window.gameApp === 'undefined') {
                log('❌ 游戏应用未找到', 'error');
                return;
            }
            
            try {
                window.gameApp.startGame();
                log('✅ 开始游戏请求已发送', 'success');
            } catch (error) {
                log(`❌ 开始游戏失败: ${error.message}`, 'error');
            }
        }

        // 模拟关卡选择
        function simulateLevelSelect() {
            log('🎯 模拟关卡选择流程...', 'info');
            
            if (typeof window.levelSelect === 'undefined') {
                log('❌ 关卡选择组件未找到', 'error');
                return;
            }
            
            try {
                window.levelSelect.show();
                log('✅ 关卡选择显示请求已发送', 'success');
            } catch (error) {
                log(`❌ 关卡选择显示失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            log('🚀 测试页面加载完成', 'info');
            
            // 等待一段时间让游戏初始化
            setTimeout(() => {
                testScreenRegistration();
                listRegisteredScreens();
            }, 2000);
        });

        // 监听控制台错误
        window.addEventListener('error', (event) => {
            log(`❌ JavaScript错误: ${event.error.message}`, 'error');
        });

        // 重写console.error来捕获错误
        const originalConsoleError = console.error;
        console.error = function(...args) {
            log(`❌ 控制台错误: ${args.join(' ')}`, 'error');
            originalConsoleError.apply(console, args);
        };
    </script>
</body>
</html>
