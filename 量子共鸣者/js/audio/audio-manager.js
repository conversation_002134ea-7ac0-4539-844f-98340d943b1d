/**
 * 量子共鸣者 - 音频管理器
 * 统一管理所有音频功能和组件
 */

class AudioManager {
    constructor() {
        this.audioEngine = null;
        this.synthesizer = null;
        this.sequencer = null;
        this.visualizer = null;
        this.effects = new Map();
        
        // 音频状态
        this.isInitialized = false;
        this.isMuted = false;
        this.masterVolume = 1.0;
        
        // 游戏音频配置
        this.gameConfig = {
            backgroundMusic: {
                enabled: true,
                volume: 0.6,
                bpm: 120
            },
            soundEffects: {
                enabled: true,
                volume: 0.8
            },
            microphone: {
                enabled: false,
                sensitivity: 0.8
            },
            visualization: {
                enabled: true,
                style: 'quantum'
            }
        };
        
        // 音频主题
        this.themes = {
            quantum: {
                baseFrequency: 55,
                harmonics: [1, 2, 3, 5, 8],
                colors: ['#00ffff', '#ff00ff', '#ffff00'],
                effects: ['reverb', 'chorus']
            },
            space: {
                baseFrequency: 40,
                harmonics: [1, 1.5, 2, 3, 4],
                colors: ['#0066ff', '#6600ff', '#ff6600'],
                effects: ['delay', 'reverb']
            },
            energy: {
                baseFrequency: 80,
                harmonics: [1, 2, 4, 8, 16],
                colors: ['#ff0066', '#66ff00', '#0066ff'],
                effects: ['distortion', 'filter']
            }
        };
        
        this.currentTheme = 'quantum';
        
        console.log('🎵 音频管理器已创建');
    }

    /**
     * 初始化音频管理器
     * @param {Object} config - 配置选项
     */
    async init(config = {}) {
        try {
            // 合并配置
            Object.assign(this.gameConfig, config);
            
            // 初始化音频引擎
            this.audioEngine = window.audioEngine;
            await this.audioEngine.init();
            
            // 创建合成器
            this.synthesizer = new AudioSynthesizer(this.audioEngine, {
                waveform: 'sine',
                attack: 0.01,
                decay: 0.1,
                sustain: 0.7,
                release: 0.3
            });
            
            // 创建序列器
            this.sequencer = new AudioSequencer(this.audioEngine);
            this.setupSequencer();
            
            // 创建效果器
            this.createEffects();
            
            // 设置音频主题
            this.setTheme(this.currentTheme);
            
            this.isInitialized = true;
            console.log('✅ 音频管理器初始化完成');
            
            return true;
        } catch (error) {
            console.error('❌ 音频管理器初始化失败:', error);
            return false;
        }
    }

    /**
     * 设置可视化器
     * @param {HTMLCanvasElement} canvas - 画布元素
     */
    setupVisualizer(canvas) {
        if (!canvas) return;
        
        this.visualizer = new AudioVisualizer(this.audioEngine, canvas);
        
        if (this.gameConfig.visualization.enabled) {
            this.visualizer.start();
        }
    }

    /**
     * 设置序列器
     */
    setupSequencer() {
        // 设置BPM
        this.sequencer.setBPM(this.gameConfig.backgroundMusic.bpm);

        // 设置默认模式
        this.sequencer.setPattern(this.currentTheme || 'quantum');

        console.log('🎼 序列器设置完成');
    }

    /**
     * 创建音频效果器
     */
    createEffects() {
        const theme = this.themes[this.currentTheme];
        
        // 为当前主题创建效果器
        theme.effects.forEach(effectType => {
            let params = {};
            
            switch (effectType) {
                case 'reverb':
                    params = { roomSize: 2, wetness: 0.3, decay: 2 };
                    break;
                case 'delay':
                    params = { delayTime: 0.3, feedback: 0.4, wetness: 0.2 };
                    break;
                case 'chorus':
                    params = { rate: 1.5, depth: 0.01, wetness: 0.4 };
                    break;
                case 'distortion':
                    params = { amount: 20, drive: 1.2, output: 0.7 };
                    break;
                case 'filter':
                    params = { type: 'lowpass', frequency: 2000, Q: 1 };
                    break;
            }
            
            const effect = new AudioEffect(this.audioEngine, effectType, params);
            this.effects.set(effectType, effect);
        });
    }

    /**
     * 设置音频主题
     * @param {string} themeName - 主题名称
     */
    setTheme(themeName) {
        if (!this.themes[themeName]) {
            console.warn(`⚠️ 未知的音频主题: ${themeName}`);
            return;
        }
        
        this.currentTheme = themeName;
        
        // 清除现有效果器
        this.effects.forEach(effect => effect.destroy());
        this.effects.clear();
        
        // 创建新的效果器
        this.createEffects();
        
        // 更新序列器
        if (this.sequencer) {
            this.sequencer.setPattern(theme);
        }
        
        // 更新可视化器配置
        if (this.visualizer) {
            const theme = this.themes[themeName];
            this.visualizer.setConfig({
                primaryColor: theme.colors[0],
                secondaryColor: theme.colors[1],
                accentColor: theme.colors[2]
            });
        }
        
        console.log(`🎨 音频主题已切换到: ${themeName}`);
    }

    /**
     * 开始背景音乐
     */
    startBackgroundMusic() {
        if (!this.isInitialized || !this.gameConfig.backgroundMusic.enabled) return;
        
        this.sequencer.start();
        
        // 启动环境音效
        if (this.synthesizer) {
            this.synthesizer.playAmbientSound(this.currentTheme);
        }
        
        console.log('🎵 背景音乐已开始');
    }

    /**
     * 停止背景音乐
     */
    stopBackgroundMusic() {
        if (this.sequencer) {
            this.sequencer.stop();
        }
        
        if (this.synthesizer) {
            this.synthesizer.stopAmbientSound();
        }
        
        console.log('🎵 背景音乐已停止');
    }

    /**
     * 播放粒子激活音效
     * @param {number} frequency - 粒子频率
     * @param {number} energy - 能量级别
     */
    playParticleActivation(frequency, energy = 1.0) {
        if (!this.gameConfig.soundEffects.enabled || !this.synthesizer) return;
        
        this.synthesizer.playParticleActivation(frequency, energy);
    }

    /**
     * 播放量子共鸣音效
     * @param {number} frequency - 基础频率
     * @param {number} strength - 共鸣强度
     * @param {number} duration - 持续时间
     */
    playQuantumResonance(frequency, strength, duration = 1.0) {
        if (!this.gameConfig.soundEffects.enabled || !this.synthesizer) return;
        
        this.synthesizer.playQuantumResonance(frequency, strength, duration);
    }

    /**
     * 播放连锁反应音效
     * @param {Array} frequencies - 频率数组
     * @param {number} delay - 延迟时间
     */
    playChainReaction(frequencies, delay = 0.1) {
        if (!this.gameConfig.soundEffects.enabled || !this.synthesizer) return;

        this.synthesizer.playChainReaction(frequencies, delay);
    }

    /**
     * 播放UI音效
     * @param {string} soundType - 音效类型 ('click', 'hover', 'error', 'success', 'combo')
     */
    playUISound(soundType) {
        // 检查音效是否启用
        if (!this.gameConfig.soundEffects.enabled) {
            console.log(`🔇 UI音效已禁用，跳过播放: ${soundType}`);
            return;
        }

        // 检查音频管理器是否已初始化
        if (!this.isInitialized) {
            console.warn(`⚠️ 音频管理器未初始化，无法播放UI音效: ${soundType}`);
            return;
        }

        // 检查合成器是否可用
        if (!this.synthesizer) {
            console.warn(`⚠️ 音频合成器不可用，无法播放UI音效: ${soundType}`);
            return;
        }

        try {
            // 根据音效类型播放不同的UI音效
            switch (soundType) {
                case 'click':
                    // 播放点击音效 - 短促的高频音
                    this.synthesizer.playParticleActivation(800, 0.3);
                    break;
                case 'hover':
                    // 播放悬停音效 - 轻柔的中频音
                    this.synthesizer.playParticleActivation(600, 0.2);
                    break;
                case 'error':
                    // 播放错误音效 - 低频警告音
                    this.synthesizer.playQuantumResonance(200, 0.8, 0.5);
                    break;
                case 'success':
                    // 播放成功音效 - 上升的和谐音
                    this.synthesizer.playChainReaction([440, 550, 660], 0.05);
                    break;
                case 'combo':
                    // 播放连击音效 - 快速的高频序列
                    this.synthesizer.playChainReaction([880, 1100, 1320], 0.03);
                    break;
                default:
                    console.warn(`⚠️ 未知的UI音效类型: ${soundType}`);
                    return;
            }

            console.log(`🔊 播放UI音效: ${soundType}`);
        } catch (error) {
            console.error(`❌ 播放UI音效失败 (${soundType}):`, error);
        }
    }

    /**
     * 设置主音量
     * @param {number} volume - 音量 (0-1)
     */
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        
        if (this.audioEngine) {
            this.audioEngine.setVolume('master', this.masterVolume);
        }
    }

    /**
     * 设置音乐音量
     * @param {number} volume - 音量 (0-1)
     */
    setMusicVolume(volume) {
        this.gameConfig.backgroundMusic.volume = Math.max(0, Math.min(1, volume));
        
        if (this.audioEngine) {
            this.audioEngine.setVolume('music', this.gameConfig.backgroundMusic.volume);
        }
    }

    /**
     * 设置音效音量
     * @param {number} volume - 音量 (0-1)
     */
    setSFXVolume(volume) {
        this.gameConfig.soundEffects.volume = Math.max(0, Math.min(1, volume));
        
        if (this.audioEngine) {
            this.audioEngine.setVolume('sfx', this.gameConfig.soundEffects.volume);
        }
    }

    /**
     * 静音/取消静音
     * @param {boolean} muted - 是否静音
     */
    setMuted(muted) {
        this.isMuted = muted;
        
        if (this.audioEngine) {
            this.audioEngine.setVolume('master', muted ? 0 : this.masterVolume);
        }
    }

    /**
     * 启用/禁用麦克风
     * @param {boolean} enabled - 是否启用
     */
    setMicrophoneEnabled(enabled) {
        this.gameConfig.microphone.enabled = enabled;
        
        if (this.audioEngine) {
            if (enabled) {
                this.audioEngine.enableMicrophone();
            } else {
                this.audioEngine.disableMicrophone();
            }
        }
    }

    /**
     * 启用/禁用可视化
     * @param {boolean} enabled - 是否启用
     */
    setVisualizationEnabled(enabled) {
        this.gameConfig.visualization.enabled = enabled;
        
        if (this.visualizer) {
            if (enabled) {
                this.visualizer.start();
            } else {
                this.visualizer.stop();
            }
        }
    }

    /**
     * 节拍回调
     * @param {number} beat - 节拍数
     */
    onBeat(beat) {
        // 可以在这里添加节拍同步的游戏逻辑
        // 例如：粒子脉冲、UI动画等
    }

    /**
     * 步骤回调
     * @param {number} step - 步骤数
     * @param {Object} data - 步骤数据
     */
    onStep(step, data) {
        // 可以在这里添加步骤同步的游戏逻辑
    }

    /**
     * 获取当前频率
     * @returns {number} 当前主要频率
     */
    getCurrentFrequency() {
        if (this.visualizer) {
            return this.visualizer.getDominantFrequency();
        }
        return 0;
    }

    /**
     * 获取音频配置
     * @returns {Object} 当前配置
     */
    getConfig() {
        return { ...this.gameConfig };
    }

    /**
     * 暂停音频
     */
    pause() {
        this.stopBackgroundMusic();
        
        if (this.audioEngine) {
            this.audioEngine.suspend();
        }
    }

    /**
     * 恢复音频
     */
    resume() {
        if (this.audioEngine) {
            this.audioEngine.resume();
        }
        
        if (this.gameConfig.backgroundMusic.enabled) {
            this.startBackgroundMusic();
        }
    }

    /**
     * 销毁音频管理器
     */
    destroy() {
        this.stopBackgroundMusic();
        
        if (this.visualizer) {
            this.visualizer.destroy();
        }
        
        if (this.synthesizer) {
            this.synthesizer.destroy();
        }
        
        if (this.sequencer) {
            this.sequencer.destroy();
        }
        
        this.effects.forEach(effect => effect.destroy());
        this.effects.clear();
        
        this.isInitialized = false;
        
        console.log('🎵 音频管理器已销毁');
    }
}

// 创建全局音频管理器实例
window.audioManager = new AudioManager();
