/**
 * 量子共鸣者 - 音频效果器
 * 用于添加各种音频效果
 */

class AudioEffect {
    constructor(audioEngine, type, params = {}) {
        this.audioEngine = audioEngine;
        this.type = type;
        this.params = params;
        this.inputNode = null;
        this.outputNode = null;
        this.effectNodes = [];
        this.isActive = true;
        
        this.createEffect();
        
        console.log(`🎛️ 音频效果器已创建: ${type}`);
    }

    /**
     * 创建效果器
     */
    createEffect() {
        if (!this.audioEngine.audioContext) return;
        
        const audioContext = this.audioEngine.audioContext;
        
        switch (this.type) {
            case 'reverb':
                this.createReverb(audioContext);
                break;
            case 'delay':
                this.createDelay(audioContext);
                break;
            case 'distortion':
                this.createDistortion(audioContext);
                break;
            case 'chorus':
                this.createChorus(audioContext);
                break;
            case 'filter':
                this.createFilter(audioContext);
                break;
            case 'compressor':
                this.createCompressor(audioContext);
                break;
            case 'bitcrusher':
                this.createBitcrusher(audioContext);
                break;
            default:
                // 直通效果器
                this.inputNode = audioContext.createGain();
                this.outputNode = this.inputNode;
        }
    }

    /**
     * 创建混响效果
     * @param {AudioContext} audioContext - 音频上下文
     */
    createReverb(audioContext) {
        const convolver = audioContext.createConvolver();
        const wetGain = audioContext.createGain();
        const dryGain = audioContext.createGain();
        const outputGain = audioContext.createGain();
        
        // 创建冲激响应
        const impulseLength = audioContext.sampleRate * (this.params.roomSize || 2);
        const impulse = audioContext.createBuffer(2, impulseLength, audioContext.sampleRate);
        
        for (let channel = 0; channel < 2; channel++) {
            const channelData = impulse.getChannelData(channel);
            for (let i = 0; i < impulseLength; i++) {
                const decay = Math.pow(1 - i / impulseLength, this.params.decay || 2);
                channelData[i] = (Math.random() * 2 - 1) * decay;
            }
        }
        
        convolver.buffer = impulse;
        
        // 设置增益
        wetGain.gain.value = this.params.wetness || 0.3;
        dryGain.gain.value = 1 - wetGain.gain.value;
        
        // 连接节点
        this.inputNode = audioContext.createGain();
        this.inputNode.connect(dryGain);
        this.inputNode.connect(convolver);
        convolver.connect(wetGain);
        
        dryGain.connect(outputGain);
        wetGain.connect(outputGain);
        
        this.outputNode = outputGain;
        this.effectNodes = [convolver, wetGain, dryGain, outputGain];
    }

    /**
     * 创建延迟效果
     * @param {AudioContext} audioContext - 音频上下文
     */
    createDelay(audioContext) {
        const delay = audioContext.createDelay(1.0);
        const feedback = audioContext.createGain();
        const wetGain = audioContext.createGain();
        const dryGain = audioContext.createGain();
        const outputGain = audioContext.createGain();
        
        // 设置参数
        delay.delayTime.value = this.params.delayTime || 0.3;
        feedback.gain.value = this.params.feedback || 0.4;
        wetGain.gain.value = this.params.wetness || 0.3;
        dryGain.gain.value = 1 - wetGain.gain.value;
        
        // 连接节点
        this.inputNode = audioContext.createGain();
        this.inputNode.connect(dryGain);
        this.inputNode.connect(delay);
        
        delay.connect(feedback);
        delay.connect(wetGain);
        feedback.connect(delay);
        
        dryGain.connect(outputGain);
        wetGain.connect(outputGain);
        
        this.outputNode = outputGain;
        this.effectNodes = [delay, feedback, wetGain, dryGain, outputGain];
    }

    /**
     * 创建失真效果
     * @param {AudioContext} audioContext - 音频上下文
     */
    createDistortion(audioContext) {
        const waveshaper = audioContext.createWaveShaper();
        const inputGain = audioContext.createGain();
        const outputGain = audioContext.createGain();
        
        // 创建失真曲线
        const amount = this.params.amount || 50;
        const samples = 44100;
        const curve = new Float32Array(samples);
        const deg = Math.PI / 180;
        
        for (let i = 0; i < samples; i++) {
            const x = (i * 2) / samples - 1;
            curve[i] = ((3 + amount) * x * 20 * deg) / (Math.PI + amount * Math.abs(x));
        }
        
        waveshaper.curve = curve;
        waveshaper.oversample = '4x';
        
        // 设置增益
        inputGain.gain.value = this.params.drive || 1;
        outputGain.gain.value = this.params.output || 0.5;
        
        // 连接节点
        this.inputNode = inputGain;
        inputGain.connect(waveshaper);
        waveshaper.connect(outputGain);
        this.outputNode = outputGain;
        
        this.effectNodes = [waveshaper, inputGain, outputGain];
    }

    /**
     * 创建合唱效果
     * @param {AudioContext} audioContext - 音频上下文
     */
    createChorus(audioContext) {
        const delay = audioContext.createDelay(0.05);
        const lfo = audioContext.createOscillator();
        const lfoGain = audioContext.createGain();
        const wetGain = audioContext.createGain();
        const dryGain = audioContext.createGain();
        const outputGain = audioContext.createGain();
        
        // 设置LFO
        lfo.type = 'sine';
        lfo.frequency.value = this.params.rate || 1.5;
        lfoGain.gain.value = this.params.depth || 0.01;
        
        // 设置延迟
        delay.delayTime.value = this.params.delayTime || 0.02;
        
        // 设置增益
        wetGain.gain.value = this.params.wetness || 0.5;
        dryGain.gain.value = 1 - wetGain.gain.value;
        
        // 连接节点
        this.inputNode = audioContext.createGain();
        this.inputNode.connect(dryGain);
        this.inputNode.connect(delay);
        
        lfo.connect(lfoGain);
        lfoGain.connect(delay.delayTime);
        delay.connect(wetGain);
        
        dryGain.connect(outputGain);
        wetGain.connect(outputGain);
        
        this.outputNode = outputGain;
        this.effectNodes = [delay, lfo, lfoGain, wetGain, dryGain, outputGain];
        
        // 启动LFO
        lfo.start();
    }

    /**
     * 创建滤波器效果
     * @param {AudioContext} audioContext - 音频上下文
     */
    createFilter(audioContext) {
        const filter = audioContext.createBiquadFilter();
        
        filter.type = this.params.type || 'lowpass';
        filter.frequency.value = this.params.frequency || 1000;
        filter.Q.value = this.params.Q || 1;
        
        this.inputNode = filter;
        this.outputNode = filter;
        this.effectNodes = [filter];
    }

    /**
     * 创建压缩器效果
     * @param {AudioContext} audioContext - 音频上下文
     */
    createCompressor(audioContext) {
        const compressor = audioContext.createDynamicsCompressor();
        
        compressor.threshold.value = this.params.threshold || -24;
        compressor.knee.value = this.params.knee || 30;
        compressor.ratio.value = this.params.ratio || 12;
        compressor.attack.value = this.params.attack || 0.003;
        compressor.release.value = this.params.release || 0.25;
        
        this.inputNode = compressor;
        this.outputNode = compressor;
        this.effectNodes = [compressor];
    }

    /**
     * 创建位压缩效果
     * @param {AudioContext} audioContext - 音频上下文
     */
    createBitcrusher(audioContext) {
        const scriptProcessor = audioContext.createScriptProcessor(4096, 1, 1);
        const bits = this.params.bits || 8;
        const normFreq = this.params.normFreq || 0.1;
        
        let phaser = 0;
        let last = 0;
        
        scriptProcessor.onaudioprocess = (e) => {
            const input = e.inputBuffer.getChannelData(0);
            const output = e.outputBuffer.getChannelData(0);
            
            for (let i = 0; i < input.length; i++) {
                phaser += normFreq;
                if (phaser >= 1.0) {
                    phaser -= 1.0;
                    last = Math.floor(input[i] * Math.pow(2, bits - 1)) / Math.pow(2, bits - 1);
                }
                output[i] = last;
            }
        };
        
        this.inputNode = scriptProcessor;
        this.outputNode = scriptProcessor;
        this.effectNodes = [scriptProcessor];
    }

    /**
     * 连接到其他节点
     * @param {AudioNode} destination - 目标节点
     */
    connect(destination) {
        if (this.outputNode) {
            this.outputNode.connect(destination);
        }
    }

    /**
     * 断开连接
     */
    disconnect() {
        if (this.outputNode) {
            this.outputNode.disconnect();
        }
    }

    /**
     * 设置参数
     * @param {string} param - 参数名
     * @param {number} value - 参数值
     */
    setParameter(param, value) {
        this.params[param] = value;
        
        // 根据效果器类型更新参数
        switch (this.type) {
            case 'reverb':
                if (param === 'wetness' && this.effectNodes[1]) {
                    this.effectNodes[1].gain.value = value;
                    this.effectNodes[2].gain.value = 1 - value;
                }
                break;
            case 'delay':
                if (param === 'delayTime' && this.effectNodes[0]) {
                    this.effectNodes[0].delayTime.value = value;
                } else if (param === 'feedback' && this.effectNodes[1]) {
                    this.effectNodes[1].gain.value = value;
                }
                break;
            case 'filter':
                if (param === 'frequency' && this.effectNodes[0]) {
                    this.effectNodes[0].frequency.value = value;
                } else if (param === 'Q' && this.effectNodes[0]) {
                    this.effectNodes[0].Q.value = value;
                }
                break;
            // 其他效果器的参数更新...
        }
    }

    /**
     * 启用/禁用效果器
     * @param {boolean} active - 是否启用
     */
    setActive(active) {
        this.isActive = active;
        
        if (this.inputNode && this.outputNode) {
            if (active) {
                // 重新连接效果器链
                this.createEffect();
            } else {
                // 直通连接
                this.inputNode.disconnect();
                this.inputNode.connect(this.outputNode);
            }
        }
    }

    /**
     * 销毁效果器
     */
    destroy() {
        this.disconnect();
        
        // 停止振荡器
        for (const node of this.effectNodes) {
            if (node.stop && typeof node.stop === 'function') {
                try {
                    node.stop();
                } catch (e) {
                    // 忽略已经停止的振荡器错误
                }
            }
        }
        
        this.effectNodes = [];
        this.inputNode = null;
        this.outputNode = null;
        
        console.log(`🎛️ 音频效果器已销毁: ${this.type}`);
    }
}

// 导出类
window.AudioEffect = AudioEffect;
