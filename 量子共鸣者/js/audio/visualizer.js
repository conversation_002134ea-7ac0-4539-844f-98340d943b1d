/**
 * 量子共鸣者 - 音频可视化器
 * 用于实时音频数据可视化
 */

class AudioVisualizer {
    constructor(audioEngine, canvas) {
        this.audioEngine = audioEngine;
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.isActive = false;
        this.animationId = null;
        
        // 可视化配置
        this.config = {
            fftSize: 2048,
            smoothingTimeConstant: 0.8,
            minDecibels: -90,
            maxDecibels: -10,
            backgroundColor: 'rgba(10, 15, 35, 0.1)',
            primaryColor: '#00ffff',
            secondaryColor: '#ff00ff',
            accentColor: '#ffff00'
        };
        
        // 可视化数据
        this.frequencyData = null;
        this.timeData = null;
        this.bufferLength = 0;
        
        // 粒子系统
        this.particles = [];
        this.maxParticles = 100;
        
        console.log('📊 音频可视化器已创建');
    }

    /**
     * 初始化可视化器
     */
    init() {
        if (!this.audioEngine.analyser) {
            console.warn('⚠️ 音频分析器未初始化');
            return false;
        }
        
        // 设置分析器参数
        this.audioEngine.analyser.fftSize = this.config.fftSize;
        this.audioEngine.analyser.smoothingTimeConstant = this.config.smoothingTimeConstant;
        this.audioEngine.analyser.minDecibels = this.config.minDecibels;
        this.audioEngine.analyser.maxDecibels = this.config.maxDecibels;
        
        // 初始化数据数组
        this.bufferLength = this.audioEngine.analyser.frequencyBinCount;
        this.frequencyData = new Uint8Array(this.bufferLength);
        this.timeData = new Uint8Array(this.bufferLength);
        
        // 初始化粒子
        this.initParticles();
        
        console.log('📊 音频可视化器初始化完成');
        return true;
    }

    /**
     * 初始化粒子系统
     */
    initParticles() {
        this.particles = [];
        for (let i = 0; i < this.maxParticles; i++) {
            this.particles.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                vx: (Math.random() - 0.5) * 2,
                vy: (Math.random() - 0.5) * 2,
                size: Math.random() * 3 + 1,
                alpha: Math.random() * 0.5 + 0.5,
                frequency: Math.random() * this.bufferLength,
                phase: Math.random() * Math.PI * 2
            });
        }
    }

    /**
     * 开始可视化
     */
    start() {
        if (!this.init()) return;
        
        this.isActive = true;
        this.animate();
        
        console.log('▶️ 音频可视化器开始运行');
    }

    /**
     * 停止可视化
     */
    stop() {
        this.isActive = false;
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
        
        console.log('⏹️ 音频可视化器停止运行');
    }

    /**
     * 动画循环
     */
    animate() {
        if (!this.isActive) return;
        
        // 获取音频数据
        this.updateAudioData();
        
        // 清空画布
        this.clearCanvas();
        
        // 绘制可视化效果
        this.drawFrequencyBars();
        this.drawWaveform();
        this.drawParticles();
        this.drawFrequencyCircle();
        
        // 继续动画
        this.animationId = requestAnimationFrame(() => this.animate());
    }

    /**
     * 更新音频数据
     */
    updateAudioData() {
        if (this.audioEngine.analyser) {
            this.audioEngine.analyser.getByteFrequencyData(this.frequencyData);
            this.audioEngine.analyser.getByteTimeDomainData(this.timeData);
        }
    }

    /**
     * 清空画布
     */
    clearCanvas() {
        this.ctx.fillStyle = this.config.backgroundColor;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }

    /**
     * 绘制频率条
     */
    drawFrequencyBars() {
        if (!this.frequencyData) return;
        
        const barWidth = this.canvas.width / this.bufferLength * 2;
        let x = 0;
        
        for (let i = 0; i < this.bufferLength; i++) {
            const barHeight = (this.frequencyData[i] / 255) * this.canvas.height * 0.8;
            
            // 创建渐变
            const gradient = this.ctx.createLinearGradient(0, this.canvas.height, 0, this.canvas.height - barHeight);
            gradient.addColorStop(0, this.config.primaryColor);
            gradient.addColorStop(0.5, this.config.secondaryColor);
            gradient.addColorStop(1, this.config.accentColor);
            
            this.ctx.fillStyle = gradient;
            this.ctx.fillRect(x, this.canvas.height - barHeight, barWidth, barHeight);
            
            x += barWidth + 1;
        }
    }

    /**
     * 绘制波形
     */
    drawWaveform() {
        if (!this.timeData) return;
        
        this.ctx.lineWidth = 2;
        this.ctx.strokeStyle = this.config.primaryColor;
        this.ctx.beginPath();
        
        const sliceWidth = this.canvas.width / this.bufferLength;
        let x = 0;
        
        for (let i = 0; i < this.bufferLength; i++) {
            const v = this.timeData[i] / 128.0;
            const y = v * this.canvas.height / 2;
            
            if (i === 0) {
                this.ctx.moveTo(x, y);
            } else {
                this.ctx.lineTo(x, y);
            }
            
            x += sliceWidth;
        }
        
        this.ctx.stroke();
    }

    /**
     * 绘制粒子系统
     */
    drawParticles() {
        if (!this.frequencyData) return;
        
        this.particles.forEach((particle, index) => {
            // 根据频率数据更新粒子
            const freqIndex = Math.floor(particle.frequency);
            const amplitude = this.frequencyData[freqIndex] / 255;
            
            // 更新粒子位置
            particle.x += particle.vx * (1 + amplitude);
            particle.y += particle.vy * (1 + amplitude);
            
            // 边界检查
            if (particle.x < 0 || particle.x > this.canvas.width) {
                particle.vx *= -1;
                particle.x = Math.max(0, Math.min(this.canvas.width, particle.x));
            }
            if (particle.y < 0 || particle.y > this.canvas.height) {
                particle.vy *= -1;
                particle.y = Math.max(0, Math.min(this.canvas.height, particle.y));
            }
            
            // 更新粒子大小和透明度
            particle.size = (1 + amplitude * 2) * (Math.random() * 2 + 1);
            particle.alpha = 0.3 + amplitude * 0.7;
            
            // 绘制粒子
            this.ctx.save();
            this.ctx.globalAlpha = particle.alpha;
            this.ctx.fillStyle = this.getParticleColor(amplitude);
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            this.ctx.fill();
            
            // 添加发光效果
            this.ctx.shadowBlur = 10;
            this.ctx.shadowColor = this.getParticleColor(amplitude);
            this.ctx.fill();
            
            this.ctx.restore();
        });
    }

    /**
     * 绘制频率圆环
     */
    drawFrequencyCircle() {
        if (!this.frequencyData) return;
        
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        const baseRadius = Math.min(this.canvas.width, this.canvas.height) / 6;
        
        this.ctx.save();
        this.ctx.translate(centerX, centerY);
        
        const angleStep = (Math.PI * 2) / this.bufferLength;
        
        for (let i = 0; i < this.bufferLength; i++) {
            const amplitude = this.frequencyData[i] / 255;
            const radius = baseRadius + amplitude * 50;
            const angle = i * angleStep;
            
            const x = Math.cos(angle) * radius;
            const y = Math.sin(angle) * radius;
            
            // 绘制频率点
            this.ctx.fillStyle = this.getFrequencyColor(i, amplitude);
            this.ctx.beginPath();
            this.ctx.arc(x, y, 1 + amplitude * 3, 0, Math.PI * 2);
            this.ctx.fill();
            
            // 连接到中心的线
            if (amplitude > 0.1) {
                this.ctx.strokeStyle = this.getFrequencyColor(i, amplitude * 0.5);
                this.ctx.lineWidth = amplitude * 2;
                this.ctx.beginPath();
                this.ctx.moveTo(0, 0);
                this.ctx.lineTo(x, y);
                this.ctx.stroke();
            }
        }
        
        this.ctx.restore();
    }

    /**
     * 获取粒子颜色
     * @param {number} amplitude - 振幅
     * @returns {string} 颜色字符串
     */
    getParticleColor(amplitude) {
        const hue = (amplitude * 360 + Date.now() * 0.1) % 360;
        return `hsl(${hue}, 100%, ${50 + amplitude * 50}%)`;
    }

    /**
     * 获取频率颜色
     * @param {number} index - 频率索引
     * @param {number} amplitude - 振幅
     * @returns {string} 颜色字符串
     */
    getFrequencyColor(index, amplitude) {
        const hue = (index / this.bufferLength * 360 + Date.now() * 0.05) % 360;
        return `hsla(${hue}, 100%, ${50 + amplitude * 50}%, ${amplitude})`;
    }

    /**
     * 设置可视化配置
     * @param {Object} config - 配置对象
     */
    setConfig(config) {
        Object.assign(this.config, config);
        
        // 重新初始化如果需要
        if (this.isActive) {
            this.init();
        }
    }

    /**
     * 调整画布大小
     * @param {number} width - 宽度
     * @param {number} height - 高度
     */
    resize(width, height) {
        this.canvas.width = width;
        this.canvas.height = height;
        
        // 重新初始化粒子位置
        this.initParticles();
    }

    /**
     * 获取当前频谱数据
     * @returns {Uint8Array} 频谱数据
     */
    getFrequencyData() {
        return this.frequencyData;
    }

    /**
     * 获取当前时域数据
     * @returns {Uint8Array} 时域数据
     */
    getTimeData() {
        return this.timeData;
    }

    /**
     * 获取主要频率
     * @returns {number} 主要频率索引
     */
    getDominantFrequency() {
        if (!this.frequencyData) return 0;
        
        let maxAmplitude = 0;
        let dominantIndex = 0;
        
        for (let i = 0; i < this.frequencyData.length; i++) {
            if (this.frequencyData[i] > maxAmplitude) {
                maxAmplitude = this.frequencyData[i];
                dominantIndex = i;
            }
        }
        
        // 转换为实际频率
        const nyquist = this.audioEngine.audioContext.sampleRate / 2;
        return (dominantIndex / this.bufferLength) * nyquist;
    }

    /**
     * 销毁可视化器
     */
    destroy() {
        this.stop();
        this.particles = [];
        this.frequencyData = null;
        this.timeData = null;
        
        console.log('📊 音频可视化器已销毁');
    }
}

// 导出类
window.AudioVisualizer = AudioVisualizer;
