/**
 * 量子共鸣者 - 玩家系统管理器
 * 负责玩家数据管理、统计追踪、成就系统、排行榜功能
 */

class PlayerManager {
    constructor() {
        this.isInitialized = false;
        
        // 当前玩家信息
        this.currentPlayer = null;
        
        // 玩家列表
        this.players = new Map();
        
        // 成就定义
        this.achievements = new Map();
        
        // 统计数据
        this.sessionStats = {
            startTime: Date.now(),
            levelsPlayed: 0,
            totalScore: 0,
            totalTime: 0,
            particlesActivated: 0,
            chainReactions: 0,
            maxCombo: 0
        };
        
        // 排行榜数据
        this.leaderboards = {
            highScores: [],
            fastestTimes: [],
            longestCombos: [],
            mostChainReactions: []
        };
        
        console.log('👤 玩家系统管理器已创建');
    }

    /**
     * 初始化玩家系统
     */
    init() {
        try {
            // 初始化成就系统
            this.initAchievements();
            
            // 加载玩家数据
            this.loadPlayerData();
            
            // 创建默认玩家（如果不存在）
            this.ensureDefaultPlayer();
            
            // 加载排行榜数据
            this.loadLeaderboards();
            
            this.isInitialized = true;
            console.log('✅ 玩家系统初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 玩家系统初始化失败:', error);
            return false;
        }
    }

    /**
     * 初始化成就系统
     */
    initAchievements() {
        // 基础成就
        this.achievements.set('first_level', {
            id: 'first_level',
            name: '初次体验',
            description: '完成第一个关卡',
            icon: '🎯',
            type: 'progress',
            condition: { levelsCompleted: 1 },
            points: 10
        });
        
        this.achievements.set('combo_master', {
            id: 'combo_master',
            name: '连击大师',
            description: '达成50连击',
            icon: '⚡',
            type: 'performance',
            condition: { maxCombo: 50 },
            points: 25
        });
        
        this.achievements.set('chain_reaction_expert', {
            id: 'chain_reaction_expert',
            name: '连锁反应专家',
            description: '在单个关卡中触发20次连锁反应',
            icon: '🔗',
            type: 'performance',
            condition: { chainReactionsInLevel: 20 },
            points: 30
        });
        
        this.achievements.set('speed_runner', {
            id: 'speed_runner',
            name: '速度之王',
            description: '在60秒内完成任意关卡',
            icon: '🏃',
            type: 'performance',
            condition: { levelTimeUnder: 60 },
            points: 20
        });
        
        this.achievements.set('perfectionist', {
            id: 'perfectionist',
            name: '完美主义者',
            description: '以100%精确度完成关卡',
            icon: '💎',
            type: 'performance',
            condition: { accuracy: 1.0 },
            points: 35
        });
        
        this.achievements.set('quantum_master', {
            id: 'quantum_master',
            name: '量子大师',
            description: '完成所有内置关卡',
            icon: '🌌',
            type: 'progress',
            condition: { allBuiltinLevelsCompleted: true },
            points: 100
        });
        
        this.achievements.set('level_creator', {
            id: 'level_creator',
            name: '关卡创造者',
            description: '创建并分享5个自定义关卡',
            icon: '🛠️',
            type: 'creative',
            condition: { customLevelsCreated: 5 },
            points: 50
        });
        
        this.achievements.set('social_player', {
            id: 'social_player',
            name: '社交玩家',
            description: '游玩10个其他玩家创建的关卡',
            icon: '👥',
            type: 'social',
            condition: { communityLevelsPlayed: 10 },
            points: 40
        });
        
        console.log(`🏆 已加载 ${this.achievements.size} 个成就`);
    }

    /**
     * 加载玩家数据
     */
    loadPlayerData() {
        if (!window.storageService) {
            console.warn('⚠️ 存储服务不可用，使用默认玩家数据');
            return;
        }
        
        // 加载玩家列表
        const playersData = storageService.get('players') || {};
        
        Object.entries(playersData).forEach(([playerId, playerData]) => {
            this.players.set(playerId, playerData);
        });
        
        // 加载当前玩家
        const currentPlayerId = storageService.get('currentPlayer') || 'default';
        this.currentPlayer = this.players.get(currentPlayerId);
        
        console.log(`👤 已加载 ${this.players.size} 个玩家档案`);
    }

    /**
     * 确保存在默认玩家
     */
    ensureDefaultPlayer() {
        if (!this.players.has('default')) {
            const defaultPlayer = this.createPlayer('default', '玩家');
            this.players.set('default', defaultPlayer);
            this.savePlayerData();
        }
        
        if (!this.currentPlayer) {
            this.currentPlayer = this.players.get('default');
            this.setCurrentPlayer('default');
        }
    }

    /**
     * 创建新玩家
     * @param {string} playerId - 玩家ID
     * @param {string} playerName - 玩家名称
     * @returns {Object} 玩家数据对象
     */
    createPlayer(playerId, playerName) {
        const player = {
            id: playerId,
            name: playerName,
            createdAt: new Date().toISOString(),
            lastPlayedAt: new Date().toISOString(),
            
            // 游戏统计
            stats: {
                totalPlayTime: 0,
                totalScore: 0,
                levelsCompleted: 0,
                levelsPlayed: 0,
                bestScore: 0,
                bestCombo: 0,
                totalParticlesActivated: 0,
                totalChainReactions: 0,
                averageAccuracy: 0,
                customLevelsCreated: 0,
                communityLevelsPlayed: 0
            },
            
            // 关卡进度
            levelProgress: {},
            
            // 已解锁成就
            achievements: {},
            
            // 设置偏好
            preferences: {
                difficulty: 'normal',
                audioVolume: 0.8,
                effectsVolume: 0.6,
                visualEffects: true,
                autoSave: true,
                language: 'zh'
            },
            
            // 社交数据
            social: {
                friendsList: [],
                sharedLevels: [],
                favoriteCreators: []
            }
        };
        
        console.log(`👤 创建新玩家: ${playerName} (${playerId})`);
        return player;
    }

    /**
     * 设置当前玩家
     * @param {string} playerId - 玩家ID
     */
    setCurrentPlayer(playerId) {
        const player = this.players.get(playerId);
        if (!player) {
            console.error('❌ 玩家不存在:', playerId);
            return false;
        }
        
        this.currentPlayer = player;
        this.currentPlayer.lastPlayedAt = new Date().toISOString();
        
        // 保存当前玩家设置
        if (window.storageService) {
            storageService.set('currentPlayer', playerId);
        }
        
        console.log(`👤 切换到玩家: ${player.name}`);
        return true;
    }

    /**
     * 获取当前玩家
     * @returns {Object|null} 当前玩家数据
     */
    getCurrentPlayer() {
        return this.currentPlayer;
    }

    /**
     * 获取玩家列表
     * @returns {Array} 玩家列表
     */
    getPlayerList() {
        return Array.from(this.players.values());
    }

    /**
     * 更新玩家统计数据
     * @param {Object} stats - 统计数据更新
     */
    updatePlayerStats(stats) {
        if (!this.currentPlayer) {
            console.warn('⚠️ 没有当前玩家，无法更新统计');
            return;
        }
        
        // 更新玩家统计
        Object.entries(stats).forEach(([key, value]) => {
            if (typeof value === 'number') {
                if (key.startsWith('total') || key.startsWith('levels')) {
                    // 累加统计
                    this.currentPlayer.stats[key] = (this.currentPlayer.stats[key] || 0) + value;
                } else if (key.startsWith('best') || key.startsWith('max')) {
                    // 最佳记录统计
                    this.currentPlayer.stats[key] = Math.max(this.currentPlayer.stats[key] || 0, value);
                } else if (key === 'averageAccuracy') {
                    // 平均值统计（需要特殊处理）
                    const currentAvg = this.currentPlayer.stats.averageAccuracy || 0;
                    const totalLevels = this.currentPlayer.stats.levelsPlayed || 1;
                    this.currentPlayer.stats.averageAccuracy = (currentAvg * (totalLevels - 1) + value) / totalLevels;
                } else {
                    this.currentPlayer.stats[key] = value;
                }
            } else {
                this.currentPlayer.stats[key] = value;
            }
        });
        
        // 更新会话统计
        Object.entries(stats).forEach(([key, value]) => {
            if (this.sessionStats.hasOwnProperty(key) && typeof value === 'number') {
                this.sessionStats[key] += value;
            }
        });
        
        // 检查成就
        this.checkAchievements(stats);
        
        // 保存数据
        this.savePlayerData();
        
        console.log('📊 玩家统计已更新');
    }

    /**
     * 更新关卡进度
     * @param {string} levelId - 关卡ID
     * @param {Object} progress - 进度数据
     */
    updateLevelProgress(levelId, progress) {
        if (!this.currentPlayer) {
            console.warn('⚠️ 没有当前玩家，无法更新关卡进度');
            return;
        }
        
        // 更新关卡进度
        this.currentPlayer.levelProgress[levelId] = {
            ...this.currentPlayer.levelProgress[levelId],
            ...progress,
            lastPlayedAt: new Date().toISOString()
        };
        
        // 如果是新完成的关卡，更新统计
        if (progress.completed && !this.currentPlayer.levelProgress[levelId]?.completed) {
            this.updatePlayerStats({ levelsCompleted: 1 });
        }
        
        // 保存数据
        this.savePlayerData();
        
        console.log(`📈 关卡进度已更新: ${levelId}`);
    }

    /**
     * 检查并解锁成就
     * @param {Object} stats - 当前统计数据
     */
    checkAchievements(stats) {
        if (!this.currentPlayer) return;
        
        const playerStats = this.currentPlayer.stats;
        const newAchievements = [];
        
        this.achievements.forEach((achievement, achievementId) => {
            // 如果已经解锁，跳过
            if (this.currentPlayer.achievements[achievementId]) {
                return;
            }
            
            let unlocked = false;
            
            // 检查成就条件
            switch (achievement.type) {
                case 'progress':
                    unlocked = this.checkProgressAchievement(achievement, playerStats);
                    break;
                case 'performance':
                    unlocked = this.checkPerformanceAchievement(achievement, playerStats, stats);
                    break;
                case 'creative':
                    unlocked = this.checkCreativeAchievement(achievement, playerStats);
                    break;
                case 'social':
                    unlocked = this.checkSocialAchievement(achievement, playerStats);
                    break;
            }
            
            if (unlocked) {
                this.unlockAchievement(achievementId);
                newAchievements.push(achievement);
            }
        });
        
        // 如果有新成就，显示通知
        if (newAchievements.length > 0) {
            this.showAchievementNotifications(newAchievements);
        }
    }

    /**
     * 检查进度类成就
     * @param {Object} achievement - 成就定义
     * @param {Object} playerStats - 玩家统计
     * @returns {boolean} 是否解锁
     */
    checkProgressAchievement(achievement, playerStats) {
        const condition = achievement.condition;
        
        if (condition.levelsCompleted) {
            return playerStats.levelsCompleted >= condition.levelsCompleted;
        }
        
        if (condition.allBuiltinLevelsCompleted) {
            // 检查是否完成所有内置关卡
            if (window.levelManager) {
                const builtinLevels = levelManager.getLevelList(false);
                return builtinLevels.every(level => 
                    this.currentPlayer.levelProgress[level.id]?.completed
                );
            }
        }
        
        return false;
    }

    /**
     * 检查表现类成就
     * @param {Object} achievement - 成就定义
     * @param {Object} playerStats - 玩家统计
     * @param {Object} currentStats - 当前游戏统计
     * @returns {boolean} 是否解锁
     */
    checkPerformanceAchievement(achievement, playerStats, currentStats) {
        const condition = achievement.condition;
        
        if (condition.maxCombo) {
            return Math.max(playerStats.bestCombo || 0, currentStats.maxCombo || 0) >= condition.maxCombo;
        }
        
        if (condition.chainReactionsInLevel) {
            return (currentStats.chainReactions || 0) >= condition.chainReactionsInLevel;
        }
        
        if (condition.levelTimeUnder) {
            return (currentStats.levelTime || Infinity) <= condition.levelTimeUnder;
        }
        
        if (condition.accuracy) {
            return (currentStats.accuracy || 0) >= condition.accuracy;
        }
        
        return false;
    }

    /**
     * 检查创意类成就
     * @param {Object} achievement - 成就定义
     * @param {Object} playerStats - 玩家统计
     * @returns {boolean} 是否解锁
     */
    checkCreativeAchievement(achievement, playerStats) {
        const condition = achievement.condition;
        
        if (condition.customLevelsCreated) {
            return playerStats.customLevelsCreated >= condition.customLevelsCreated;
        }
        
        return false;
    }

    /**
     * 检查社交类成就
     * @param {Object} achievement - 成就定义
     * @param {Object} playerStats - 玩家统计
     * @returns {boolean} 是否解锁
     */
    checkSocialAchievement(achievement, playerStats) {
        const condition = achievement.condition;
        
        if (condition.communityLevelsPlayed) {
            return playerStats.communityLevelsPlayed >= condition.communityLevelsPlayed;
        }
        
        return false;
    }

    /**
     * 解锁成就
     * @param {string} achievementId - 成就ID
     */
    unlockAchievement(achievementId) {
        if (!this.currentPlayer) return;
        
        const achievement = this.achievements.get(achievementId);
        if (!achievement) {
            console.error('❌ 成就不存在:', achievementId);
            return;
        }
        
        // 记录解锁时间
        this.currentPlayer.achievements[achievementId] = {
            unlockedAt: new Date().toISOString(),
            points: achievement.points
        };
        
        // 更新成就统计
        this.updatePlayerStats({ achievementsUnlocked: 1 });
        
        console.log(`🏆 成就解锁: ${achievement.name}`);
    }

    /**
     * 显示成就通知
     * @param {Array} achievements - 新解锁的成就列表
     */
    showAchievementNotifications(achievements) {
        achievements.forEach((achievement, index) => {
            setTimeout(() => {
                if (window.uiManager) {
                    uiManager.showAchievementToast(achievement);
                }
            }, index * 1000); // 每个成就间隔1秒显示
        });
    }

    /**
     * 保存玩家数据
     */
    savePlayerData() {
        if (!window.storageService) {
            console.warn('⚠️ 存储服务不可用，无法保存玩家数据');
            return;
        }
        
        // 保存玩家列表
        const playersData = {};
        this.players.forEach((player, playerId) => {
            playersData[playerId] = player;
        });
        
        storageService.set('players', playersData);
        
        console.log('💾 玩家数据已保存');
    }

    /**
     * 加载排行榜数据
     */
    loadLeaderboards() {
        if (!window.storageService) return;
        
        const leaderboardData = storageService.get('leaderboards') || {};
        this.leaderboards = {
            highScores: leaderboardData.highScores || [],
            fastestTimes: leaderboardData.fastestTimes || [],
            longestCombos: leaderboardData.longestCombos || [],
            mostChainReactions: leaderboardData.mostChainReactions || []
        };
        
        console.log('🏆 排行榜数据已加载');
    }

    /**
     * 更新排行榜
     * @param {string} category - 排行榜类别
     * @param {Object} entry - 排行榜条目
     */
    updateLeaderboard(category, entry) {
        if (!this.leaderboards[category]) {
            console.error('❌ 排行榜类别不存在:', category);
            return;
        }
        
        // 添加玩家信息
        const leaderboardEntry = {
            ...entry,
            playerId: this.currentPlayer?.id,
            playerName: this.currentPlayer?.name,
            timestamp: new Date().toISOString()
        };
        
        // 添加到排行榜
        this.leaderboards[category].push(leaderboardEntry);
        
        // 排序并限制条目数量
        this.sortAndLimitLeaderboard(category);
        
        // 保存排行榜数据
        this.saveLeaderboards();
        
        console.log(`🏆 排行榜已更新: ${category}`);
    }

    /**
     * 排序并限制排行榜条目
     * @param {string} category - 排行榜类别
     */
    sortAndLimitLeaderboard(category) {
        const leaderboard = this.leaderboards[category];
        
        // 根据类别进行排序
        switch (category) {
            case 'highScores':
                leaderboard.sort((a, b) => b.score - a.score);
                break;
            case 'fastestTimes':
                leaderboard.sort((a, b) => a.time - b.time);
                break;
            case 'longestCombos':
                leaderboard.sort((a, b) => b.combo - a.combo);
                break;
            case 'mostChainReactions':
                leaderboard.sort((a, b) => b.chainReactions - a.chainReactions);
                break;
        }
        
        // 限制为前100名
        this.leaderboards[category] = leaderboard.slice(0, 100);
    }

    /**
     * 保存排行榜数据
     */
    saveLeaderboards() {
        if (!window.storageService) return;
        
        storageService.set('leaderboards', this.leaderboards);
    }

    /**
     * 获取排行榜数据
     * @param {string} category - 排行榜类别
     * @param {number} limit - 返回条目数量限制
     * @returns {Array} 排行榜数据
     */
    getLeaderboard(category, limit = 10) {
        const leaderboard = this.leaderboards[category] || [];
        return leaderboard.slice(0, limit);
    }

    /**
     * 获取玩家在排行榜中的排名
     * @param {string} category - 排行榜类别
     * @param {string} playerId - 玩家ID
     * @returns {number} 排名（从1开始，-1表示未上榜）
     */
    getPlayerRank(category, playerId = null) {
        const targetPlayerId = playerId || this.currentPlayer?.id;
        if (!targetPlayerId) return -1;
        
        const leaderboard = this.leaderboards[category] || [];
        const index = leaderboard.findIndex(entry => entry.playerId === targetPlayerId);
        
        return index >= 0 ? index + 1 : -1;
    }

    /**
     * 获取会话统计
     * @returns {Object} 会话统计数据
     */
    getSessionStats() {
        return {
            ...this.sessionStats,
            sessionDuration: Date.now() - this.sessionStats.startTime
        };
    }

    /**
     * 重置会话统计
     */
    resetSessionStats() {
        this.sessionStats = {
            startTime: Date.now(),
            levelsPlayed: 0,
            totalScore: 0,
            totalTime: 0,
            particlesActivated: 0,
            chainReactions: 0,
            maxCombo: 0
        };
        
        console.log('📊 会话统计已重置');
    }

    /**
     * 销毁玩家系统
     */
    destroy() {
        // 保存最终数据
        this.savePlayerData();
        this.saveLeaderboards();
        
        // 清理数据
        this.players.clear();
        this.achievements.clear();
        this.currentPlayer = null;
        this.isInitialized = false;
        
        console.log('👤 玩家系统已销毁');
    }
}

// 创建全局玩家系统实例
window.playerManager = new PlayerManager();
