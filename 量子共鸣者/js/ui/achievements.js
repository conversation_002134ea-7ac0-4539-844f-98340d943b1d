/**
 * 量子共鸣者 - 成就系统UI
 * 负责成就展示、通知、进度追踪等界面功能
 */

class AchievementsUI {
    constructor() {
        this.isInitialized = false;
        this.isVisible = false;
        
        // 元素引用
        this.elements = {
            container: null,
            achievementsList: null,
            progressBar: null,
            statsPanel: null
        };
        
        // 成就分类
        this.categories = {
            'progress': '进度成就',
            'performance': '表现成就',
            'creative': '创意成就',
            'social': '社交成就'
        };
        
        // 通知队列
        this.notificationQueue = [];
        this.isShowingNotification = false;
        
        console.log('🏆 成就系统UI已创建');
    }

    /**
     * 初始化成就UI
     */
    init() {
        try {
            // 获取容器元素
            this.getElements();
            
            // 创建成就界面结构
            this.createAchievementsStructure();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('✅ 成就系统UI初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 成就系统UI初始化失败:', error);
            return false;
        }
    }

    /**
     * 获取元素引用
     */
    getElements() {
        this.elements.container = document.getElementById('achievementsScreen') || 
                                 document.querySelector('.achievements-screen');
    }

    /**
     * 创建成就界面结构
     */
    createAchievementsStructure() {
        if (!this.elements.container) {
            console.warn('⚠️ 成就界面容器不存在，跳过结构创建');
            return;
        }

        this.elements.container.innerHTML = `
            <div class="achievements-overlay">
                <div class="achievements-content">
                    <div class="achievements-header">
                        <h1 class="achievements-title">成就系统</h1>
                        <button class="close-btn" id="closeAchievementsBtn">✕</button>
                    </div>
                    
                    <div class="achievements-summary">
                        <div class="achievement-progress">
                            <div class="progress-info">
                                <span class="progress-text">成就进度</span>
                                <span class="progress-count" id="achievementProgress">0 / 0</span>
                            </div>
                            <div class="progress-bar-container">
                                <div class="progress-bar" id="achievementProgressBar">
                                    <div class="progress-fill"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="achievement-stats">
                            <div class="stat-item">
                                <span class="stat-value" id="totalPoints">0</span>
                                <span class="stat-label">总积分</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value" id="recentAchievements">0</span>
                                <span class="stat-label">最近解锁</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="achievements-filters">
                        <button class="filter-btn active" data-category="all">全部</button>
                        <button class="filter-btn" data-category="progress">进度</button>
                        <button class="filter-btn" data-category="performance">表现</button>
                        <button class="filter-btn" data-category="creative">创意</button>
                        <button class="filter-btn" data-category="social">社交</button>
                        <button class="filter-btn" data-category="unlocked">已解锁</button>
                    </div>
                    
                    <div class="achievements-main">
                        <div class="achievements-list" id="achievementsList">
                            <!-- 成就列表将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 更新元素引用
        this.elements.achievementsList = document.getElementById('achievementsList');
        this.elements.progressBar = document.getElementById('achievementProgressBar');
        this.elements.statsPanel = document.querySelector('.achievement-stats');
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 关闭按钮
        const closeBtn = document.getElementById('closeAchievementsBtn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hide();
            });
        }
        
        // 分类过滤按钮
        const filterBtns = document.querySelectorAll('.filter-btn');
        filterBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                // 更新按钮状态
                filterBtns.forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                
                // 过滤成就
                const category = e.target.dataset.category;
                this.filterAchievements(category);
            });
        });
        
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (this.isVisible && e.key === 'Escape') {
                this.hide();
            }
        });
    }

    /**
     * 显示成就界面
     */
    show() {
        if (!this.isInitialized) {
            this.init();
        }
        
        // 显示容器
        if (this.elements.container) {
            this.elements.container.style.display = 'flex';
            this.elements.container.classList.add('screen-enter');
            
            setTimeout(() => {
                this.elements.container.classList.remove('screen-enter');
            }, 500);
        }
        
        // 更新成就显示
        this.updateAchievementsDisplay();
        
        this.isVisible = true;
        console.log('🏆 成就界面已显示');
    }

    /**
     * 隐藏成就界面
     */
    hide() {
        if (this.elements.container) {
            this.elements.container.classList.add('screen-exit');
            
            setTimeout(() => {
                this.elements.container.style.display = 'none';
                this.elements.container.classList.remove('screen-exit');
            }, 500);
        }
        
        this.isVisible = false;
        console.log('🏆 成就界面已隐藏');
    }

    /**
     * 更新成就显示
     */
    updateAchievementsDisplay() {
        if (!window.playerManager || !this.elements.achievementsList) return;
        
        const player = playerManager.getCurrentPlayer();
        if (!player) return;
        
        // 获取所有成就
        const allAchievements = Array.from(playerManager.achievements.values());
        const unlockedAchievements = Object.keys(player.achievements);
        
        // 更新进度信息
        this.updateProgressInfo(unlockedAchievements.length, allAchievements.length);
        
        // 更新统计信息
        this.updateStatsInfo(player, unlockedAchievements);
        
        // 生成成就列表
        this.generateAchievementsList(allAchievements, player.achievements);
    }

    /**
     * 更新进度信息
     * @param {number} unlocked - 已解锁数量
     * @param {number} total - 总数量
     */
    updateProgressInfo(unlocked, total) {
        const progressCount = document.getElementById('achievementProgress');
        const progressBar = this.elements.progressBar;
        
        if (progressCount) {
            progressCount.textContent = `${unlocked} / ${total}`;
        }
        
        if (progressBar) {
            const progressFill = progressBar.querySelector('.progress-fill');
            const percentage = total > 0 ? (unlocked / total) * 100 : 0;
            
            if (progressFill) {
                progressFill.style.width = `${percentage}%`;
            }
        }
    }

    /**
     * 更新统计信息
     * @param {Object} player - 玩家数据
     * @param {Array} unlockedAchievements - 已解锁成就列表
     */
    updateStatsInfo(player, unlockedAchievements) {
        const totalPointsEl = document.getElementById('totalPoints');
        const recentAchievementsEl = document.getElementById('recentAchievements');
        
        // 计算总积分
        let totalPoints = 0;
        unlockedAchievements.forEach(achievementId => {
            const achievement = playerManager.achievements.get(achievementId);
            if (achievement) {
                totalPoints += achievement.points;
            }
        });
        
        if (totalPointsEl) {
            totalPointsEl.textContent = totalPoints.toString();
        }
        
        // 计算最近解锁的成就（7天内）
        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        const recentCount = unlockedAchievements.filter(achievementId => {
            const unlockedAt = player.achievements[achievementId]?.unlockedAt;
            return unlockedAt && new Date(unlockedAt) > weekAgo;
        }).length;
        
        if (recentAchievementsEl) {
            recentAchievementsEl.textContent = recentCount.toString();
        }
    }

    /**
     * 生成成就列表
     * @param {Array} achievements - 成就列表
     * @param {Object} playerAchievements - 玩家已解锁成就
     */
    generateAchievementsList(achievements, playerAchievements) {
        if (!this.elements.achievementsList) return;
        
        // 按类型分组成就
        const groupedAchievements = {};
        achievements.forEach(achievement => {
            if (!groupedAchievements[achievement.type]) {
                groupedAchievements[achievement.type] = [];
            }
            groupedAchievements[achievement.type].push(achievement);
        });
        
        // 生成HTML
        let html = '';
        Object.entries(groupedAchievements).forEach(([type, typeAchievements]) => {
            html += `
                <div class="achievement-category" data-category="${type}">
                    <h3 class="category-title">${this.categories[type] || type}</h3>
                    <div class="category-achievements">
                        ${typeAchievements.map(achievement => 
                            this.generateAchievementHTML(achievement, playerAchievements[achievement.id])
                        ).join('')}
                    </div>
                </div>
            `;
        });
        
        this.elements.achievementsList.innerHTML = html;
    }

    /**
     * 生成单个成就的HTML
     * @param {Object} achievement - 成就数据
     * @param {Object} playerAchievement - 玩家成就数据
     * @returns {string} HTML字符串
     */
    generateAchievementHTML(achievement, playerAchievement) {
        const isUnlocked = !!playerAchievement;
        const unlockedDate = playerAchievement ? new Date(playerAchievement.unlockedAt).toLocaleDateString() : '';
        
        return `
            <div class="achievement-item ${isUnlocked ? 'unlocked' : 'locked'}" data-achievement-id="${achievement.id}">
                <div class="achievement-icon">
                    ${isUnlocked ? achievement.icon : '🔒'}
                </div>
                <div class="achievement-info">
                    <div class="achievement-name">${achievement.name}</div>
                    <div class="achievement-description">${achievement.description}</div>
                    <div class="achievement-meta">
                        <span class="achievement-points">${achievement.points} 积分</span>
                        ${isUnlocked ? `<span class="achievement-date">解锁于 ${unlockedDate}</span>` : ''}
                    </div>
                </div>
                <div class="achievement-status">
                    ${isUnlocked ? '<span class="status-unlocked">✓</span>' : '<span class="status-locked">🔒</span>'}
                </div>
            </div>
        `;
    }

    /**
     * 过滤成就显示
     * @param {string} category - 过滤类别
     */
    filterAchievements(category) {
        const achievementItems = document.querySelectorAll('.achievement-item');
        const categoryGroups = document.querySelectorAll('.achievement-category');
        
        if (category === 'all') {
            // 显示所有成就
            achievementItems.forEach(item => item.style.display = 'flex');
            categoryGroups.forEach(group => group.style.display = 'block');
        } else if (category === 'unlocked') {
            // 只显示已解锁的成就
            achievementItems.forEach(item => {
                item.style.display = item.classList.contains('unlocked') ? 'flex' : 'none';
            });
            categoryGroups.forEach(group => {
                const hasUnlocked = group.querySelectorAll('.achievement-item.unlocked').length > 0;
                group.style.display = hasUnlocked ? 'block' : 'none';
            });
        } else {
            // 按类别过滤
            categoryGroups.forEach(group => {
                group.style.display = group.dataset.category === category ? 'block' : 'none';
            });
        }
    }

    /**
     * 显示成就解锁通知
     * @param {Object} achievement - 成就数据
     */
    showAchievementToast(achievement) {
        // 添加到通知队列
        this.notificationQueue.push(achievement);
        
        // 如果没有正在显示通知，开始显示
        if (!this.isShowingNotification) {
            this.processNotificationQueue();
        }
    }

    /**
     * 处理通知队列
     */
    processNotificationQueue() {
        if (this.notificationQueue.length === 0) {
            this.isShowingNotification = false;
            return;
        }
        
        this.isShowingNotification = true;
        const achievement = this.notificationQueue.shift();
        
        // 创建通知元素
        const notification = this.createNotificationElement(achievement);
        document.body.appendChild(notification);
        
        // 显示动画
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        // 自动隐藏
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
                // 处理下一个通知
                this.processNotificationQueue();
            }, 500);
        }, 3000);
    }

    /**
     * 创建通知元素
     * @param {Object} achievement - 成就数据
     * @returns {HTMLElement} 通知元素
     */
    createNotificationElement(achievement) {
        const notification = document.createElement('div');
        notification.className = 'achievement-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">${achievement.icon}</div>
                <div class="notification-info">
                    <div class="notification-title">成就解锁！</div>
                    <div class="notification-name">${achievement.name}</div>
                    <div class="notification-description">${achievement.description}</div>
                    <div class="notification-points">+${achievement.points} 积分</div>
                </div>
            </div>
        `;
        
        return notification;
    }

    /**
     * 获取成就完成度统计
     * @returns {Object} 统计数据
     */
    getCompletionStats() {
        if (!window.playerManager) return null;
        
        const player = playerManager.getCurrentPlayer();
        if (!player) return null;
        
        const allAchievements = Array.from(playerManager.achievements.values());
        const unlockedAchievements = Object.keys(player.achievements);
        
        // 按类型统计
        const statsByType = {};
        Object.keys(this.categories).forEach(type => {
            const typeAchievements = allAchievements.filter(a => a.type === type);
            const typeUnlocked = unlockedAchievements.filter(id => {
                const achievement = playerManager.achievements.get(id);
                return achievement && achievement.type === type;
            });
            
            statsByType[type] = {
                total: typeAchievements.length,
                unlocked: typeUnlocked.length,
                percentage: typeAchievements.length > 0 ? (typeUnlocked.length / typeAchievements.length) * 100 : 0
            };
        });
        
        return {
            overall: {
                total: allAchievements.length,
                unlocked: unlockedAchievements.length,
                percentage: allAchievements.length > 0 ? (unlockedAchievements.length / allAchievements.length) * 100 : 0
            },
            byType: statsByType
        };
    }

    /**
     * 销毁成就UI
     */
    destroy() {
        // 清理通知队列
        this.notificationQueue = [];
        this.isShowingNotification = false;
        
        // 清理元素引用
        Object.keys(this.elements).forEach(key => {
            this.elements[key] = null;
        });
        
        this.isInitialized = false;
        
        console.log('🏆 成就系统UI已销毁');
    }
}

// 创建全局成就UI实例
window.achievementsUI = new AchievementsUI();
