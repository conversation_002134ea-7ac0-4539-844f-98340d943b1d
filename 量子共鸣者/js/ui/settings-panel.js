/**
 * 量子共鸣者 - 设置面板组件
 * 负责管理游戏设置界面，包括音频、图形、控制等设置
 */

class SettingsPanel {
    constructor() {
        this.isInitialized = false;
        this.isVisible = false;
        
        // 设置面板元素
        this.elements = {
            container: null,
            tabs: [],
            panels: [],
            saveButton: null,
            resetButton: null,
            cancelButton: null
        };
        
        // 当前设置
        this.currentSettings = {
            audio: {
                masterVolume: 0.8,
                musicVolume: 0.7,
                sfxVolume: 0.9,
                uiVolume: 0.6,
                enableAudio: true,
                audioTheme: 'quantum'
            },
            graphics: {
                quality: 'high',
                renderMode: '3d',
                particleCount: 'medium',
                enableEffects: true,
                enableGlow: true,
                frameRate: 60
            },
            controls: {
                mousesensitivity: 0.5,
                keyboardLayout: 'qwerty',
                enableTouch: true,
                enableMicrophone: false,
                microphoneSensitivity: 0.7
            },
            gameplay: {
                difficulty: 'normal',
                autoSave: true,
                showTutorial: true,
                enableHints: true,
                pauseOnFocusLoss: true
            },
            accessibility: {
                colorBlindMode: 'none',
                highContrast: false,
                largeText: false,
                reduceMotion: false,
                screenReader: false
            },
            language: 'zh-CN'
        };
        
        // 临时设置（用于取消时恢复）
        this.tempSettings = null;
        
        console.log('⚙️ 设置面板组件已创建');
    }

    /**
     * 初始化设置面板
     */
    init() {
        try {
            // 获取设置面板元素
            this.getSettingsElements();
            
            // 创建设置面板结构
            this.createSettingsStructure();
            
            // 加载当前设置
            this.loadSettings();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 初始化标签页
            this.initTabs();
            
            this.isInitialized = true;
            console.log('✅ 设置面板初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 设置面板初始化失败:', error);
            return false;
        }
    }

    /**
     * 获取设置面板元素
     */
    getSettingsElements() {
        this.elements.container = document.getElementById('settingsPanel') || 
                                 document.querySelector('.settings-panel');
    }

    /**
     * 创建设置面板结构
     */
    createSettingsStructure() {
        if (!this.elements.container) {
            console.warn('⚠️ 设置面板容器不存在，跳过结构创建');
            return;
        }

        this.elements.container.innerHTML = `
            <div class="settings-header">
                <h2>游戏设置</h2>
                <button class="settings-close" id="settingsClose">×</button>
            </div>
            
            <div class="settings-tabs">
                <button class="settings-tab active" data-tab="audio">音频</button>
                <button class="settings-tab" data-tab="graphics">图形</button>
                <button class="settings-tab" data-tab="controls">控制</button>
                <button class="settings-tab" data-tab="gameplay">游戏</button>
                <button class="settings-tab" data-tab="accessibility">辅助功能</button>
                <button class="settings-tab" data-tab="language">语言</button>
            </div>
            
            <div class="settings-content">
                ${this.createAudioPanel()}
                ${this.createGraphicsPanel()}
                ${this.createControlsPanel()}
                ${this.createGameplayPanel()}
                ${this.createAccessibilityPanel()}
                ${this.createLanguagePanel()}
            </div>
            
            <div class="settings-footer">
                <button class="settings-button secondary" id="settingsReset">重置默认</button>
                <div class="settings-actions">
                    <button class="settings-button secondary" id="settingsCancel">取消</button>
                    <button class="settings-button primary" id="settingsSave">保存</button>
                </div>
            </div>
        `;

        // 更新元素引用
        this.elements.tabs = this.elements.container.querySelectorAll('.settings-tab');
        this.elements.panels = this.elements.container.querySelectorAll('.settings-panel');
        this.elements.saveButton = document.getElementById('settingsSave');
        this.elements.resetButton = document.getElementById('settingsReset');
        this.elements.cancelButton = document.getElementById('settingsCancel');
    }

    /**
     * 创建音频设置面板
     */
    createAudioPanel() {
        return `
            <div class="settings-panel active" data-panel="audio">
                <div class="settings-group">
                    <h3>音量设置</h3>
                    <div class="settings-item">
                        <label>主音量</label>
                        <div class="slider-container">
                            <input type="range" id="masterVolume" min="0" max="100" value="80" class="quantum-slider">
                            <span class="slider-value">80%</span>
                        </div>
                    </div>
                    <div class="settings-item">
                        <label>音乐音量</label>
                        <div class="slider-container">
                            <input type="range" id="musicVolume" min="0" max="100" value="70" class="quantum-slider">
                            <span class="slider-value">70%</span>
                        </div>
                    </div>
                    <div class="settings-item">
                        <label>音效音量</label>
                        <div class="slider-container">
                            <input type="range" id="sfxVolume" min="0" max="100" value="90" class="quantum-slider">
                            <span class="slider-value">90%</span>
                        </div>
                    </div>
                    <div class="settings-item">
                        <label>界面音量</label>
                        <div class="slider-container">
                            <input type="range" id="uiVolume" min="0" max="100" value="60" class="quantum-slider">
                            <span class="slider-value">60%</span>
                        </div>
                    </div>
                </div>
                
                <div class="settings-group">
                    <h3>音频选项</h3>
                    <div class="settings-item">
                        <label>启用音频</label>
                        <input type="checkbox" id="enableAudio" checked class="quantum-checkbox">
                    </div>
                    <div class="settings-item">
                        <label>音频主题</label>
                        <select id="audioTheme" class="quantum-select">
                            <option value="quantum">量子</option>
                            <option value="space">太空</option>
                            <option value="energy">能量</option>
                        </select>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建图形设置面板
     */
    createGraphicsPanel() {
        return `
            <div class="settings-panel" data-panel="graphics">
                <div class="settings-group">
                    <h3>渲染设置</h3>
                    <div class="settings-item">
                        <label>图形质量</label>
                        <select id="quality" class="quantum-select">
                            <option value="low">低</option>
                            <option value="medium">中</option>
                            <option value="high">高</option>
                            <option value="ultra">超高</option>
                        </select>
                    </div>
                    <div class="settings-item">
                        <label>渲染模式</label>
                        <select id="renderMode" class="quantum-select">
                            <option value="2d">2D模式</option>
                            <option value="3d">3D模式</option>
                            <option value="auto">自动</option>
                        </select>
                    </div>
                    <div class="settings-item">
                        <label>粒子数量</label>
                        <select id="particleCount" class="quantum-select">
                            <option value="low">少</option>
                            <option value="medium">中</option>
                            <option value="high">多</option>
                            <option value="ultra">极多</option>
                        </select>
                    </div>
                    <div class="settings-item">
                        <label>目标帧率</label>
                        <select id="frameRate" class="quantum-select">
                            <option value="30">30 FPS</option>
                            <option value="60">60 FPS</option>
                            <option value="120">120 FPS</option>
                            <option value="unlimited">不限制</option>
                        </select>
                    </div>
                </div>
                
                <div class="settings-group">
                    <h3>视觉效果</h3>
                    <div class="settings-item">
                        <label>启用特效</label>
                        <input type="checkbox" id="enableEffects" checked class="quantum-checkbox">
                    </div>
                    <div class="settings-item">
                        <label>启用发光</label>
                        <input type="checkbox" id="enableGlow" checked class="quantum-checkbox">
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建控制设置面板
     */
    createControlsPanel() {
        return `
            <div class="settings-panel" data-panel="controls">
                <div class="settings-group">
                    <h3>鼠标设置</h3>
                    <div class="settings-item">
                        <label>鼠标灵敏度</label>
                        <div class="slider-container">
                            <input type="range" id="mouseSensitivity" min="0.1" max="2.0" step="0.1" value="0.5" class="quantum-slider">
                            <span class="slider-value">0.5</span>
                        </div>
                    </div>
                </div>
                
                <div class="settings-group">
                    <h3>键盘设置</h3>
                    <div class="settings-item">
                        <label>键盘布局</label>
                        <select id="keyboardLayout" class="quantum-select">
                            <option value="qwerty">QWERTY</option>
                            <option value="azerty">AZERTY</option>
                            <option value="dvorak">Dvorak</option>
                        </select>
                    </div>
                </div>
                
                <div class="settings-group">
                    <h3>输入选项</h3>
                    <div class="settings-item">
                        <label>启用触摸控制</label>
                        <input type="checkbox" id="enableTouch" checked class="quantum-checkbox">
                    </div>
                    <div class="settings-item">
                        <label>启用麦克风输入</label>
                        <input type="checkbox" id="enableMicrophone" class="quantum-checkbox">
                    </div>
                    <div class="settings-item">
                        <label>麦克风灵敏度</label>
                        <div class="slider-container">
                            <input type="range" id="microphoneSensitivity" min="0.1" max="1.0" step="0.1" value="0.7" class="quantum-slider">
                            <span class="slider-value">0.7</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建游戏设置面板
     */
    createGameplayPanel() {
        return `
            <div class="settings-panel" data-panel="gameplay">
                <div class="settings-group">
                    <h3>游戏选项</h3>
                    <div class="settings-item">
                        <label>难度等级</label>
                        <select id="difficulty" class="quantum-select">
                            <option value="easy">简单</option>
                            <option value="normal">普通</option>
                            <option value="hard">困难</option>
                            <option value="expert">专家</option>
                        </select>
                    </div>
                    <div class="settings-item">
                        <label>自动保存</label>
                        <input type="checkbox" id="autoSave" checked class="quantum-checkbox">
                    </div>
                    <div class="settings-item">
                        <label>显示教程</label>
                        <input type="checkbox" id="showTutorial" checked class="quantum-checkbox">
                    </div>
                    <div class="settings-item">
                        <label>启用提示</label>
                        <input type="checkbox" id="enableHints" checked class="quantum-checkbox">
                    </div>
                    <div class="settings-item">
                        <label>失去焦点时暂停</label>
                        <input type="checkbox" id="pauseOnFocusLoss" checked class="quantum-checkbox">
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建辅助功能面板
     */
    createAccessibilityPanel() {
        return `
            <div class="settings-panel" data-panel="accessibility">
                <div class="settings-group">
                    <h3>视觉辅助</h3>
                    <div class="settings-item">
                        <label>色盲模式</label>
                        <select id="colorBlindMode" class="quantum-select">
                            <option value="none">无</option>
                            <option value="protanopia">红色盲</option>
                            <option value="deuteranopia">绿色盲</option>
                            <option value="tritanopia">蓝色盲</option>
                        </select>
                    </div>
                    <div class="settings-item">
                        <label>高对比度</label>
                        <input type="checkbox" id="highContrast" class="quantum-checkbox">
                    </div>
                    <div class="settings-item">
                        <label>大字体</label>
                        <input type="checkbox" id="largeText" class="quantum-checkbox">
                    </div>
                    <div class="settings-item">
                        <label>减少动画</label>
                        <input type="checkbox" id="reduceMotion" class="quantum-checkbox">
                    </div>
                </div>
                
                <div class="settings-group">
                    <h3>其他辅助</h3>
                    <div class="settings-item">
                        <label>屏幕阅读器支持</label>
                        <input type="checkbox" id="screenReader" class="quantum-checkbox">
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建语言设置面板
     */
    createLanguagePanel() {
        return `
            <div class="settings-panel" data-panel="language">
                <div class="settings-group">
                    <h3>语言选择</h3>
                    <div class="settings-item">
                        <label>界面语言</label>
                        <select id="language" class="quantum-select">
                            <option value="zh-CN">简体中文</option>
                            <option value="zh-TW">繁體中文</option>
                            <option value="en-US">English</option>
                            <option value="ja-JP">日本語</option>
                            <option value="ko-KR">한국어</option>
                        </select>
                    </div>
                </div>
                
                <div class="settings-group">
                    <h3>语言说明</h3>
                    <p class="settings-description">
                        更改语言设置后需要重新启动游戏才能生效。
                    </p>
                </div>
            </div>
        `;
    }

    /**
     * 加载设置
     */
    loadSettings() {
        if (window.storageService) {
            const savedSettings = storageService.getSettings();
            if (savedSettings) {
                this.currentSettings = { ...this.currentSettings, ...savedSettings };
            }
        }
        
        this.applySettingsToUI();
    }

    /**
     * 应用设置到UI
     */
    applySettingsToUI() {
        // 音频设置
        this.setSliderValue('masterVolume', this.currentSettings.audio.masterVolume * 100);
        this.setSliderValue('musicVolume', this.currentSettings.audio.musicVolume * 100);
        this.setSliderValue('sfxVolume', this.currentSettings.audio.sfxVolume * 100);
        this.setSliderValue('uiVolume', this.currentSettings.audio.uiVolume * 100);
        this.setCheckboxValue('enableAudio', this.currentSettings.audio.enableAudio);
        this.setSelectValue('audioTheme', this.currentSettings.audio.audioTheme);
        
        // 图形设置
        this.setSelectValue('quality', this.currentSettings.graphics.quality);
        this.setSelectValue('renderMode', this.currentSettings.graphics.renderMode);
        this.setSelectValue('particleCount', this.currentSettings.graphics.particleCount);
        this.setSelectValue('frameRate', this.currentSettings.graphics.frameRate);
        this.setCheckboxValue('enableEffects', this.currentSettings.graphics.enableEffects);
        this.setCheckboxValue('enableGlow', this.currentSettings.graphics.enableGlow);
        
        // 控制设置
        this.setSliderValue('mouseSensitivity', this.currentSettings.controls.mousesensitivity);
        this.setSelectValue('keyboardLayout', this.currentSettings.controls.keyboardLayout);
        this.setCheckboxValue('enableTouch', this.currentSettings.controls.enableTouch);
        this.setCheckboxValue('enableMicrophone', this.currentSettings.controls.enableMicrophone);
        this.setSliderValue('microphoneSensitivity', this.currentSettings.controls.microphoneSensitivity);
        
        // 游戏设置
        this.setSelectValue('difficulty', this.currentSettings.gameplay.difficulty);
        this.setCheckboxValue('autoSave', this.currentSettings.gameplay.autoSave);
        this.setCheckboxValue('showTutorial', this.currentSettings.gameplay.showTutorial);
        this.setCheckboxValue('enableHints', this.currentSettings.gameplay.enableHints);
        this.setCheckboxValue('pauseOnFocusLoss', this.currentSettings.gameplay.pauseOnFocusLoss);
        
        // 辅助功能设置
        this.setSelectValue('colorBlindMode', this.currentSettings.accessibility.colorBlindMode);
        this.setCheckboxValue('highContrast', this.currentSettings.accessibility.highContrast);
        this.setCheckboxValue('largeText', this.currentSettings.accessibility.largeText);
        this.setCheckboxValue('reduceMotion', this.currentSettings.accessibility.reduceMotion);
        this.setCheckboxValue('screenReader', this.currentSettings.accessibility.screenReader);
        
        // 语言设置
        this.setSelectValue('language', this.currentSettings.language);
    }

    /**
     * 设置滑块值
     * @param {string} id - 元素ID
     * @param {number} value - 值
     */
    setSliderValue(id, value) {
        const slider = document.getElementById(id);
        const valueDisplay = slider?.parentNode.querySelector('.slider-value');
        
        if (slider) {
            slider.value = value;
            if (valueDisplay) {
                if (id.includes('Volume')) {
                    valueDisplay.textContent = `${Math.round(value)}%`;
                } else {
                    valueDisplay.textContent = value.toString();
                }
            }
        }
    }

    /**
     * 设置复选框值
     * @param {string} id - 元素ID
     * @param {boolean} value - 值
     */
    setCheckboxValue(id, value) {
        const checkbox = document.getElementById(id);
        if (checkbox) {
            checkbox.checked = value;
        }
    }

    /**
     * 设置选择框值
     * @param {string} id - 元素ID
     * @param {string} value - 值
     */
    setSelectValue(id, value) {
        const select = document.getElementById(id);
        if (select) {
            select.value = value;
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 标签页切换
        this.elements.tabs.forEach(tab => {
            tab.addEventListener('click', () => this.switchTab(tab.dataset.tab));
        });
        
        // 滑块事件
        const sliders = this.elements.container.querySelectorAll('.quantum-slider');
        sliders.forEach(slider => {
            slider.addEventListener('input', (e) => this.onSliderChange(e));
        });
        
        // 复选框事件
        const checkboxes = this.elements.container.querySelectorAll('.quantum-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (e) => this.onCheckboxChange(e));
        });
        
        // 选择框事件
        const selects = this.elements.container.querySelectorAll('.quantum-select');
        selects.forEach(select => {
            select.addEventListener('change', (e) => this.onSelectChange(e));
        });
        
        // 按钮事件
        if (this.elements.saveButton) {
            this.elements.saveButton.addEventListener('click', () => this.saveSettings());
        }
        
        if (this.elements.resetButton) {
            this.elements.resetButton.addEventListener('click', () => this.resetSettings());
        }
        
        if (this.elements.cancelButton) {
            this.elements.cancelButton.addEventListener('click', () => this.cancelSettings());
        }
        
        // 关闭按钮
        const closeButton = document.getElementById('settingsClose');
        if (closeButton) {
            closeButton.addEventListener('click', () => this.hide());
        }
    }

    /**
     * 初始化标签页
     */
    initTabs() {
        // 显示第一个标签页
        this.switchTab('audio');
    }

    /**
     * 切换标签页
     * @param {string} tabName - 标签页名称
     */
    switchTab(tabName) {
        // 更新标签页状态
        this.elements.tabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabName);
        });
        
        // 更新面板显示
        this.elements.panels.forEach(panel => {
            panel.classList.toggle('active', panel.dataset.panel === tabName);
        });
    }

    /**
     * 滑块变化事件
     * @param {Event} event - 事件对象
     */
    onSliderChange(event) {
        const slider = event.target;
        const valueDisplay = slider.parentNode.querySelector('.slider-value');
        
        if (valueDisplay) {
            if (slider.id.includes('Volume')) {
                valueDisplay.textContent = `${Math.round(slider.value)}%`;
            } else {
                valueDisplay.textContent = slider.value;
            }
        }
        
        // 实时预览音量变化
        if (slider.id.includes('Volume') && window.audioManager) {
            const volumeType = slider.id.replace('Volume', '');
            const volume = slider.value / 100;
            audioManager.setVolume(volumeType, volume);
        }
    }

    /**
     * 复选框变化事件
     * @param {Event} event - 事件对象
     */
    onCheckboxChange(event) {
        const checkbox = event.target;
        
        // 实时预览某些设置
        if (checkbox.id === 'enableAudio' && window.audioManager) {
            audioManager.setEnabled(checkbox.checked);
        }
    }

    /**
     * 选择框变化事件
     * @param {Event} event - 事件对象
     */
    onSelectChange(event) {
        const select = event.target;
        
        // 实时预览某些设置
        if (select.id === 'audioTheme' && window.audioManager) {
            audioManager.setTheme(select.value);
        }
    }

    /**
     * 保存设置
     */
    saveSettings() {
        // 收集当前UI中的设置
        this.collectSettingsFromUI();
        
        // 保存到存储
        if (window.storageService) {
            storageService.saveSettings(this.currentSettings);
        }
        
        // 应用设置到游戏系统
        this.applySettingsToGame();
        
        // 显示保存成功消息
        this.showMessage('设置已保存', 'success');
        
        // 隐藏设置面板
        this.hide();
    }

    /**
     * 从UI收集设置
     */
    collectSettingsFromUI() {
        // 音频设置
        this.currentSettings.audio.masterVolume = this.getSliderValue('masterVolume') / 100;
        this.currentSettings.audio.musicVolume = this.getSliderValue('musicVolume') / 100;
        this.currentSettings.audio.sfxVolume = this.getSliderValue('sfxVolume') / 100;
        this.currentSettings.audio.uiVolume = this.getSliderValue('uiVolume') / 100;
        this.currentSettings.audio.enableAudio = this.getCheckboxValue('enableAudio');
        this.currentSettings.audio.audioTheme = this.getSelectValue('audioTheme');
        
        // 图形设置
        this.currentSettings.graphics.quality = this.getSelectValue('quality');
        this.currentSettings.graphics.renderMode = this.getSelectValue('renderMode');
        this.currentSettings.graphics.particleCount = this.getSelectValue('particleCount');
        this.currentSettings.graphics.frameRate = parseInt(this.getSelectValue('frameRate'));
        this.currentSettings.graphics.enableEffects = this.getCheckboxValue('enableEffects');
        this.currentSettings.graphics.enableGlow = this.getCheckboxValue('enableGlow');
        
        // 控制设置
        this.currentSettings.controls.mousesensitivity = parseFloat(this.getSliderValue('mouseSensitivity'));
        this.currentSettings.controls.keyboardLayout = this.getSelectValue('keyboardLayout');
        this.currentSettings.controls.enableTouch = this.getCheckboxValue('enableTouch');
        this.currentSettings.controls.enableMicrophone = this.getCheckboxValue('enableMicrophone');
        this.currentSettings.controls.microphoneSensitivity = parseFloat(this.getSliderValue('microphoneSensitivity'));
        
        // 游戏设置
        this.currentSettings.gameplay.difficulty = this.getSelectValue('difficulty');
        this.currentSettings.gameplay.autoSave = this.getCheckboxValue('autoSave');
        this.currentSettings.gameplay.showTutorial = this.getCheckboxValue('showTutorial');
        this.currentSettings.gameplay.enableHints = this.getCheckboxValue('enableHints');
        this.currentSettings.gameplay.pauseOnFocusLoss = this.getCheckboxValue('pauseOnFocusLoss');
        
        // 辅助功能设置
        this.currentSettings.accessibility.colorBlindMode = this.getSelectValue('colorBlindMode');
        this.currentSettings.accessibility.highContrast = this.getCheckboxValue('highContrast');
        this.currentSettings.accessibility.largeText = this.getCheckboxValue('largeText');
        this.currentSettings.accessibility.reduceMotion = this.getCheckboxValue('reduceMotion');
        this.currentSettings.accessibility.screenReader = this.getCheckboxValue('screenReader');
        
        // 语言设置
        this.currentSettings.language = this.getSelectValue('language');
    }

    /**
     * 获取滑块值
     * @param {string} id - 元素ID
     * @returns {number} 值
     */
    getSliderValue(id) {
        const slider = document.getElementById(id);
        return slider ? parseFloat(slider.value) : 0;
    }

    /**
     * 获取复选框值
     * @param {string} id - 元素ID
     * @returns {boolean} 值
     */
    getCheckboxValue(id) {
        const checkbox = document.getElementById(id);
        return checkbox ? checkbox.checked : false;
    }

    /**
     * 获取选择框值
     * @param {string} id - 元素ID
     * @returns {string} 值
     */
    getSelectValue(id) {
        const select = document.getElementById(id);
        return select ? select.value : '';
    }

    /**
     * 应用设置到游戏系统
     */
    applySettingsToGame() {
        // 应用音频设置
        if (window.audioManager) {
            audioManager.applySettings(this.currentSettings.audio);
        }
        
        // 应用图形设置
        if (window.renderEngine) {
            renderEngine.applySettings(this.currentSettings.graphics);
        }
        
        // 应用控制设置
        if (window.inputManager) {
            inputManager.applySettings(this.currentSettings.controls);
        }
        
        // 应用语言设置
        if (window.i18n && this.currentSettings.language !== i18n.getCurrentLanguage()) {
            i18n.setLanguage(this.currentSettings.language);
        }
        
        // 应用辅助功能设置
        this.applyAccessibilitySettings();
    }

    /**
     * 应用辅助功能设置
     */
    applyAccessibilitySettings() {
        const root = document.documentElement;
        
        // 高对比度
        root.classList.toggle('high-contrast', this.currentSettings.accessibility.highContrast);
        
        // 大字体
        root.classList.toggle('large-text', this.currentSettings.accessibility.largeText);
        
        // 减少动画
        root.classList.toggle('reduce-motion', this.currentSettings.accessibility.reduceMotion);
        
        // 色盲模式
        root.className = root.className.replace(/colorblind-\w+/g, '');
        if (this.currentSettings.accessibility.colorBlindMode !== 'none') {
            root.classList.add(`colorblind-${this.currentSettings.accessibility.colorBlindMode}`);
        }
    }

    /**
     * 重置设置
     */
    resetSettings() {
        if (window.uiManager) {
            uiManager.showModal('confirmModal', {
                title: '重置设置',
                content: '确定要重置所有设置为默认值吗？此操作无法撤销。',
                onConfirm: () => {
                    this.doResetSettings();
                }
            });
        }
    }

    /**
     * 执行重置设置
     */
    doResetSettings() {
        // 重置为默认设置
        this.currentSettings = {
            audio: {
                masterVolume: 0.8,
                musicVolume: 0.7,
                sfxVolume: 0.9,
                uiVolume: 0.6,
                enableAudio: true,
                audioTheme: 'quantum'
            },
            graphics: {
                quality: 'high',
                renderMode: '3d',
                particleCount: 'medium',
                enableEffects: true,
                enableGlow: true,
                frameRate: 60
            },
            controls: {
                mouseSensitivity: 0.5,
                keyboardLayout: 'qwerty',
                enableTouch: true,
                enableMicrophone: false,
                microphoneSensitivity: 0.7
            },
            gameplay: {
                difficulty: 'normal',
                autoSave: true,
                showTutorial: true,
                enableHints: true,
                pauseOnFocusLoss: true
            },
            accessibility: {
                colorBlindMode: 'none',
                highContrast: false,
                largeText: false,
                reduceMotion: false,
                screenReader: false
            },
            language: 'zh-CN'
        };
        
        // 更新UI
        this.applySettingsToUI();
        
        // 显示重置成功消息
        this.showMessage('设置已重置为默认值', 'success');
    }

    /**
     * 取消设置
     */
    cancelSettings() {
        // 恢复临时设置
        if (this.tempSettings) {
            this.currentSettings = { ...this.tempSettings };
            this.applySettingsToUI();
            this.applySettingsToGame();
        }
        
        this.hide();
    }

    /**
     * 显示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型
     */
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageElement = document.createElement('div');
        messageElement.className = `settings-message ${type}`;
        messageElement.textContent = message;
        
        // 添加到设置面板
        this.elements.container.appendChild(messageElement);
        
        // 自动移除
        setTimeout(() => {
            if (messageElement.parentNode) {
                messageElement.parentNode.removeChild(messageElement);
            }
        }, 3000);
    }

    /**
     * 显示设置面板
     */
    show() {
        if (!this.isInitialized) {
            this.init();
        }
        
        // 保存当前设置作为临时设置
        this.tempSettings = { ...this.currentSettings };
        
        // 显示面板
        if (this.elements.container) {
            this.elements.container.style.display = 'flex';
            this.elements.container.classList.add('settings-enter');
            
            setTimeout(() => {
                this.elements.container.classList.remove('settings-enter');
            }, 300);
        }
        
        this.isVisible = true;
    }

    /**
     * 隐藏设置面板
     */
    hide() {
        if (this.elements.container) {
            this.elements.container.classList.add('settings-exit');
            
            setTimeout(() => {
                this.elements.container.style.display = 'none';
                this.elements.container.classList.remove('settings-exit');
            }, 300);
        }
        
        this.isVisible = false;
        
        // 返回上一个屏幕
        if (window.uiManager) {
            uiManager.showScreen('mainMenuScreen');
        }
    }

    /**
     * 销毁设置面板
     */
    destroy() {
        // 清理元素引用
        Object.keys(this.elements).forEach(key => {
            this.elements[key] = null;
        });
        
        this.isInitialized = false;
        console.log('⚙️ 设置面板已销毁');
    }
}

// 创建全局设置面板实例
window.settingsPanel = new SettingsPanel();
