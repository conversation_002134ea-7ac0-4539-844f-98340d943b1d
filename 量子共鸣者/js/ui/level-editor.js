/**
 * 量子共鸣者 - 关卡编辑器
 * 负责关卡的创建、编辑和测试功能
 */

class LevelEditor {
    constructor() {
        this.isInitialized = false;
        this.isVisible = false;
        this.isEditing = false;
        
        // 编辑器元素
        this.elements = {
            container: null,
            canvas: null,
            toolbar: null,
            properties: null,
            preview: null
        };
        
        // 编辑器状态
        this.editMode = 'particle'; // particle, connection, objective
        this.selectedParticle = null;
        this.selectedConnection = null;
        
        // 当前编辑的关卡
        this.currentLevel = {
            id: '',
            name: '新关卡',
            description: '关卡描述',
            author: '未知作者',
            version: '1.0',
            difficulty: {
                normal: {
                    particles: [],
                    connections: [],
                    objectives: [],
                    timeLimit: 180,
                    targetScore: 1000
                }
            },
            theme: 'quantum',
            music: ''
        };
        
        // 编辑器配置
        this.config = {
            canvasWidth: 800,
            canvasHeight: 600,
            gridSize: 20,
            showGrid: true,
            snapToGrid: true,
            particleRadius: 8,
            connectionWidth: 2
        };
        
        // 鼠标状态
        this.mouse = {
            x: 0,
            y: 0,
            down: false,
            dragging: false,
            dragTarget: null
        };
        
        console.log('🛠️ 关卡编辑器已创建');
    }

    /**
     * 初始化关卡编辑器
     */
    init() {
        try {
            // 获取编辑器元素
            this.getEditorElements();
            
            // 创建编辑器结构
            this.createEditorStructure();
            
            // 初始化画布
            this.initCanvas();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 初始化工具栏
            this.initToolbar();
            
            this.isInitialized = true;
            console.log('✅ 关卡编辑器初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 关卡编辑器初始化失败:', error);
            return false;
        }
    }

    /**
     * 获取编辑器元素
     */
    getEditorElements() {
        this.elements.container = document.getElementById('levelEditorScreen') || 
                                 document.querySelector('.level-editor-screen');
    }

    /**
     * 创建编辑器结构
     */
    createEditorStructure() {
        if (!this.elements.container) {
            console.warn('⚠️ 关卡编辑器容器不存在，跳过结构创建');
            return;
        }

        this.elements.container.innerHTML = `
            <div class="editor-header">
                <h2>关卡编辑器</h2>
                <div class="editor-actions">
                    <button class="editor-button" id="newLevelBtn">新建</button>
                    <button class="editor-button" id="loadLevelBtn">加载</button>
                    <button class="editor-button" id="saveLevelBtn">保存</button>
                    <button class="editor-button" id="testLevelBtn">测试</button>
                    <button class="editor-button secondary" id="editorBackBtn">返回</button>
                </div>
            </div>
            
            <div class="editor-content">
                <div class="editor-sidebar">
                    <div class="editor-toolbar" id="editorToolbar">
                        <h3>工具</h3>
                        <div class="tool-group">
                            <button class="tool-button active" data-tool="particle">
                                <span class="tool-icon">⚛️</span>
                                <span class="tool-name">粒子</span>
                            </button>
                            <button class="tool-button" data-tool="connection">
                                <span class="tool-icon">🔗</span>
                                <span class="tool-name">连接</span>
                            </button>
                            <button class="tool-button" data-tool="objective">
                                <span class="tool-icon">🎯</span>
                                <span class="tool-name">目标</span>
                            </button>
                        </div>
                        
                        <div class="editor-options">
                            <h4>选项</h4>
                            <label class="option-item">
                                <input type="checkbox" id="showGridCheck" checked>
                                显示网格
                            </label>
                            <label class="option-item">
                                <input type="checkbox" id="snapToGridCheck" checked>
                                对齐网格
                            </label>
                        </div>
                    </div>
                    
                    <div class="editor-properties" id="editorProperties">
                        <h3>属性</h3>
                        <div class="properties-content" id="propertiesContent">
                            <p class="no-selection">请选择一个对象</p>
                        </div>
                    </div>
                </div>
                
                <div class="editor-main">
                    <div class="editor-canvas-container">
                        <canvas id="editorCanvas" width="800" height="600"></canvas>
                    </div>
                    
                    <div class="editor-status">
                        <span class="status-item">模式: <span id="currentMode">粒子</span></span>
                        <span class="status-item">粒子: <span id="particleCount">0</span></span>
                        <span class="status-item">连接: <span id="connectionCount">0</span></span>
                        <span class="status-item">鼠标: <span id="mousePos">0, 0</span></span>
                    </div>
                </div>
                
                <div class="editor-panel">
                    <div class="level-info-panel">
                        <h3>关卡信息</h3>
                        <div class="info-group">
                            <label>关卡名称</label>
                            <input type="text" id="levelName" value="新关卡" class="info-input">
                        </div>
                        <div class="info-group">
                            <label>关卡描述</label>
                            <textarea id="levelDescription" class="info-textarea">关卡描述</textarea>
                        </div>
                        <div class="info-group">
                            <label>作者</label>
                            <input type="text" id="levelAuthor" value="未知作者" class="info-input">
                        </div>
                        <div class="info-group">
                            <label>主题</label>
                            <select id="levelTheme" class="info-select">
                                <option value="quantum">量子</option>
                                <option value="energy">能量</option>
                                <option value="space">太空</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="difficulty-panel">
                        <h3>难度设置</h3>
                        <div class="difficulty-tabs">
                            <button class="difficulty-tab active" data-difficulty="normal">普通</button>
                            <button class="difficulty-tab" data-difficulty="easy">简单</button>
                            <button class="difficulty-tab" data-difficulty="hard">困难</button>
                        </div>
                        
                        <div class="difficulty-settings">
                            <div class="setting-group">
                                <label>时间限制 (秒)</label>
                                <input type="number" id="timeLimit" value="180" min="30" max="600" class="setting-input">
                            </div>
                            <div class="setting-group">
                                <label>目标分数</label>
                                <input type="number" id="targetScore" value="1000" min="100" max="50000" class="setting-input">
                            </div>
                        </div>
                    </div>
                    
                    <div class="objectives-panel">
                        <h3>关卡目标</h3>
                        <div class="objectives-list" id="objectivesList">
                            <!-- 目标列表将在这里动态生成 -->
                        </div>
                        <button class="add-objective-btn" id="addObjectiveBtn">添加目标</button>
                    </div>
                </div>
            </div>
        `;

        // 更新元素引用
        this.elements.canvas = document.getElementById('editorCanvas');
        this.elements.toolbar = document.getElementById('editorToolbar');
        this.elements.properties = document.getElementById('editorProperties');
    }

    /**
     * 初始化画布
     */
    initCanvas() {
        if (!this.elements.canvas) return;
        
        this.ctx = this.elements.canvas.getContext('2d');
        
        // 设置画布样式
        this.elements.canvas.style.border = '2px solid var(--ui-border)';
        this.elements.canvas.style.borderRadius = '8px';
        this.elements.canvas.style.background = 'var(--bg-primary)';
        this.elements.canvas.style.cursor = 'crosshair';
        
        // 开始渲染循环
        this.startRenderLoop();
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 工具按钮
        document.querySelectorAll('.tool-button').forEach(button => {
            button.addEventListener('click', (e) => {
                this.selectTool(e.target.closest('.tool-button').dataset.tool);
            });
        });
        
        // 画布事件
        if (this.elements.canvas) {
            this.elements.canvas.addEventListener('mousedown', (e) => this.onMouseDown(e));
            this.elements.canvas.addEventListener('mousemove', (e) => this.onMouseMove(e));
            this.elements.canvas.addEventListener('mouseup', (e) => this.onMouseUp(e));
            this.elements.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
        }
        
        // 选项复选框
        const showGridCheck = document.getElementById('showGridCheck');
        const snapToGridCheck = document.getElementById('snapToGridCheck');
        
        if (showGridCheck) {
            showGridCheck.addEventListener('change', (e) => {
                this.config.showGrid = e.target.checked;
            });
        }
        
        if (snapToGridCheck) {
            snapToGridCheck.addEventListener('change', (e) => {
                this.config.snapToGrid = e.target.checked;
            });
        }
        
        // 按钮事件
        const newLevelBtn = document.getElementById('newLevelBtn');
        const loadLevelBtn = document.getElementById('loadLevelBtn');
        const saveLevelBtn = document.getElementById('saveLevelBtn');
        const testLevelBtn = document.getElementById('testLevelBtn');
        const editorBackBtn = document.getElementById('editorBackBtn');
        
        if (newLevelBtn) newLevelBtn.addEventListener('click', () => this.newLevel());
        if (loadLevelBtn) loadLevelBtn.addEventListener('click', () => this.loadLevel());
        if (saveLevelBtn) saveLevelBtn.addEventListener('click', () => this.saveLevel());
        if (testLevelBtn) testLevelBtn.addEventListener('click', () => this.testLevel());
        if (editorBackBtn) editorBackBtn.addEventListener('click', () => this.hide());
        
        // 关卡信息输入
        const levelName = document.getElementById('levelName');
        const levelDescription = document.getElementById('levelDescription');
        const levelAuthor = document.getElementById('levelAuthor');
        const levelTheme = document.getElementById('levelTheme');
        
        if (levelName) {
            levelName.addEventListener('input', (e) => {
                this.currentLevel.name = e.target.value;
            });
        }
        
        if (levelDescription) {
            levelDescription.addEventListener('input', (e) => {
                this.currentLevel.description = e.target.value;
            });
        }
        
        if (levelAuthor) {
            levelAuthor.addEventListener('input', (e) => {
                this.currentLevel.author = e.target.value;
            });
        }
        
        if (levelTheme) {
            levelTheme.addEventListener('change', (e) => {
                this.currentLevel.theme = e.target.value;
            });
        }
        
        // 难度设置
        const timeLimit = document.getElementById('timeLimit');
        const targetScore = document.getElementById('targetScore');
        
        if (timeLimit) {
            timeLimit.addEventListener('input', (e) => {
                this.currentLevel.difficulty.normal.timeLimit = parseInt(e.target.value);
            });
        }
        
        if (targetScore) {
            targetScore.addEventListener('input', (e) => {
                this.currentLevel.difficulty.normal.targetScore = parseInt(e.target.value);
            });
        }
    }

    /**
     * 初始化工具栏
     */
    initToolbar() {
        this.selectTool('particle');
    }

    /**
     * 选择工具
     * @param {string} tool - 工具名称
     */
    selectTool(tool) {
        this.editMode = tool;
        
        // 更新工具按钮状态
        document.querySelectorAll('.tool-button').forEach(button => {
            button.classList.toggle('active', button.dataset.tool === tool);
        });
        
        // 更新状态显示
        const currentMode = document.getElementById('currentMode');
        if (currentMode) {
            const modeNames = {
                'particle': '粒子',
                'connection': '连接',
                'objective': '目标'
            };
            currentMode.textContent = modeNames[tool] || tool;
        }
        
        // 更新鼠标样式
        if (this.elements.canvas) {
            const cursors = {
                'particle': 'crosshair',
                'connection': 'pointer',
                'objective': 'help'
            };
            this.elements.canvas.style.cursor = cursors[tool] || 'default';
        }
    }

    /**
     * 鼠标按下事件
     * @param {MouseEvent} event - 鼠标事件
     */
    onMouseDown(event) {
        const rect = this.elements.canvas.getBoundingClientRect();
        this.mouse.x = event.clientX - rect.left;
        this.mouse.y = event.clientY - rect.top;
        this.mouse.down = true;
        
        if (this.config.snapToGrid) {
            this.mouse.x = Math.round(this.mouse.x / this.config.gridSize) * this.config.gridSize;
            this.mouse.y = Math.round(this.mouse.y / this.config.gridSize) * this.config.gridSize;
        }
        
        switch (this.editMode) {
            case 'particle':
                this.handleParticleClick(event);
                break;
            case 'connection':
                this.handleConnectionClick(event);
                break;
            case 'objective':
                this.handleObjectiveClick(event);
                break;
        }
    }

    /**
     * 鼠标移动事件
     * @param {MouseEvent} event - 鼠标事件
     */
    onMouseMove(event) {
        const rect = this.elements.canvas.getBoundingClientRect();
        this.mouse.x = event.clientX - rect.left;
        this.mouse.y = event.clientY - rect.top;
        
        if (this.config.snapToGrid) {
            this.mouse.x = Math.round(this.mouse.x / this.config.gridSize) * this.config.gridSize;
            this.mouse.y = Math.round(this.mouse.y / this.config.gridSize) * this.config.gridSize;
        }
        
        // 更新鼠标位置显示
        const mousePos = document.getElementById('mousePos');
        if (mousePos) {
            mousePos.textContent = `${Math.round(this.mouse.x)}, ${Math.round(this.mouse.y)}`;
        }
        
        // 处理拖拽
        if (this.mouse.down && this.mouse.dragTarget) {
            this.mouse.dragTarget.x = this.mouse.x / this.config.canvasWidth;
            this.mouse.dragTarget.y = this.mouse.y / this.config.canvasHeight;
            this.mouse.dragging = true;
        }
    }

    /**
     * 鼠标释放事件
     * @param {MouseEvent} event - 鼠标事件
     */
    onMouseUp(event) {
        this.mouse.down = false;
        this.mouse.dragging = false;
        this.mouse.dragTarget = null;
    }

    /**
     * 处理粒子点击
     * @param {MouseEvent} event - 鼠标事件
     */
    handleParticleClick(event) {
        const particles = this.currentLevel.difficulty.normal.particles;
        
        // 检查是否点击了现有粒子
        const clickedParticle = this.findParticleAt(this.mouse.x, this.mouse.y);
        
        if (clickedParticle) {
            if (event.button === 2) { // 右键删除
                const index = particles.indexOf(clickedParticle);
                if (index >= 0) {
                    particles.splice(index, 1);
                    this.updateCounts();
                }
            } else { // 左键选择/拖拽
                this.selectedParticle = clickedParticle;
                this.mouse.dragTarget = clickedParticle;
                this.updateProperties();
            }
        } else if (event.button === 0) { // 左键添加新粒子
            const newParticle = {
                x: this.mouse.x / this.config.canvasWidth,
                y: this.mouse.y / this.config.canvasHeight,
                frequency: 440,
                energy: 50
            };
            
            particles.push(newParticle);
            this.selectedParticle = newParticle;
            this.updateCounts();
            this.updateProperties();
        }
    }

    /**
     * 处理连接点击
     * @param {MouseEvent} event - 鼠标事件
     */
    handleConnectionClick(event) {
        const clickedParticle = this.findParticleAt(this.mouse.x, this.mouse.y);
        
        if (clickedParticle) {
            if (!this.selectedParticle) {
                // 选择第一个粒子
                this.selectedParticle = clickedParticle;
            } else if (this.selectedParticle !== clickedParticle) {
                // 创建连接
                const particles = this.currentLevel.difficulty.normal.particles;
                const fromIndex = particles.indexOf(this.selectedParticle);
                const toIndex = particles.indexOf(clickedParticle);
                
                if (fromIndex >= 0 && toIndex >= 0) {
                    const connections = this.currentLevel.difficulty.normal.connections;
                    
                    // 检查连接是否已存在
                    const existingConnection = connections.find(conn => 
                        (conn.from === fromIndex && conn.to === toIndex) ||
                        (conn.from === toIndex && conn.to === fromIndex)
                    );
                    
                    if (!existingConnection) {
                        connections.push({
                            from: fromIndex,
                            to: toIndex,
                            strength: 0.5
                        });
                        this.updateCounts();
                    }
                }
                
                this.selectedParticle = null;
            }
        }
    }

    /**
     * 处理目标点击
     * @param {MouseEvent} event - 鼠标事件
     */
    handleObjectiveClick(event) {
        // 目标编辑功能待实现
        console.log('目标编辑功能待实现');
    }

    /**
     * 查找指定位置的粒子
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @returns {Object|null} 找到的粒子
     */
    findParticleAt(x, y) {
        const particles = this.currentLevel.difficulty.normal.particles;
        
        for (const particle of particles) {
            const px = particle.x * this.config.canvasWidth;
            const py = particle.y * this.config.canvasHeight;
            const distance = Math.sqrt((x - px) ** 2 + (y - py) ** 2);
            
            if (distance <= this.config.particleRadius) {
                return particle;
            }
        }
        
        return null;
    }

    /**
     * 开始渲染循环
     */
    startRenderLoop() {
        const render = () => {
            this.render();
            requestAnimationFrame(render);
        };
        
        requestAnimationFrame(render);
    }

    /**
     * 渲染编辑器
     */
    render() {
        if (!this.ctx) return;
        
        const ctx = this.ctx;
        const width = this.config.canvasWidth;
        const height = this.config.canvasHeight;
        
        // 清空画布
        ctx.clearRect(0, 0, width, height);
        
        // 绘制网格
        if (this.config.showGrid) {
            this.drawGrid(ctx, width, height);
        }
        
        // 绘制连接
        this.drawConnections(ctx, width, height);
        
        // 绘制粒子
        this.drawParticles(ctx, width, height);
        
        // 绘制选中状态
        this.drawSelection(ctx, width, height);
    }

    /**
     * 绘制网格
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     * @param {number} width - 画布宽度
     * @param {number} height - 画布高度
     */
    drawGrid(ctx, width, height) {
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.lineWidth = 1;
        
        const gridSize = this.config.gridSize;
        
        // 绘制垂直线
        for (let x = 0; x <= width; x += gridSize) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, height);
            ctx.stroke();
        }
        
        // 绘制水平线
        for (let y = 0; y <= height; y += gridSize) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(width, y);
            ctx.stroke();
        }
    }

    /**
     * 绘制连接
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     * @param {number} width - 画布宽度
     * @param {number} height - 画布高度
     */
    drawConnections(ctx, width, height) {
        const particles = this.currentLevel.difficulty.normal.particles;
        const connections = this.currentLevel.difficulty.normal.connections;
        
        ctx.strokeStyle = 'rgba(0, 255, 255, 0.6)';
        ctx.lineWidth = this.config.connectionWidth;
        
        connections.forEach(connection => {
            const fromParticle = particles[connection.from];
            const toParticle = particles[connection.to];
            
            if (fromParticle && toParticle) {
                const x1 = fromParticle.x * width;
                const y1 = fromParticle.y * height;
                const x2 = toParticle.x * width;
                const y2 = toParticle.y * height;
                
                ctx.beginPath();
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.stroke();
            }
        });
    }

    /**
     * 绘制粒子
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     * @param {number} width - 画布宽度
     * @param {number} height - 画布高度
     */
    drawParticles(ctx, width, height) {
        const particles = this.currentLevel.difficulty.normal.particles;
        
        particles.forEach(particle => {
            const x = particle.x * width;
            const y = particle.y * height;
            
            // 绘制发光效果
            ctx.save();
            ctx.shadowColor = '#00ffff';
            ctx.shadowBlur = 10;
            
            // 绘制粒子
            ctx.fillStyle = '#00ffff';
            ctx.beginPath();
            ctx.arc(x, y, this.config.particleRadius, 0, Math.PI * 2);
            ctx.fill();
            
            // 绘制频率标签
            ctx.restore();
            ctx.fillStyle = '#ffffff';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`${particle.frequency}Hz`, x, y - 15);
        });
    }

    /**
     * 绘制选中状态
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     * @param {number} width - 画布宽度
     * @param {number} height - 画布高度
     */
    drawSelection(ctx, width, height) {
        if (this.selectedParticle) {
            const x = this.selectedParticle.x * width;
            const y = this.selectedParticle.y * height;
            
            ctx.strokeStyle = '#ffff00';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(x, y, this.config.particleRadius + 5, 0, Math.PI * 2);
            ctx.stroke();
        }
    }

    /**
     * 更新计数显示
     */
    updateCounts() {
        const particleCount = document.getElementById('particleCount');
        const connectionCount = document.getElementById('connectionCount');
        
        if (particleCount) {
            particleCount.textContent = this.currentLevel.difficulty.normal.particles.length;
        }
        
        if (connectionCount) {
            connectionCount.textContent = this.currentLevel.difficulty.normal.connections.length;
        }
    }

    /**
     * 更新属性面板
     */
    updateProperties() {
        const propertiesContent = document.getElementById('propertiesContent');
        if (!propertiesContent) return;
        
        if (this.selectedParticle) {
            propertiesContent.innerHTML = `
                <div class="property-group">
                    <label>频率 (Hz)</label>
                    <input type="number" id="particleFrequency" value="${this.selectedParticle.frequency}" min="20" max="20000">
                </div>
                <div class="property-group">
                    <label>能量</label>
                    <input type="number" id="particleEnergy" value="${this.selectedParticle.energy}" min="1" max="100">
                </div>
                <div class="property-group">
                    <label>X坐标</label>
                    <input type="number" id="particleX" value="${(this.selectedParticle.x * 100).toFixed(1)}" min="0" max="100" step="0.1">
                </div>
                <div class="property-group">
                    <label>Y坐标</label>
                    <input type="number" id="particleY" value="${(this.selectedParticle.y * 100).toFixed(1)}" min="0" max="100" step="0.1">
                </div>
            `;
            
            // 添加属性变化监听器
            const frequencyInput = document.getElementById('particleFrequency');
            const energyInput = document.getElementById('particleEnergy');
            const xInput = document.getElementById('particleX');
            const yInput = document.getElementById('particleY');
            
            if (frequencyInput) {
                frequencyInput.addEventListener('input', (e) => {
                    this.selectedParticle.frequency = parseInt(e.target.value);
                });
            }
            
            if (energyInput) {
                energyInput.addEventListener('input', (e) => {
                    this.selectedParticle.energy = parseInt(e.target.value);
                });
            }
            
            if (xInput) {
                xInput.addEventListener('input', (e) => {
                    this.selectedParticle.x = parseFloat(e.target.value) / 100;
                });
            }
            
            if (yInput) {
                yInput.addEventListener('input', (e) => {
                    this.selectedParticle.y = parseFloat(e.target.value) / 100;
                });
            }
        } else {
            propertiesContent.innerHTML = '<p class="no-selection">请选择一个对象</p>';
        }
    }

    /**
     * 新建关卡
     */
    newLevel() {
        this.currentLevel = {
            id: '',
            name: '新关卡',
            description: '关卡描述',
            author: '未知作者',
            version: '1.0',
            difficulty: {
                normal: {
                    particles: [],
                    connections: [],
                    objectives: [],
                    timeLimit: 180,
                    targetScore: 1000
                }
            },
            theme: 'quantum',
            music: ''
        };
        
        this.selectedParticle = null;
        this.selectedConnection = null;
        this.updateCounts();
        this.updateProperties();
        
        // 重置表单
        const levelName = document.getElementById('levelName');
        const levelDescription = document.getElementById('levelDescription');
        const levelAuthor = document.getElementById('levelAuthor');
        const levelTheme = document.getElementById('levelTheme');
        const timeLimit = document.getElementById('timeLimit');
        const targetScore = document.getElementById('targetScore');
        
        if (levelName) levelName.value = '新关卡';
        if (levelDescription) levelDescription.value = '关卡描述';
        if (levelAuthor) levelAuthor.value = '未知作者';
        if (levelTheme) levelTheme.value = 'quantum';
        if (timeLimit) timeLimit.value = '180';
        if (targetScore) targetScore.value = '1000';
        
        console.log('📝 已创建新关卡');
    }

    /**
     * 加载关卡
     */
    loadLevel() {
        // 显示关卡选择对话框
        if (window.uiManager) {
            uiManager.showModal('loadLevelModal', {
                title: '加载关卡',
                content: '选择要加载的关卡',
                onConfirm: (levelId) => {
                    this.doLoadLevel(levelId);
                }
            });
        }
    }

    /**
     * 执行关卡加载
     * @param {string} levelId - 关卡ID
     */
    doLoadLevel(levelId) {
        if (window.levelManager) {
            const level = levelManager.getLevel(levelId);
            if (level) {
                this.currentLevel = JSON.parse(JSON.stringify(level)); // 深拷贝
                this.selectedParticle = null;
                this.selectedConnection = null;
                this.updateCounts();
                this.updateProperties();
                
                console.log(`📂 已加载关卡: ${level.name}`);
            }
        }
    }

    /**
     * 保存关卡
     */
    saveLevel() {
        if (!this.currentLevel.name || this.currentLevel.name === '新关卡') {
            alert('请输入关卡名称');
            return;
        }
        
        // 生成关卡ID
        if (!this.currentLevel.id) {
            this.currentLevel.id = this.generateLevelId(this.currentLevel.name);
        }
        
        // 验证关卡
        if (window.levelManager) {
            const validation = levelManager.validateLevel(this.currentLevel);
            if (!validation.valid) {
                alert(`关卡验证失败:\n${validation.errors.join('\n')}`);
                return;
            }
            
            if (validation.warnings.length > 0) {
                const proceed = confirm(`关卡有以下警告:\n${validation.warnings.join('\n')}\n\n是否继续保存？`);
                if (!proceed) return;
            }
            
            // 保存关卡
            const success = levelManager.saveCustomLevel(this.currentLevel);
            if (success) {
                alert('关卡保存成功！');
            } else {
                alert('关卡保存失败！');
            }
        }
    }

    /**
     * 测试关卡
     */
    testLevel() {
        if (this.currentLevel.difficulty.normal.particles.length === 0) {
            // 使用通知系统替代alert弹框
            if (window.notificationSystem) {
                window.notificationSystem.warning(
                    '关卡中没有粒子，无法测试',
                    { title: '测试关卡', duration: 4000 }
                );
            } else {
                alert('关卡中没有粒子，无法测试');
            }
            return;
        }
        
        // 临时保存关卡用于测试
        const testLevel = JSON.parse(JSON.stringify(this.currentLevel));
        testLevel.id = 'test_level';
        testLevel.name = '测试关卡';
        
        // 启动测试
        if (window.gameController && window.uiManager) {
            gameController.startTestLevel(testLevel);
            uiManager.showScreen('game-screen');
        }
        
        console.log('🧪 开始测试关卡');
    }

    /**
     * 生成关卡ID
     * @param {string} name - 关卡名称
     * @returns {string} 关卡ID
     */
    generateLevelId(name) {
        const timestamp = Date.now();
        const randomStr = Math.random().toString(36).substring(2, 8);
        return `custom_${name.replace(/\s+/g, '_').toLowerCase()}_${timestamp}_${randomStr}`;
    }

    /**
     * 显示关卡编辑器
     */
    show() {
        if (!this.isInitialized) {
            this.init();
        }
        
        if (this.elements.container) {
            this.elements.container.style.display = 'flex';
            this.elements.container.classList.add('screen-enter');
            
            setTimeout(() => {
                this.elements.container.classList.remove('screen-enter');
            }, 500);
        }
        
        this.isVisible = true;
        this.updateCounts();
    }

    /**
     * 隐藏关卡编辑器
     */
    hide() {
        if (this.elements.container) {
            this.elements.container.classList.add('screen-exit');
            
            setTimeout(() => {
                this.elements.container.style.display = 'none';
                this.elements.container.classList.remove('screen-exit');
            }, 500);
        }
        
        this.isVisible = false;
        
        // 返回主菜单
        if (window.uiManager) {
            uiManager.showScreen('mainMenuScreen');
        }
    }

    /**
     * 销毁关卡编辑器
     */
    destroy() {
        // 清理元素引用
        Object.keys(this.elements).forEach(key => {
            this.elements[key] = null;
        });
        
        this.currentLevel = null;
        this.selectedParticle = null;
        this.selectedConnection = null;
        this.isInitialized = false;
        
        console.log('🛠️ 关卡编辑器已销毁');
    }
}

// 创建全局关卡编辑器实例
window.levelEditor = new LevelEditor();
