/**
 * 量子共鸣者 - 游戏结束屏幕
 * 负责显示游戏结果、分数统计和重玩选项
 */

class GameOver {
    constructor() {
        this.isInitialized = false;
        this.isVisible = false;
        
        // 游戏结果数据
        this.gameResult = null;
        
        // 元素引用
        this.elements = {
            container: null,
            scoreDisplay: null,
            starsDisplay: null,
            statsDisplay: null,
            buttonsContainer: null
        };
        
        // 动画状态
        this.animations = {
            scoreCountUp: null,
            starsReveal: null
        };
        
        console.log('🏁 游戏结束屏幕已创建');
    }

    /**
     * 初始化游戏结束屏幕
     */
    init() {
        try {
            // 获取容器元素
            this.getElements();
            
            // 创建屏幕结构
            this.createScreenStructure();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('✅ 游戏结束屏幕初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 游戏结束屏幕初始化失败:', error);
            return false;
        }
    }

    /**
     * 获取元素引用
     */
    getElements() {
        this.elements.container = document.getElementById('gameOverScreen') || 
                                 document.querySelector('.game-over-screen');
    }

    /**
     * 创建屏幕结构
     */
    createScreenStructure() {
        if (!this.elements.container) {
            console.warn('⚠️ 游戏结束屏幕容器不存在，跳过结构创建');
            return;
        }

        this.elements.container.innerHTML = `
            <div class="game-over-overlay">
                <div class="game-over-content">
                    <div class="game-over-header">
                        <h1 class="game-over-title" id="gameOverTitle">关卡完成！</h1>
                        <div class="level-info" id="levelInfo">
                            <span class="level-name" id="levelName">关卡名称</span>
                            <span class="difficulty-badge" id="difficultyBadge">普通</span>
                        </div>
                    </div>
                    
                    <div class="game-over-main">
                        <div class="score-section">
                            <div class="final-score">
                                <span class="score-label">最终得分</span>
                                <span class="score-value" id="finalScore">0</span>
                            </div>
                            
                            <div class="stars-display" id="starsDisplay">
                                <div class="star" data-star="1">⭐</div>
                                <div class="star" data-star="2">⭐</div>
                                <div class="star" data-star="3">⭐</div>
                            </div>
                        </div>
                        
                        <div class="stats-section" id="statsSection">
                            <h3 class="stats-title">游戏统计</h3>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <span class="stat-label">游戏时间</span>
                                    <span class="stat-value" id="gameTime">00:00</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">激活粒子</span>
                                    <span class="stat-value" id="particlesActivated">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">连锁反应</span>
                                    <span class="stat-value" id="chainReactions">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">最大连击</span>
                                    <span class="stat-value" id="maxCombo">0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">精确度</span>
                                    <span class="stat-value" id="accuracy">0%</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">效率</span>
                                    <span class="stat-value" id="efficiency">0%</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="achievements-section" id="achievementsSection">
                            <h3 class="achievements-title">本局成就</h3>
                            <div class="achievements-list" id="achievementsList">
                                <!-- 成就列表将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                    
                    <div class="game-over-footer">
                        <div class="action-buttons">
                            <button class="game-over-btn secondary" id="backToMenuBtn">
                                <span class="btn-icon">🏠</span>
                                <span class="btn-text">返回菜单</span>
                            </button>
                            <button class="game-over-btn secondary" id="selectLevelBtn">
                                <span class="btn-icon">📋</span>
                                <span class="btn-text">选择关卡</span>
                            </button>
                            <button class="game-over-btn primary" id="replayBtn">
                                <span class="btn-icon">🔄</span>
                                <span class="btn-text">重新游玩</span>
                            </button>
                            <button class="game-over-btn primary" id="nextLevelBtn" style="display: none;">
                                <span class="btn-icon">➡️</span>
                                <span class="btn-text">下一关</span>
                            </button>
                        </div>
                        
                        <div class="social-actions">
                            <button class="social-btn" id="shareScoreBtn">
                                <span class="btn-icon">📤</span>
                                <span class="btn-text">分享成绩</span>
                            </button>
                            <button class="social-btn" id="saveReplayBtn">
                                <span class="btn-icon">💾</span>
                                <span class="btn-text">保存回放</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 背景粒子效果 -->
                <canvas id="gameOverBackground" class="game-over-background"></canvas>
            </div>
        `;

        // 更新元素引用
        this.elements.scoreDisplay = document.getElementById('finalScore');
        this.elements.starsDisplay = document.getElementById('starsDisplay');
        this.elements.statsDisplay = document.getElementById('statsSection');
        this.elements.buttonsContainer = document.querySelector('.action-buttons');
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 返回菜单按钮
        const backToMenuBtn = document.getElementById('backToMenuBtn');
        if (backToMenuBtn) {
            backToMenuBtn.addEventListener('click', () => {
                this.hide();
                if (window.uiManager) {
                    uiManager.showScreen('main-menu-screen');
                }
            });
        }
        
        // 选择关卡按钮
        const selectLevelBtn = document.getElementById('selectLevelBtn');
        if (selectLevelBtn) {
            selectLevelBtn.addEventListener('click', () => {
                this.hide();
                if (window.uiManager) {
                    uiManager.showScreen('levelSelectScreen');
                }
            });
        }
        
        // 重新游玩按钮
        const replayBtn = document.getElementById('replayBtn');
        if (replayBtn) {
            replayBtn.addEventListener('click', () => {
                this.replayLevel();
            });
        }
        
        // 下一关按钮
        const nextLevelBtn = document.getElementById('nextLevelBtn');
        if (nextLevelBtn) {
            nextLevelBtn.addEventListener('click', () => {
                this.playNextLevel();
            });
        }
        
        // 分享成绩按钮
        const shareScoreBtn = document.getElementById('shareScoreBtn');
        if (shareScoreBtn) {
            shareScoreBtn.addEventListener('click', () => {
                this.shareScore();
            });
        }
        
        // 保存回放按钮
        const saveReplayBtn = document.getElementById('saveReplayBtn');
        if (saveReplayBtn) {
            saveReplayBtn.addEventListener('click', () => {
                this.saveReplay();
            });
        }
        
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (this.isVisible) {
                switch (e.key) {
                    case 'Escape':
                        this.hide();
                        if (window.uiManager) {
                            uiManager.showScreen('main-menu-screen');
                        }
                        break;
                    case 'Enter':
                    case ' ':
                        this.replayLevel();
                        break;
                    case 'n':
                    case 'N':
                        this.playNextLevel();
                        break;
                }
            }
        });
    }

    /**
     * 显示游戏结束屏幕
     * @param {Object} result - 游戏结果数据
     */
    show(result) {
        if (!this.isInitialized) {
            this.init();
        }
        
        this.gameResult = result;
        
        // 显示容器
        if (this.elements.container) {
            this.elements.container.style.display = 'flex';
            this.elements.container.classList.add('screen-enter');
            
            setTimeout(() => {
                this.elements.container.classList.remove('screen-enter');
            }, 500);
        }
        
        // 更新显示内容
        this.updateDisplay(result);
        
        // 开始动画
        this.startAnimations();
        
        // 初始化背景效果
        this.initBackground();
        
        this.isVisible = true;
        console.log('🏁 游戏结束屏幕已显示');
    }

    /**
     * 隐藏游戏结束屏幕
     */
    hide() {
        if (this.elements.container) {
            this.elements.container.classList.add('screen-exit');
            
            setTimeout(() => {
                this.elements.container.style.display = 'none';
                this.elements.container.classList.remove('screen-exit');
            }, 500);
        }
        
        // 停止动画
        this.stopAnimations();
        
        this.isVisible = false;
        console.log('🏁 游戏结束屏幕已隐藏');
    }

    /**
     * 更新显示内容
     * @param {Object} result - 游戏结果数据
     */
    updateDisplay(result) {
        // 更新标题
        const title = document.getElementById('gameOverTitle');
        if (title) {
            title.textContent = result.completed ? '关卡完成！' : '游戏结束';
            title.className = `game-over-title ${result.completed ? 'success' : 'failed'}`;
        }
        
        // 更新关卡信息
        const levelName = document.getElementById('levelName');
        const difficultyBadge = document.getElementById('difficultyBadge');
        
        if (levelName && result.levelName) {
            levelName.textContent = result.levelName;
        }
        
        if (difficultyBadge && result.difficulty) {
            const difficultyNames = {
                'easy': '简单',
                'normal': '普通',
                'hard': '困难',
                'expert': '专家'
            };
            difficultyBadge.textContent = difficultyNames[result.difficulty] || result.difficulty;
            difficultyBadge.className = `difficulty-badge ${result.difficulty}`;
        }
        
        // 更新分数（将通过动画显示）
        if (this.elements.scoreDisplay) {
            this.elements.scoreDisplay.textContent = '0';
        }
        
        // 更新星级显示
        this.updateStarsDisplay(result.stars || 0);
        
        // 更新统计数据
        this.updateStats(result);
        
        // 更新成就
        this.updateAchievements(result.achievements || []);
        
        // 更新按钮状态
        this.updateButtons(result);
    }

    /**
     * 更新星级显示
     * @param {number} stars - 获得的星级数
     */
    updateStarsDisplay(stars) {
        const starElements = document.querySelectorAll('.star');
        
        starElements.forEach((star, index) => {
            star.classList.remove('earned', 'animate');
            if (index < stars) {
                setTimeout(() => {
                    star.classList.add('earned', 'animate');
                }, (index + 1) * 300);
            }
        });
    }

    /**
     * 更新统计数据
     * @param {Object} result - 游戏结果数据
     */
    updateStats(result) {
        const stats = {
            gameTime: this.formatTime(result.time || 0),
            particlesActivated: result.particlesActivated || 0,
            chainReactions: result.chainReactions || 0,
            maxCombo: result.maxCombo || 0,
            accuracy: `${Math.round((result.accuracy || 0) * 100)}%`,
            efficiency: `${Math.round((result.efficiency || 0) * 100)}%`
        };
        
        Object.entries(stats).forEach(([key, value]) => {
            const element = document.getElementById(key);
            if (element) {
                element.textContent = value;
            }
        });
    }

    /**
     * 更新成就显示
     * @param {Array} achievements - 获得的成就列表
     */
    updateAchievements(achievements) {
        const achievementsList = document.getElementById('achievementsList');
        if (!achievementsList) return;
        
        if (achievements.length === 0) {
            achievementsList.innerHTML = '<p class="no-achievements">本局未获得新成就</p>';
            return;
        }
        
        achievementsList.innerHTML = achievements.map(achievement => `
            <div class="achievement-item">
                <span class="achievement-icon">${achievement.icon || '🏆'}</span>
                <div class="achievement-info">
                    <span class="achievement-name">${achievement.name}</span>
                    <span class="achievement-description">${achievement.description}</span>
                </div>
            </div>
        `).join('');
    }

    /**
     * 更新按钮状态
     * @param {Object} result - 游戏结果数据
     */
    updateButtons(result) {
        const nextLevelBtn = document.getElementById('nextLevelBtn');
        
        // 检查是否有下一关可用
        if (result.hasNextLevel && result.completed) {
            if (nextLevelBtn) {
                nextLevelBtn.style.display = 'flex';
            }
        } else {
            if (nextLevelBtn) {
                nextLevelBtn.style.display = 'none';
            }
        }
    }

    /**
     * 开始动画效果
     */
    startAnimations() {
        // 分数计数动画
        this.animateScoreCountUp();
        
        // 背景粒子动画
        this.startBackgroundAnimation();
    }

    /**
     * 停止动画效果
     */
    stopAnimations() {
        // 停止分数动画
        if (this.animations.scoreCountUp) {
            cancelAnimationFrame(this.animations.scoreCountUp);
            this.animations.scoreCountUp = null;
        }
        
        // 停止星级动画
        if (this.animations.starsReveal) {
            clearTimeout(this.animations.starsReveal);
            this.animations.starsReveal = null;
        }
    }

    /**
     * 分数计数动画
     */
    animateScoreCountUp() {
        if (!this.elements.scoreDisplay || !this.gameResult) return;
        
        const targetScore = this.gameResult.score || 0;
        const duration = 2000; // 2秒
        const startTime = Date.now();
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentScore = Math.round(targetScore * easeOutQuart);
            
            this.elements.scoreDisplay.textContent = currentScore.toLocaleString();
            
            if (progress < 1) {
                this.animations.scoreCountUp = requestAnimationFrame(animate);
            }
        };
        
        animate();
    }

    /**
     * 初始化背景效果
     */
    initBackground() {
        const canvas = document.getElementById('gameOverBackground');
        if (!canvas) return;
        
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        
        // 背景粒子效果将由UI动画系统处理
        if (window.uiAnimations) {
            uiAnimations.createBackgroundParticles(canvas);
        }
    }

    /**
     * 开始背景动画
     */
    startBackgroundAnimation() {
        // 背景动画逻辑
        if (window.uiAnimations) {
            uiAnimations.startGameOverAnimation();
        }
    }

    /**
     * 重新游玩关卡
     */
    replayLevel() {
        this.hide();
        
        if (window.gameController && this.gameResult) {
            gameController.replayLevel();
        }
    }

    /**
     * 游玩下一关
     */
    playNextLevel() {
        this.hide();
        
        if (window.gameController && this.gameResult) {
            gameController.playNextLevel();
        }
    }

    /**
     * 分享成绩
     */
    shareScore() {
        if (!this.gameResult) return;
        
        const shareText = `我在量子共鸣者中获得了 ${this.gameResult.score} 分！快来挑战吧！`;
        
        if (navigator.share) {
            navigator.share({
                title: '量子共鸣者 - 我的成绩',
                text: shareText,
                url: window.location.href
            });
        } else {
            // 复制到剪贴板
            navigator.clipboard.writeText(shareText).then(() => {
                if (window.uiManager) {
                    uiManager.showToast('成绩已复制到剪贴板！');
                }
            });
        }
    }

    /**
     * 保存回放
     */
    saveReplay() {
        if (window.gameController) {
            gameController.saveReplay();
            
            if (window.uiManager) {
                uiManager.showToast('回放已保存！');
            }
        }
    }

    /**
     * 格式化时间
     * @param {number} seconds - 秒数
     * @returns {string} 格式化的时间字符串
     */
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    /**
     * 销毁游戏结束屏幕
     */
    destroy() {
        this.stopAnimations();
        
        // 清理元素引用
        Object.keys(this.elements).forEach(key => {
            this.elements[key] = null;
        });
        
        this.gameResult = null;
        this.isInitialized = false;
        
        console.log('🏁 游戏结束屏幕已销毁');
    }
}

// 创建全局游戏结束屏幕实例
window.gameOver = new GameOver();
