/**
 * 量子共鸣者 - UI组件库
 * 提供可复用的UI组件和交互元素
 */

class UIComponents {
    constructor() {
        this.components = new Map();
        this.animations = new Map();
        this.themes = {
            default: {
                primaryColor: '#4a90e2',
                secondaryColor: '#7b68ee',
                backgroundColor: '#1a1a2e',
                textColor: '#ffffff',
                accentColor: '#ff6b6b'
            }
        };
        this.currentTheme = 'default';
        
        this.init();
    }

    /**
     * 初始化UI组件库
     */
    init() {
        // 注册基础组件
        this.registerBaseComponents();
        
        // 设置全局样式
        this.setupGlobalStyles();
        
        console.log('🎨 UI组件库初始化完成');
    }

    /**
     * 注册基础组件
     */
    registerBaseComponents() {
        // 按钮组件
        this.registerComponent('button', this.createButton.bind(this));
        
        // 滑块组件
        this.registerComponent('slider', this.createSlider.bind(this));
        
        // 进度条组件
        this.registerComponent('progressBar', this.createProgressBar.bind(this));
        
        // 模态框组件
        this.registerComponent('modal', this.createModal.bind(this));
        
        // 通知组件
        this.registerComponent('notification', this.createNotification.bind(this));
        
        // 加载器组件
        this.registerComponent('loader', this.createLoader.bind(this));
    }

    /**
     * 设置全局样式
     */
    setupGlobalStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .ui-component {
                font-family: 'Arial', sans-serif;
                box-sizing: border-box;
            }
            
            .ui-button {
                padding: 12px 24px;
                border: none;
                border-radius: 8px;
                background: linear-gradient(135deg, #4a90e2, #7b68ee);
                color: white;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }
            
            .ui-button:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
            }
            
            .ui-button:active {
                transform: translateY(0);
            }
            
            .ui-button.disabled {
                opacity: 0.5;
                cursor: not-allowed;
                transform: none;
            }
            
            .ui-slider {
                width: 100%;
                height: 6px;
                border-radius: 3px;
                background: rgba(255, 255, 255, 0.2);
                position: relative;
                cursor: pointer;
            }
            
            .ui-slider-track {
                height: 100%;
                border-radius: 3px;
                background: linear-gradient(90deg, #4a90e2, #7b68ee);
                transition: width 0.2s ease;
            }
            
            .ui-slider-thumb {
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background: white;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                cursor: grab;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
                transition: transform 0.2s ease;
            }
            
            .ui-slider-thumb:hover {
                transform: translateY(-50%) scale(1.1);
            }
            
            .ui-progress-bar {
                width: 100%;
                height: 8px;
                border-radius: 4px;
                background: rgba(255, 255, 255, 0.1);
                overflow: hidden;
            }
            
            .ui-progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #4a90e2, #7b68ee);
                transition: width 0.3s ease;
                border-radius: 4px;
            }
            
            .ui-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }
            
            .ui-modal.active {
                opacity: 1;
                visibility: visible;
            }
            
            .ui-modal-content {
                background: #1a1a2e;
                border-radius: 12px;
                padding: 24px;
                max-width: 500px;
                width: 90%;
                transform: scale(0.8);
                transition: transform 0.3s ease;
            }
            
            .ui-modal.active .ui-modal-content {
                transform: scale(1);
            }
            
            .ui-notification {
                position: fixed;
                top: 20px;
                right: 20px;
                background: #1a1a2e;
                color: white;
                padding: 16px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                transform: translateX(100%);
                transition: transform 0.3s ease;
                z-index: 1001;
            }
            
            .ui-notification.show {
                transform: translateX(0);
            }
            
            .ui-loader {
                width: 40px;
                height: 40px;
                border: 4px solid rgba(255, 255, 255, 0.2);
                border-top: 4px solid #4a90e2;
                border-radius: 50%;
                animation: ui-spin 1s linear infinite;
            }
            
            @keyframes ui-spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        
        document.head.appendChild(style);
    }

    /**
     * 注册组件
     */
    registerComponent(name, factory) {
        this.components.set(name, factory);
    }

    /**
     * 创建按钮组件
     */
    createButton(config) {
        const button = document.createElement('button');
        button.className = 'ui-component ui-button';
        button.textContent = config.text || 'Button';
        
        if (config.disabled) {
            button.classList.add('disabled');
            button.disabled = true;
        }
        
        if (config.onClick) {
            button.addEventListener('click', config.onClick);
        }
        
        if (config.style) {
            Object.assign(button.style, config.style);
        }
        
        return button;
    }

    /**
     * 创建滑块组件
     */
    createSlider(config) {
        const container = document.createElement('div');
        container.className = 'ui-component ui-slider';
        
        const track = document.createElement('div');
        track.className = 'ui-slider-track';
        
        const thumb = document.createElement('div');
        thumb.className = 'ui-slider-thumb';
        
        container.appendChild(track);
        container.appendChild(thumb);
        
        // 滑块逻辑
        const min = config.min || 0;
        const max = config.max || 100;
        let value = config.value || min;
        
        const updateSlider = (newValue) => {
            value = Math.max(min, Math.min(max, newValue));
            const percentage = (value - min) / (max - min) * 100;
            track.style.width = percentage + '%';
            thumb.style.left = percentage + '%';
            
            if (config.onChange) {
                config.onChange(value);
            }
        };
        
        // 鼠标事件
        let isDragging = false;
        
        const handleMouseMove = (e) => {
            if (!isDragging) return;
            
            const rect = container.getBoundingClientRect();
            const percentage = (e.clientX - rect.left) / rect.width;
            const newValue = min + percentage * (max - min);
            updateSlider(newValue);
        };
        
        thumb.addEventListener('mousedown', () => {
            isDragging = true;
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', () => {
                isDragging = false;
                document.removeEventListener('mousemove', handleMouseMove);
            }, { once: true });
        });
        
        container.addEventListener('click', (e) => {
            if (e.target === thumb) return;
            
            const rect = container.getBoundingClientRect();
            const percentage = (e.clientX - rect.left) / rect.width;
            const newValue = min + percentage * (max - min);
            updateSlider(newValue);
        });
        
        // 初始化
        updateSlider(value);
        
        // 添加方法
        container.setValue = updateSlider;
        container.getValue = () => value;
        
        return container;
    }

    /**
     * 创建进度条组件
     */
    createProgressBar(config) {
        const container = document.createElement('div');
        container.className = 'ui-component ui-progress-bar';
        
        const fill = document.createElement('div');
        fill.className = 'ui-progress-fill';
        
        container.appendChild(fill);
        
        const setProgress = (percentage) => {
            fill.style.width = Math.max(0, Math.min(100, percentage)) + '%';
        };
        
        // 初始化
        setProgress(config.value || 0);
        
        // 添加方法
        container.setProgress = setProgress;
        
        return container;
    }

    /**
     * 创建模态框组件
     */
    createModal(config) {
        const modal = document.createElement('div');
        modal.className = 'ui-component ui-modal';
        
        const content = document.createElement('div');
        content.className = 'ui-modal-content';
        content.innerHTML = config.content || '';
        
        modal.appendChild(content);
        
        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.hide();
            }
        });
        
        // 添加方法
        modal.show = () => {
            modal.classList.add('active');
            document.body.appendChild(modal);
        };
        
        modal.hide = () => {
            modal.classList.remove('active');
            setTimeout(() => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
            }, 300);
        };
        
        return modal;
    }

    /**
     * 创建通知组件
     */
    createNotification(config) {
        const notification = document.createElement('div');
        notification.className = 'ui-component ui-notification';
        notification.textContent = config.message || '';
        
        // 自动隐藏
        const duration = config.duration || 3000;
        
        const show = () => {
            document.body.appendChild(notification);
            setTimeout(() => {
                notification.classList.add('show');
            }, 10);
            
            setTimeout(() => {
                hide();
            }, duration);
        };
        
        const hide = () => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        };
        
        // 添加方法
        notification.show = show;
        notification.hide = hide;
        
        return notification;
    }

    /**
     * 创建加载器组件
     */
    createLoader(config) {
        const loader = document.createElement('div');
        loader.className = 'ui-component ui-loader';
        
        if (config.size) {
            loader.style.width = config.size + 'px';
            loader.style.height = config.size + 'px';
        }
        
        return loader;
    }

    /**
     * 创建组件
     */
    create(componentName, config = {}) {
        const factory = this.components.get(componentName);
        if (factory) {
            return factory(config);
        } else {
            console.error(`❌ 未知组件类型: ${componentName}`);
            return null;
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, duration = 3000) {
        const notification = this.create('notification', { message, duration });
        notification.show();
        return notification;
    }

    /**
     * 显示模态框
     */
    showModal(content) {
        const modal = this.create('modal', { content });
        modal.show();
        return modal;
    }

    /**
     * 设置主题
     */
    setTheme(themeName) {
        if (this.themes[themeName]) {
            this.currentTheme = themeName;
            this.applyTheme(this.themes[themeName]);
        }
    }

    /**
     * 应用主题
     */
    applyTheme(theme) {
        const root = document.documentElement;
        Object.entries(theme).forEach(([key, value]) => {
            root.style.setProperty(`--ui-${key}`, value);
        });
    }

    /**
     * 添加动画
     */
    addAnimation(name, keyframes, options = {}) {
        this.animations.set(name, { keyframes, options });
    }

    /**
     * 播放动画
     */
    animate(element, animationName, options = {}) {
        const animation = this.animations.get(animationName);
        if (animation && element.animate) {
            return element.animate(animation.keyframes, {
                ...animation.options,
                ...options
            });
        }
        return null;
    }

    /**
     * 销毁UI组件库
     */
    destroy() {
        this.components.clear();
        this.animations.clear();
        console.log('🎨 UI组件库已销毁');
    }
}

// 导出UI组件库类
window.UIComponents = UIComponents;
