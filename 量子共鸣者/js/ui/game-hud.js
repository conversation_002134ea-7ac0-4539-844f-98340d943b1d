/**
 * 量子共鸣者 - 游戏HUD组件
 * 负责游戏中的抬头显示界面，包括分数、能量、频率等信息
 */

class GameHUD {
    constructor() {
        this.isVisible = false;
        this.isInitialized = false;
        
        // HUD元素引用
        this.elements = {
            container: null,
            scoreDisplay: null,
            energyBar: null,
            energyValue: null,
            frequencyDisplay: null,
            comboCounter: null,
            comboMultiplier: null,
            timeDisplay: null,
            particleCounter: null,
            levelInfo: null,
            pauseButton: null,
            settingsButton: null
        };
        
        // 游戏数据
        this.gameData = {
            score: 0,
            energy: 100,
            maxEnergy: 100,
            frequency: 440,
            combo: 0,
            maxCombo: 0,
            multiplier: 1.0,
            time: 0,
            particleCount: 0,
            level: 1,
            levelName: '量子启蒙'
        };
        
        // 动画状态
        this.animations = {
            scoreCounter: null,
            energyPulse: null,
            comboFlash: null,
            frequencyWave: null
        };
        
        // 更新频率控制
        this.updateInterval = null;
        this.updateFrequency = 60; // FPS
        
        console.log('🎮 游戏HUD组件已创建');
    }

    /**
     * 初始化HUD
     */
    init() {
        try {
            // 获取HUD元素
            this.getHUDElements();
            
            // 创建HUD结构（如果不存在）
            this.createHUDStructure();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 初始化动画
            this.initAnimations();
            
            // 开始更新循环
            this.startUpdateLoop();
            
            this.isInitialized = true;
            console.log('✅ 游戏HUD初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 游戏HUD初始化失败:', error);
            return false;
        }
    }

    /**
     * 获取HUD元素
     */
    getHUDElements() {
        this.elements.container = document.getElementById('gameHUD');
        this.elements.scoreDisplay = document.getElementById('scoreDisplay');
        this.elements.energyBar = document.getElementById('energyBar');
        this.elements.energyValue = document.getElementById('energyValue');
        this.elements.frequencyDisplay = document.getElementById('frequencyDisplay');
        this.elements.comboCounter = document.getElementById('comboCounter');
        this.elements.comboMultiplier = document.getElementById('comboMultiplier');
        this.elements.timeDisplay = document.getElementById('timeDisplay');
        this.elements.particleCounter = document.getElementById('particleCounter');
        this.elements.levelInfo = document.getElementById('levelInfo');
        this.elements.pauseButton = document.getElementById('pauseButton');
        this.elements.settingsButton = document.getElementById('settingsButton');
    }

    /**
     * 创建HUD结构
     */
    createHUDStructure() {
        if (!this.elements.container) {
            console.warn('⚠️ HUD容器不存在，跳过HUD结构创建');
            return;
        }

        // 如果HUD元素不存在，创建它们
        if (!this.elements.scoreDisplay) {
            this.createScoreDisplay();
        }
        
        if (!this.elements.energyBar) {
            this.createEnergyBar();
        }
        
        if (!this.elements.frequencyDisplay) {
            this.createFrequencyDisplay();
        }
        
        if (!this.elements.comboCounter) {
            this.createComboCounter();
        }
        
        if (!this.elements.timeDisplay) {
            this.createTimeDisplay();
        }
        
        if (!this.elements.particleCounter) {
            this.createParticleCounter();
        }
        
        if (!this.elements.levelInfo) {
            this.createLevelInfo();
        }
        
        if (!this.elements.pauseButton) {
            this.createControlButtons();
        }
    }

    /**
     * 创建分数显示
     */
    createScoreDisplay() {
        const scoreContainer = document.createElement('div');
        scoreContainer.className = 'hud-score-container';
        scoreContainer.innerHTML = `
            <div class="hud-label">分数</div>
            <div id="scoreDisplay" class="hud-score">0</div>
        `;
        this.elements.container.appendChild(scoreContainer);
        this.elements.scoreDisplay = document.getElementById('scoreDisplay');
    }

    /**
     * 创建能量条
     */
    createEnergyBar() {
        const energyContainer = document.createElement('div');
        energyContainer.className = 'hud-energy-container';
        energyContainer.innerHTML = `
            <div class="hud-label">量子能量</div>
            <div class="energy-bar-wrapper">
                <div id="energyBar" class="energy-bar">
                    <div class="energy-fill"></div>
                    <div class="energy-glow"></div>
                </div>
                <div id="energyValue" class="energy-value">100%</div>
            </div>
        `;
        this.elements.container.appendChild(energyContainer);
        this.elements.energyBar = document.getElementById('energyBar');
        this.elements.energyValue = document.getElementById('energyValue');
    }

    /**
     * 创建频率显示
     */
    createFrequencyDisplay() {
        const frequencyContainer = document.createElement('div');
        frequencyContainer.className = 'hud-frequency-container';
        frequencyContainer.innerHTML = `
            <div class="hud-label">共鸣频率</div>
            <div id="frequencyDisplay" class="hud-frequency">
                <span class="frequency-value">440</span>
                <span class="frequency-unit">Hz</span>
                <div class="frequency-wave"></div>
            </div>
        `;
        this.elements.container.appendChild(frequencyContainer);
        this.elements.frequencyDisplay = document.getElementById('frequencyDisplay');
    }

    /**
     * 创建连击计数器
     */
    createComboCounter() {
        const comboContainer = document.createElement('div');
        comboContainer.className = 'hud-combo-container';
        comboContainer.innerHTML = `
            <div class="hud-label">连击</div>
            <div class="combo-wrapper">
                <div id="comboCounter" class="hud-combo">0</div>
                <div id="comboMultiplier" class="combo-multiplier">×1.0</div>
            </div>
        `;
        this.elements.container.appendChild(comboContainer);
        this.elements.comboCounter = document.getElementById('comboCounter');
        this.elements.comboMultiplier = document.getElementById('comboMultiplier');
    }

    /**
     * 创建时间显示
     */
    createTimeDisplay() {
        const timeContainer = document.createElement('div');
        timeContainer.className = 'hud-time-container';
        timeContainer.innerHTML = `
            <div class="hud-label">时间</div>
            <div id="timeDisplay" class="hud-time">00:00</div>
        `;
        this.elements.container.appendChild(timeContainer);
        this.elements.timeDisplay = document.getElementById('timeDisplay');
    }

    /**
     * 创建粒子计数器
     */
    createParticleCounter() {
        const particleContainer = document.createElement('div');
        particleContainer.className = 'hud-particle-container';
        particleContainer.innerHTML = `
            <div class="hud-label">活跃粒子</div>
            <div id="particleCounter" class="hud-particle">0</div>
        `;
        this.elements.container.appendChild(particleContainer);
        this.elements.particleCounter = document.getElementById('particleCounter');
    }

    /**
     * 创建关卡信息
     */
    createLevelInfo() {
        const levelContainer = document.createElement('div');
        levelContainer.className = 'hud-level-container';
        levelContainer.innerHTML = `
            <div id="levelInfo" class="hud-level">
                <div class="level-number">关卡 1</div>
                <div class="level-name">量子启蒙</div>
            </div>
        `;
        this.elements.container.appendChild(levelContainer);
        this.elements.levelInfo = document.getElementById('levelInfo');
    }

    /**
     * 创建控制按钮
     */
    createControlButtons() {
        const controlContainer = document.createElement('div');
        controlContainer.className = 'hud-controls';
        controlContainer.innerHTML = `
            <button id="pauseButton" class="hud-button pause-button" title="暂停游戏">
                <i class="icon-pause"></i>
            </button>
            <button id="settingsButton" class="hud-button settings-button" title="设置">
                <i class="icon-settings"></i>
            </button>
        `;
        this.elements.container.appendChild(controlContainer);
        this.elements.pauseButton = document.getElementById('pauseButton');
        this.elements.settingsButton = document.getElementById('settingsButton');
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 暂停按钮
        if (this.elements.pauseButton) {
            this.elements.pauseButton.addEventListener('click', () => {
                this.onPauseClick();
            });
        }
        
        // 设置按钮
        if (this.elements.settingsButton) {
            this.elements.settingsButton.addEventListener('click', () => {
                this.onSettingsClick();
            });
        }
        
        // 能量条点击
        if (this.elements.energyBar) {
            this.elements.energyBar.addEventListener('click', () => {
                this.onEnergyBarClick();
            });
        }
        
        // 频率显示点击
        if (this.elements.frequencyDisplay) {
            this.elements.frequencyDisplay.addEventListener('click', () => {
                this.onFrequencyClick();
            });
        }
    }

    /**
     * 初始化动画
     */
    initAnimations() {
        // 分数计数动画
        this.animations.scoreCounter = {
            current: 0,
            target: 0,
            speed: 0.1
        };
        
        // 能量脉冲动画
        this.animations.energyPulse = {
            phase: 0,
            speed: 0.05
        };
        
        // 连击闪烁动画
        this.animations.comboFlash = {
            active: false,
            duration: 0
        };
        
        // 频率波形动画
        this.animations.frequencyWave = {
            phase: 0,
            amplitude: 1.0,
            frequency: 1.0
        };
    }

    /**
     * 开始更新循环
     */
    startUpdateLoop() {
        this.updateInterval = setInterval(() => {
            this.updateAnimations();
            this.updateDisplay();
        }, 1000 / this.updateFrequency);
    }

    /**
     * 更新动画
     */
    updateAnimations() {
        // 更新分数计数动画
        if (this.animations.scoreCounter.current !== this.animations.scoreCounter.target) {
            const diff = this.animations.scoreCounter.target - this.animations.scoreCounter.current;
            this.animations.scoreCounter.current += diff * this.animations.scoreCounter.speed;
            
            if (Math.abs(diff) < 1) {
                this.animations.scoreCounter.current = this.animations.scoreCounter.target;
            }
        }
        
        // 更新能量脉冲动画
        this.animations.energyPulse.phase += this.animations.energyPulse.speed;
        if (this.animations.energyPulse.phase > Math.PI * 2) {
            this.animations.energyPulse.phase = 0;
        }
        
        // 更新连击闪烁动画
        if (this.animations.comboFlash.active) {
            this.animations.comboFlash.duration -= 1000 / this.updateFrequency;
            if (this.animations.comboFlash.duration <= 0) {
                this.animations.comboFlash.active = false;
            }
        }
        
        // 更新频率波形动画
        this.animations.frequencyWave.phase += 0.1;
        if (this.animations.frequencyWave.phase > Math.PI * 2) {
            this.animations.frequencyWave.phase = 0;
        }
    }

    /**
     * 更新显示
     */
    updateDisplay() {
        // 更新分数显示
        if (this.elements.scoreDisplay) {
            this.elements.scoreDisplay.textContent = Math.floor(this.animations.scoreCounter.current).toLocaleString();
        }
        
        // 更新能量条
        this.updateEnergyBar();
        
        // 更新频率显示
        this.updateFrequencyDisplay();
        
        // 更新连击显示
        this.updateComboDisplay();
        
        // 更新时间显示
        this.updateTimeDisplay();
        
        // 更新粒子计数
        this.updateParticleCounter();
    }

    /**
     * 更新能量条
     */
    updateEnergyBar() {
        if (!this.elements.energyBar) return;
        
        const percentage = (this.gameData.energy / this.gameData.maxEnergy) * 100;
        const fill = this.elements.energyBar.querySelector('.energy-fill');
        const glow = this.elements.energyBar.querySelector('.energy-glow');
        
        if (fill) {
            fill.style.width = `${percentage}%`;
            
            // 根据能量水平改变颜色
            if (percentage > 70) {
                fill.style.background = 'linear-gradient(90deg, #00ff00, #00ffff)';
            } else if (percentage > 30) {
                fill.style.background = 'linear-gradient(90deg, #ffff00, #00ffff)';
            } else {
                fill.style.background = 'linear-gradient(90deg, #ff0000, #ff8800)';
            }
        }
        
        if (glow) {
            const pulseIntensity = 0.5 + 0.5 * Math.sin(this.animations.energyPulse.phase);
            glow.style.opacity = pulseIntensity * (percentage / 100);
        }
        
        if (this.elements.energyValue) {
            this.elements.energyValue.textContent = `${Math.round(percentage)}%`;
        }
    }

    /**
     * 更新频率显示
     */
    updateFrequencyDisplay() {
        if (!this.elements.frequencyDisplay) return;
        
        const valueElement = this.elements.frequencyDisplay.querySelector('.frequency-value');
        const waveElement = this.elements.frequencyDisplay.querySelector('.frequency-wave');
        
        if (valueElement) {
            valueElement.textContent = Math.round(this.gameData.frequency);
        }
        
        if (waveElement) {
            const waveHeight = 0.5 + 0.5 * Math.sin(this.animations.frequencyWave.phase);
            waveElement.style.height = `${waveHeight * 20}px`;
        }
    }

    /**
     * 更新连击显示
     */
    updateComboDisplay() {
        if (this.elements.comboCounter) {
            this.elements.comboCounter.textContent = this.gameData.combo;
            
            // 连击闪烁效果
            if (this.animations.comboFlash.active) {
                this.elements.comboCounter.classList.add('combo-flash');
            } else {
                this.elements.comboCounter.classList.remove('combo-flash');
            }
        }
        
        if (this.elements.comboMultiplier) {
            this.elements.comboMultiplier.textContent = `×${this.gameData.multiplier.toFixed(1)}`;
        }
    }

    /**
     * 更新时间显示
     */
    updateTimeDisplay() {
        if (!this.elements.timeDisplay) return;
        
        const minutes = Math.floor(this.gameData.time / 60);
        const seconds = Math.floor(this.gameData.time % 60);
        this.elements.timeDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    /**
     * 更新粒子计数
     */
    updateParticleCounter() {
        if (this.elements.particleCounter) {
            this.elements.particleCounter.textContent = this.gameData.particleCount;
        }
    }

    /**
     * 更新游戏数据
     * @param {Object} data - 游戏数据
     */
    updateGameData(data) {
        Object.assign(this.gameData, data);
        
        // 触发特殊动画
        if (data.score !== undefined && data.score > this.animations.scoreCounter.target) {
            this.animations.scoreCounter.target = data.score;
            this.triggerScoreAnimation();
        }
        
        if (data.combo !== undefined && data.combo > this.gameData.combo) {
            this.triggerComboAnimation();
        }
    }

    /**
     * 触发分数动画
     */
    triggerScoreAnimation() {
        if (this.elements.scoreDisplay) {
            this.elements.scoreDisplay.classList.add('score-increase');
            setTimeout(() => {
                this.elements.scoreDisplay.classList.remove('score-increase');
            }, 500);
        }
    }

    /**
     * 触发连击动画
     */
    triggerComboAnimation() {
        this.animations.comboFlash.active = true;
        this.animations.comboFlash.duration = 300;
        
        if (window.audioManager) {
            audioManager.playUISound('combo');
        }
    }

    /**
     * 暂停按钮点击事件
     */
    onPauseClick() {
        if (window.gameController) {
            gameController.pauseGame();
        }
        
        if (window.uiManager) {
            uiManager.showScreen('pause-screen');
        }
    }

    /**
     * 设置按钮点击事件
     */
    onSettingsClick() {
        if (window.uiManager) {
            uiManager.showScreen('settings-screen');
        }
    }

    /**
     * 能量条点击事件
     */
    onEnergyBarClick() {
        // 可以添加能量相关的交互
        console.log('🔋 能量条被点击');
    }

    /**
     * 频率显示点击事件
     */
    onFrequencyClick() {
        // 可以添加频率调节的交互
        console.log('🎵 频率显示被点击');
    }

    /**
     * 显示HUD
     */
    show() {
        if (this.elements.container) {
            this.elements.container.style.display = 'flex';
            this.elements.container.classList.add('hud-enter');
            
            setTimeout(() => {
                this.elements.container.classList.remove('hud-enter');
            }, 300);
        }
        
        this.isVisible = true;
    }

    /**
     * 隐藏HUD
     */
    hide() {
        if (this.elements.container) {
            this.elements.container.classList.add('hud-exit');
            
            setTimeout(() => {
                this.elements.container.style.display = 'none';
                this.elements.container.classList.remove('hud-exit');
            }, 300);
        }
        
        this.isVisible = false;
    }

    /**
     * 销毁HUD
     */
    destroy() {
        // 停止更新循环
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        
        // 清理动画
        Object.keys(this.animations).forEach(key => {
            this.animations[key] = null;
        });
        
        // 清理元素引用
        Object.keys(this.elements).forEach(key => {
            this.elements[key] = null;
        });
        
        this.isInitialized = false;
        console.log('🎮 游戏HUD已销毁');
    }
}

// 创建全局游戏HUD实例
window.gameHUD = new GameHUD();
