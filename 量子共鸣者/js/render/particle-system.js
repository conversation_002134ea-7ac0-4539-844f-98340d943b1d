/**
 * 量子共鸣者 - 2D粒子系统
 * 提供2D粒子效果和动画
 */

class ParticleSystem {
    constructor(renderer) {
        this.renderer = renderer;
        this.particles = [];
        this.emitters = [];
        this.maxParticles = 1000;
        this.isActive = true;
        
        // 粒子池用于性能优化
        this.particlePool = [];
        this.poolSize = 500;
        
        this.init();
    }

    /**
     * 初始化粒子系统
     */
    init() {
        // 预创建粒子池
        this.createParticlePool();
        console.log('✨ 2D粒子系统初始化完成');
    }

    /**
     * 创建粒子池
     */
    createParticlePool() {
        for (let i = 0; i < this.poolSize; i++) {
            this.particlePool.push(this.createParticle());
        }
    }

    /**
     * 创建粒子
     */
    createParticle() {
        return {
            x: 0,
            y: 0,
            vx: 0,
            vy: 0,
            ax: 0,
            ay: 0,
            size: 1,
            color: '#ffffff',
            alpha: 1,
            life: 1,
            maxLife: 1,
            rotation: 0,
            rotationSpeed: 0,
            active: false,
            type: 'default'
        };
    }

    /**
     * 从池中获取粒子
     */
    getParticleFromPool() {
        for (let particle of this.particlePool) {
            if (!particle.active) {
                particle.active = true;
                return particle;
            }
        }
        
        // 如果池中没有可用粒子，创建新的
        if (this.particles.length < this.maxParticles) {
            return this.createParticle();
        }
        
        return null;
    }

    /**
     * 将粒子返回池中
     */
    returnParticleToPool(particle) {
        particle.active = false;
        particle.life = 0;
    }

    /**
     * 添加粒子发射器
     */
    addEmitter(config) {
        const emitter = {
            x: config.x || 0,
            y: config.y || 0,
            rate: config.rate || 10, // 每秒发射粒子数
            particleConfig: config.particleConfig || {},
            active: true,
            lastEmitTime: 0,
            ...config
        };
        
        this.emitters.push(emitter);
        return emitter;
    }

    /**
     * 移除粒子发射器
     */
    removeEmitter(emitter) {
        const index = this.emitters.indexOf(emitter);
        if (index > -1) {
            this.emitters.splice(index, 1);
        }
    }

    /**
     * 发射粒子
     */
    emit(x, y, config = {}) {
        const particle = this.getParticleFromPool();
        if (!particle) return null;
        
        // 设置粒子属性
        particle.x = x + (config.offsetX || 0);
        particle.y = y + (config.offsetY || 0);
        particle.vx = config.velocityX || (Math.random() - 0.5) * 100;
        particle.vy = config.velocityY || (Math.random() - 0.5) * 100;
        particle.ax = config.accelerationX || 0;
        particle.ay = config.accelerationY || 0;
        particle.size = config.size || Math.random() * 3 + 1;
        particle.color = config.color || `hsl(${Math.random() * 360}, 70%, 60%)`;
        particle.alpha = config.alpha || 1;
        particle.life = config.life || 1;
        particle.maxLife = particle.life;
        particle.rotation = config.rotation || 0;
        particle.rotationSpeed = config.rotationSpeed || (Math.random() - 0.5) * 0.1;
        particle.type = config.type || 'default';
        particle.active = true;
        
        this.particles.push(particle);
        return particle;
    }

    /**
     * 创建爆炸效果
     */
    createExplosion(x, y, count = 20, config = {}) {
        for (let i = 0; i < count; i++) {
            const angle = (Math.PI * 2 * i) / count;
            const speed = config.speed || 50 + Math.random() * 100;
            
            this.emit(x, y, {
                velocityX: Math.cos(angle) * speed,
                velocityY: Math.sin(angle) * speed,
                size: config.size || Math.random() * 4 + 2,
                color: config.color || `hsl(${Math.random() * 60 + 15}, 100%, 60%)`,
                life: config.life || 0.5 + Math.random() * 0.5,
                accelerationY: config.gravity || 50,
                type: 'explosion'
            });
        }
    }

    /**
     * 创建火花效果
     */
    createSparks(x, y, count = 10, config = {}) {
        for (let i = 0; i < count; i++) {
            this.emit(x, y, {
                velocityX: (Math.random() - 0.5) * 200,
                velocityY: (Math.random() - 0.5) * 200,
                size: config.size || Math.random() * 2 + 1,
                color: config.color || '#ffff00',
                life: config.life || 0.3 + Math.random() * 0.3,
                type: 'spark'
            });
        }
    }

    /**
     * 创建量子共鸣效果
     */
    createQuantumResonance(x, y, frequency = 440) {
        const count = Math.floor(frequency / 50) + 5;
        const hue = (frequency - 20) / (20000 - 20) * 300; // 频率映射到色相
        
        for (let i = 0; i < count; i++) {
            const angle = (Math.PI * 2 * i) / count;
            const radius = 30 + Math.sin(Date.now() * 0.01 + i) * 20;
            
            this.emit(
                x + Math.cos(angle) * radius,
                y + Math.sin(angle) * radius,
                {
                    velocityX: Math.cos(angle) * 20,
                    velocityY: Math.sin(angle) * 20,
                    size: 2 + Math.sin(Date.now() * 0.02 + i) * 1,
                    color: `hsl(${hue}, 80%, 70%)`,
                    life: 1,
                    alpha: 0.8,
                    type: 'resonance'
                }
            );
        }
    }

    /**
     * 更新粒子系统
     */
    update(deltaTime) {
        if (!this.isActive) return;
        
        const dt = deltaTime / 1000; // 转换为秒
        
        // 更新发射器
        this.updateEmitters(dt);
        
        // 更新粒子
        this.updateParticles(dt);
        
        // 清理死亡粒子
        this.cleanupParticles();
    }

    /**
     * 更新发射器
     */
    updateEmitters(deltaTime) {
        const currentTime = Date.now();
        
        for (let emitter of this.emitters) {
            if (!emitter.active) continue;
            
            const timeSinceLastEmit = currentTime - emitter.lastEmitTime;
            const emitInterval = 1000 / emitter.rate; // 毫秒
            
            if (timeSinceLastEmit >= emitInterval) {
                this.emit(emitter.x, emitter.y, emitter.particleConfig);
                emitter.lastEmitTime = currentTime;
            }
        }
    }

    /**
     * 更新粒子
     */
    updateParticles(deltaTime) {
        for (let particle of this.particles) {
            if (!particle.active) continue;
            
            // 更新生命值
            particle.life -= deltaTime;
            if (particle.life <= 0) {
                this.returnParticleToPool(particle);
                continue;
            }
            
            // 更新物理属性
            particle.vx += particle.ax * deltaTime;
            particle.vy += particle.ay * deltaTime;
            particle.x += particle.vx * deltaTime;
            particle.y += particle.vy * deltaTime;
            particle.rotation += particle.rotationSpeed * deltaTime;
            
            // 更新透明度（基于生命值）
            particle.alpha = particle.life / particle.maxLife;
            
            // 特殊类型的粒子更新
            this.updateSpecialParticles(particle, deltaTime);
        }
    }

    /**
     * 更新特殊类型粒子
     */
    updateSpecialParticles(particle, deltaTime) {
        switch (particle.type) {
            case 'resonance':
                // 量子共鸣粒子的特殊行为
                particle.size = 2 + Math.sin(Date.now() * 0.02) * 1;
                break;
                
            case 'explosion':
                // 爆炸粒子逐渐减小
                particle.size *= 0.98;
                break;
                
            case 'spark':
                // 火花粒子快速衰减
                particle.alpha *= 0.95;
                break;
        }
    }

    /**
     * 清理死亡粒子
     */
    cleanupParticles() {
        this.particles = this.particles.filter(particle => particle.active);
    }

    /**
     * 渲染粒子系统
     */
    render() {
        if (!this.renderer || !this.isActive) return;
        
        for (let particle of this.particles) {
            if (particle.active && particle.alpha > 0) {
                this.renderer.drawParticle(particle);
            }
        }
    }

    /**
     * 清空所有粒子
     */
    clear() {
        for (let particle of this.particles) {
            this.returnParticleToPool(particle);
        }
        this.particles = [];
    }

    /**
     * 暂停粒子系统
     */
    pause() {
        this.isActive = false;
    }

    /**
     * 恢复粒子系统
     */
    resume() {
        this.isActive = true;
    }

    /**
     * 获取粒子统计信息
     */
    getStats() {
        return {
            activeParticles: this.particles.filter(p => p.active).length,
            totalParticles: this.particles.length,
            emitters: this.emitters.length,
            poolSize: this.particlePool.length,
            isActive: this.isActive
        };
    }

    /**
     * 销毁粒子系统
     */
    destroy() {
        this.clear();
        this.emitters = [];
        this.particlePool = [];
        this.isActive = false;
        console.log('✨ 2D粒子系统已销毁');
    }
}

// 导出粒子系统类
window.ParticleSystem = ParticleSystem;
