/**
 * 量子共鸣者 - WebGL 3D渲染器
 * 基于WebGL的高性能3D渲染系统
 */

class WebGLRenderer {
    constructor() {
        this.canvas = null;
        this.gl = null;
        this.isInitialized = false;
        
        // 着色器程序
        this.shaderPrograms = new Map();
        this.currentProgram = null;
        
        // 缓冲区
        this.buffers = new Map();
        this.textures = new Map();
        
        // 3D相机
        this.camera = {
            position: [0, 0, 5],
            target: [0, 0, 0],
            up: [0, 1, 0],
            fov: 45,
            near: 0.1,
            far: 100.0
        };
        
        // 变换矩阵
        this.matrices = {
            model: this.createIdentityMatrix(),
            view: this.createIdentityMatrix(),
            projection: this.createIdentityMatrix(),
            mvp: this.createIdentityMatrix()
        };
        
        // 光照设置
        this.lighting = {
            ambient: [0.2, 0.2, 0.4, 1.0],
            directional: {
                direction: [0.5, -1.0, -0.5],
                color: [1.0, 1.0, 1.0, 1.0]
            },
            point: {
                position: [0, 0, 5],
                color: [0.0, 1.0, 1.0, 1.0],
                intensity: 1.0
            }
        };
        
        // 渲染统计
        this.stats = {
            drawCalls: 0,
            vertices: 0,
            triangles: 0
        };
        
        console.log('🌌 WebGL 3D渲染器已创建');
    }

    /**
     * 初始化WebGL渲染器
     * @param {HTMLCanvasElement} canvas - Canvas元素
     */
    async init(canvas) {
        try {
            this.canvas = canvas;
            
            // 获取WebGL上下文
            this.gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
            
            if (!this.gl) {
                throw new Error('WebGL不受支持');
            }
            
            // 设置WebGL状态
            this.setupWebGLState();
            
            // 编译着色器
            await this.compileShaders();
            
            // 创建基础几何体
            this.createBasicGeometry();
            
            // 设置相机
            this.updateCamera();
            
            this.isInitialized = true;
            console.log('✅ WebGL 3D渲染器初始化完成');
            
            return true;
        } catch (error) {
            console.error('❌ WebGL 3D渲染器初始化失败:', error);
            return false;
        }
    }

    /**
     * 设置WebGL状态
     */
    setupWebGLState() {
        const gl = this.gl;
        
        // 启用深度测试
        gl.enable(gl.DEPTH_TEST);
        gl.depthFunc(gl.LEQUAL);
        
        // 启用混合
        gl.enable(gl.BLEND);
        gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);
        
        // 启用面剔除
        gl.enable(gl.CULL_FACE);
        gl.cullFace(gl.BACK);
        
        // 设置清除颜色
        gl.clearColor(0.04, 0.06, 0.14, 1.0);
        
        // 设置视口
        this.resize();
    }

    /**
     * 编译着色器程序
     */
    async compileShaders() {
        // 粒子着色器
        const particleVertexShader = `
            attribute vec3 a_position;
            attribute vec3 a_color;
            attribute float a_size;
            attribute float a_alpha;
            
            uniform mat4 u_mvpMatrix;
            uniform float u_time;
            uniform vec3 u_cameraPosition;
            
            varying vec3 v_color;
            varying float v_alpha;
            varying float v_distance;
            
            void main() {
                gl_Position = u_mvpMatrix * vec4(a_position, 1.0);
                gl_PointSize = a_size * (1.0 + sin(u_time * 2.0 + a_position.x) * 0.2);
                
                v_color = a_color;
                v_alpha = a_alpha;
                v_distance = distance(a_position, u_cameraPosition);
            }
        `;
        
        const particleFragmentShader = `
            precision mediump float;
            
            varying vec3 v_color;
            varying float v_alpha;
            varying float v_distance;
            
            uniform float u_time;
            
            void main() {
                vec2 center = gl_PointCoord - 0.5;
                float dist = length(center);
                
                if (dist > 0.5) {
                    discard;
                }
                
                // 创建发光效果
                float glow = 1.0 - smoothstep(0.0, 0.5, dist);
                glow = pow(glow, 2.0);
                
                // 添加脉动效果
                float pulse = 0.8 + 0.2 * sin(u_time * 3.0);
                
                // 距离衰减
                float distanceFade = 1.0 / (1.0 + v_distance * 0.1);
                
                vec3 finalColor = v_color * glow * pulse * distanceFade;
                float finalAlpha = v_alpha * glow * distanceFade;
                
                gl_FragColor = vec4(finalColor, finalAlpha);
            }
        `;
        
        // 连接线着色器
        const connectionVertexShader = `
            attribute vec3 a_position;
            attribute vec3 a_color;
            attribute float a_alpha;
            
            uniform mat4 u_mvpMatrix;
            uniform float u_time;
            
            varying vec3 v_color;
            varying float v_alpha;
            varying float v_progress;
            
            void main() {
                gl_Position = u_mvpMatrix * vec4(a_position, 1.0);
                
                v_color = a_color;
                v_alpha = a_alpha;
                v_progress = (a_position.z + 5.0) / 10.0; // 假设z范围是-5到5
            }
        `;
        
        const connectionFragmentShader = `
            precision mediump float;
            
            varying vec3 v_color;
            varying float v_alpha;
            varying float v_progress;
            
            uniform float u_time;
            
            void main() {
                // 创建流动效果
                float flow = sin(v_progress * 10.0 - u_time * 5.0) * 0.5 + 0.5;
                
                vec3 finalColor = v_color * (0.5 + flow * 0.5);
                float finalAlpha = v_alpha * flow;
                
                gl_FragColor = vec4(finalColor, finalAlpha);
            }
        `;
        
        // 编译并创建着色器程序
        this.shaderPrograms.set('particle', this.createShaderProgram(particleVertexShader, particleFragmentShader));
        this.shaderPrograms.set('connection', this.createShaderProgram(connectionVertexShader, connectionFragmentShader));
    }

    /**
     * 创建着色器程序
     * @param {string} vertexSource - 顶点着色器源码
     * @param {string} fragmentSource - 片段着色器源码
     * @returns {WebGLProgram} 着色器程序
     */
    createShaderProgram(vertexSource, fragmentSource) {
        const gl = this.gl;
        
        // 编译顶点着色器
        const vertexShader = this.compileShader(gl.VERTEX_SHADER, vertexSource);
        if (!vertexShader) return null;
        
        // 编译片段着色器
        const fragmentShader = this.compileShader(gl.FRAGMENT_SHADER, fragmentSource);
        if (!fragmentShader) return null;
        
        // 创建程序
        const program = gl.createProgram();
        gl.attachShader(program, vertexShader);
        gl.attachShader(program, fragmentShader);
        gl.linkProgram(program);
        
        // 检查链接状态
        if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
            console.error('着色器程序链接失败:', gl.getProgramInfoLog(program));
            gl.deleteProgram(program);
            return null;
        }
        
        // 清理着色器
        gl.deleteShader(vertexShader);
        gl.deleteShader(fragmentShader);
        
        return program;
    }

    /**
     * 编译着色器
     * @param {number} type - 着色器类型
     * @param {string} source - 着色器源码
     * @returns {WebGLShader} 编译后的着色器
     */
    compileShader(type, source) {
        const gl = this.gl;
        const shader = gl.createShader(type);
        
        gl.shaderSource(shader, source);
        gl.compileShader(shader);
        
        if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
            console.error('着色器编译失败:', gl.getShaderInfoLog(shader));
            gl.deleteShader(shader);
            return null;
        }
        
        return shader;
    }

    /**
     * 创建基础几何体
     */
    createBasicGeometry() {
        // 创建粒子缓冲区
        this.createParticleBuffers();
        
        // 创建连接线缓冲区
        this.createConnectionBuffers();
    }

    /**
     * 创建粒子缓冲区
     */
    createParticleBuffers() {
        const gl = this.gl;
        
        // 位置缓冲区
        const positionBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
        
        // 颜色缓冲区
        const colorBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
        
        // 大小缓冲区
        const sizeBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, sizeBuffer);
        
        // 透明度缓冲区
        const alphaBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, alphaBuffer);
        
        this.buffers.set('particlePosition', positionBuffer);
        this.buffers.set('particleColor', colorBuffer);
        this.buffers.set('particleSize', sizeBuffer);
        this.buffers.set('particleAlpha', alphaBuffer);
    }

    /**
     * 创建连接线缓冲区
     */
    createConnectionBuffers() {
        const gl = this.gl;
        
        // 位置缓冲区
        const positionBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
        
        // 颜色缓冲区
        const colorBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
        
        // 透明度缓冲区
        const alphaBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, alphaBuffer);
        
        this.buffers.set('connectionPosition', positionBuffer);
        this.buffers.set('connectionColor', colorBuffer);
        this.buffers.set('connectionAlpha', alphaBuffer);
    }

    /**
     * 创建单位矩阵
     * @returns {Float32Array} 4x4单位矩阵
     */
    createIdentityMatrix() {
        return new Float32Array([
            1, 0, 0, 0,
            0, 1, 0, 0,
            0, 0, 1, 0,
            0, 0, 0, 1
        ]);
    }

    /**
     * 更新相机
     */
    updateCamera() {
        // 更新视图矩阵
        this.matrices.view = this.createLookAtMatrix(
            this.camera.position,
            this.camera.target,
            this.camera.up
        );
        
        // 更新投影矩阵
        const aspect = this.canvas.width / this.canvas.height;
        this.matrices.projection = this.createPerspectiveMatrix(
            this.camera.fov * Math.PI / 180,
            aspect,
            this.camera.near,
            this.camera.far
        );
        
        // 计算MVP矩阵
        this.updateMVPMatrix();
    }

    /**
     * 更新MVP矩阵
     */
    updateMVPMatrix() {
        // MVP = Projection * View * Model
        this.matrices.mvp = this.multiplyMatrices(
            this.matrices.projection,
            this.multiplyMatrices(this.matrices.view, this.matrices.model)
        );
    }

    /**
     * 调整画布大小
     */
    resize() {
        if (!this.canvas || !this.gl) return;
        
        const gl = this.gl;
        const canvas = this.canvas;
        
        // 设置画布大小
        const displayWidth = canvas.clientWidth;
        const displayHeight = canvas.clientHeight;
        
        if (canvas.width !== displayWidth || canvas.height !== displayHeight) {
            canvas.width = displayWidth;
            canvas.height = displayHeight;
            
            // 设置视口
            gl.viewport(0, 0, displayWidth, displayHeight);
            
            // 更新相机
            this.updateCamera();
        }
    }

    /**
     * 开始渲染帧
     */
    beginFrame() {
        if (!this.isInitialized) return;
        
        const gl = this.gl;
        
        // 清除缓冲区
        gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
        
        // 重置统计
        this.stats.drawCalls = 0;
        this.stats.vertices = 0;
        this.stats.triangles = 0;
    }

    /**
     * 结束渲染帧
     */
    endFrame() {
        // 可以在这里添加后处理效果
    }

    /**
     * 获取渲染统计信息
     * @returns {Object} 渲染统计
     */
    getStats() {
        return { ...this.stats };
    }

    /**
     * 创建透视投影矩阵
     * @param {number} fov - 视野角度（弧度）
     * @param {number} aspect - 宽高比
     * @param {number} near - 近裁剪面
     * @param {number} far - 远裁剪面
     * @returns {Float32Array} 透视投影矩阵
     */
    createPerspectiveMatrix(fov, aspect, near, far) {
        const f = Math.tan(Math.PI * 0.5 - 0.5 * fov);
        const rangeInv = 1.0 / (near - far);

        return new Float32Array([
            f / aspect, 0, 0, 0,
            0, f, 0, 0,
            0, 0, (near + far) * rangeInv, -1,
            0, 0, near * far * rangeInv * 2, 0
        ]);
    }

    /**
     * 创建视图矩阵
     * @param {Array} eye - 相机位置
     * @param {Array} target - 目标位置
     * @param {Array} up - 上方向
     * @returns {Float32Array} 视图矩阵
     */
    createLookAtMatrix(eye, target, up) {
        const zAxis = this.normalize([
            eye[0] - target[0],
            eye[1] - target[1],
            eye[2] - target[2]
        ]);

        const xAxis = this.normalize(this.cross(up, zAxis));
        const yAxis = this.normalize(this.cross(zAxis, xAxis));

        return new Float32Array([
            xAxis[0], yAxis[0], zAxis[0], 0,
            xAxis[1], yAxis[1], zAxis[1], 0,
            xAxis[2], yAxis[2], zAxis[2], 0,
            -this.dot(xAxis, eye), -this.dot(yAxis, eye), -this.dot(zAxis, eye), 1
        ]);
    }

    /**
     * 矩阵乘法
     * @param {Float32Array} a - 矩阵A
     * @param {Float32Array} b - 矩阵B
     * @returns {Float32Array} 结果矩阵
     */
    multiplyMatrices(a, b) {
        const result = new Float32Array(16);

        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                result[i * 4 + j] =
                    a[i * 4 + 0] * b[0 * 4 + j] +
                    a[i * 4 + 1] * b[1 * 4 + j] +
                    a[i * 4 + 2] * b[2 * 4 + j] +
                    a[i * 4 + 3] * b[3 * 4 + j];
            }
        }

        return result;
    }

    /**
     * 向量归一化
     * @param {Array} v - 向量
     * @returns {Array} 归一化后的向量
     */
    normalize(v) {
        const length = Math.sqrt(v[0] * v[0] + v[1] * v[1] + v[2] * v[2]);
        if (length > 0.00001) {
            return [v[0] / length, v[1] / length, v[2] / length];
        }
        return [0, 0, 0];
    }

    /**
     * 向量叉积
     * @param {Array} a - 向量A
     * @param {Array} b - 向量B
     * @returns {Array} 叉积结果
     */
    cross(a, b) {
        return [
            a[1] * b[2] - a[2] * b[1],
            a[2] * b[0] - a[0] * b[2],
            a[0] * b[1] - a[1] * b[0]
        ];
    }

    /**
     * 向量点积
     * @param {Array} a - 向量A
     * @param {Array} b - 向量B
     * @returns {number} 点积结果
     */
    dot(a, b) {
        return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];
    }

    /**
     * 销毁渲染器
     */
    destroy() {
        if (!this.gl) return;

        // 删除着色器程序
        this.shaderPrograms.forEach(program => {
            this.gl.deleteProgram(program);
        });
        this.shaderPrograms.clear();

        // 删除缓冲区
        this.buffers.forEach(buffer => {
            this.gl.deleteBuffer(buffer);
        });
        this.buffers.clear();

        // 删除纹理
        this.textures.forEach(texture => {
            this.gl.deleteTexture(texture);
        });
        this.textures.clear();

        this.gl = null;
        this.canvas = null;
        this.isInitialized = false;

        console.log('🌌 WebGL 3D渲染器已销毁');
    }
}

// 创建全局WebGL渲染器实例
window.webglRenderer = new WebGLRenderer();
