/**
 * 量子共鸣者 - 基础渲染器
 * 提供2D渲染功能和基础图形绘制
 */

class Renderer {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.width = canvas.width;
        this.height = canvas.height;
        
        // 渲染状态
        this.isRendering = false;
        this.frameCount = 0;
        this.lastFrameTime = 0;
        this.fps = 0;
        
        // 变换矩阵栈
        this.transformStack = [];
        
        // 渲染设置
        this.settings = {
            antialias: true,
            alpha: true,
            preserveDrawingBuffer: false
        };
        
        this.init();
    }

    /**
     * 初始化渲染器
     */
    init() {
        // 设置画布属性
        this.setupCanvas();
        
        // 设置渲染上下文
        this.setupContext();
        
        console.log('🎨 基础渲染器初始化完成');
    }

    /**
     * 设置画布
     */
    setupCanvas() {
        // 设置高DPI支持
        const devicePixelRatio = window.devicePixelRatio || 1;
        const rect = this.canvas.getBoundingClientRect();
        
        this.canvas.width = rect.width * devicePixelRatio;
        this.canvas.height = rect.height * devicePixelRatio;
        
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
        
        this.ctx.scale(devicePixelRatio, devicePixelRatio);
        
        this.width = rect.width;
        this.height = rect.height;
    }

    /**
     * 设置渲染上下文
     */
    setupContext() {
        // 设置默认样式
        this.ctx.imageSmoothingEnabled = this.settings.antialias;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.lineCap = 'round';
        this.ctx.lineJoin = 'round';
    }

    /**
     * 开始渲染循环
     */
    startRenderLoop() {
        this.isRendering = true;
        this.renderLoop();
    }

    /**
     * 停止渲染循环
     */
    stopRenderLoop() {
        this.isRendering = false;
    }

    /**
     * 渲染循环
     */
    renderLoop() {
        if (!this.isRendering) return;
        
        const currentTime = performance.now();
        const deltaTime = currentTime - this.lastFrameTime;
        
        // 计算FPS
        this.frameCount++;
        if (this.frameCount % 60 === 0) {
            this.fps = Math.round(1000 / deltaTime);
        }
        
        // 清空画布
        this.clear();
        
        // 渲染内容（由子类实现）
        this.render(deltaTime);
        
        this.lastFrameTime = currentTime;
        requestAnimationFrame(() => this.renderLoop());
    }

    /**
     * 渲染方法（由子类重写）
     */
    render(deltaTime) {
        // 基础渲染逻辑
    }

    /**
     * 清空画布
     */
    clear(color = 'rgba(0, 0, 0, 0)') {
        this.ctx.clearRect(0, 0, this.width, this.height);
        if (color !== 'rgba(0, 0, 0, 0)') {
            this.ctx.fillStyle = color;
            this.ctx.fillRect(0, 0, this.width, this.height);
        }
    }

    /**
     * 保存变换状态
     */
    pushTransform() {
        this.ctx.save();
        this.transformStack.push({
            x: 0,
            y: 0,
            rotation: 0,
            scaleX: 1,
            scaleY: 1
        });
    }

    /**
     * 恢复变换状态
     */
    popTransform() {
        this.ctx.restore();
        this.transformStack.pop();
    }

    /**
     * 平移
     */
    translate(x, y) {
        this.ctx.translate(x, y);
    }

    /**
     * 旋转
     */
    rotate(angle) {
        this.ctx.rotate(angle);
    }

    /**
     * 缩放
     */
    scale(x, y = x) {
        this.ctx.scale(x, y);
    }

    /**
     * 绘制圆形
     */
    drawCircle(x, y, radius, fillColor, strokeColor, strokeWidth = 1) {
        this.ctx.beginPath();
        this.ctx.arc(x, y, radius, 0, Math.PI * 2);
        
        if (fillColor) {
            this.ctx.fillStyle = fillColor;
            this.ctx.fill();
        }
        
        if (strokeColor) {
            this.ctx.strokeStyle = strokeColor;
            this.ctx.lineWidth = strokeWidth;
            this.ctx.stroke();
        }
    }

    /**
     * 绘制矩形
     */
    drawRect(x, y, width, height, fillColor, strokeColor, strokeWidth = 1) {
        if (fillColor) {
            this.ctx.fillStyle = fillColor;
            this.ctx.fillRect(x, y, width, height);
        }
        
        if (strokeColor) {
            this.ctx.strokeStyle = strokeColor;
            this.ctx.lineWidth = strokeWidth;
            this.ctx.strokeRect(x, y, width, height);
        }
    }

    /**
     * 绘制线条
     */
    drawLine(x1, y1, x2, y2, color, width = 1) {
        this.ctx.beginPath();
        this.ctx.moveTo(x1, y1);
        this.ctx.lineTo(x2, y2);
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = width;
        this.ctx.stroke();
    }

    /**
     * 绘制文本
     */
    drawText(text, x, y, font, color, align = 'center', baseline = 'middle') {
        this.ctx.font = font;
        this.ctx.fillStyle = color;
        this.ctx.textAlign = align;
        this.ctx.textBaseline = baseline;
        this.ctx.fillText(text, x, y);
    }

    /**
     * 绘制渐变圆形
     */
    drawGradientCircle(x, y, radius, colors) {
        const gradient = this.ctx.createRadialGradient(x, y, 0, x, y, radius);
        colors.forEach((color, index) => {
            gradient.addColorStop(index / (colors.length - 1), color);
        });
        
        this.ctx.beginPath();
        this.ctx.arc(x, y, radius, 0, Math.PI * 2);
        this.ctx.fillStyle = gradient;
        this.ctx.fill();
    }

    /**
     * 绘制粒子
     */
    drawParticle(particle) {
        this.pushTransform();
        this.translate(particle.x, particle.y);
        this.rotate(particle.rotation || 0);
        
        const alpha = particle.alpha || 1;
        const size = particle.size || 2;
        const color = particle.color || '#ffffff';
        
        this.ctx.globalAlpha = alpha;
        this.drawCircle(0, 0, size, color);
        this.ctx.globalAlpha = 1;
        
        this.popTransform();
    }

    /**
     * 绘制波形
     */
    drawWaveform(points, color, width = 2) {
        if (points.length < 2) return;
        
        this.ctx.beginPath();
        this.ctx.moveTo(points[0].x, points[0].y);
        
        for (let i = 1; i < points.length; i++) {
            this.ctx.lineTo(points[i].x, points[i].y);
        }
        
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = width;
        this.ctx.stroke();
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        this.setupCanvas();
        console.log(`🎨 渲染器画布大小已更新: ${this.width}x${this.height}`);
    }

    /**
     * 获取FPS
     */
    getFPS() {
        return this.fps;
    }

    /**
     * 获取渲染统计信息
     */
    getStats() {
        return {
            fps: this.fps,
            frameCount: this.frameCount,
            width: this.width,
            height: this.height,
            isRendering: this.isRendering
        };
    }

    /**
     * 销毁渲染器
     */
    destroy() {
        this.stopRenderLoop();
        this.transformStack = [];
        console.log('🎨 基础渲染器已销毁');
    }
}

// 导出渲染器类
window.Renderer = Renderer;
