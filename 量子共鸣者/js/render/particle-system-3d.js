/**
 * 量子共鸣者 - 3D粒子系统
 * 基于WebGL的高性能3D粒子渲染和物理模拟
 */

class ParticleSystem3D {
    constructor(webglRenderer) {
        this.renderer = webglRenderer;
        this.particles = [];
        this.connections = [];
        this.maxParticles = 1000;
        this.maxConnections = 500;
        
        // 粒子数据数组（用于WebGL缓冲区）
        this.particlePositions = new Float32Array(this.maxParticles * 3);
        this.particleColors = new Float32Array(this.maxParticles * 3);
        this.particleSizes = new Float32Array(this.maxParticles);
        this.particleAlphas = new Float32Array(this.maxParticles);
        
        // 连接线数据数组
        this.connectionPositions = new Float32Array(this.maxConnections * 6); // 每条线2个点
        this.connectionColors = new Float32Array(this.maxConnections * 6);
        this.connectionAlphas = new Float32Array(this.maxConnections * 2);
        
        // 物理参数
        this.physics = {
            gravity: [0, -0.001, 0],
            damping: 0.99,
            repulsion: 0.1,
            attraction: 0.05,
            maxVelocity: 0.1
        };
        
        // 量子效果参数
        this.quantumEffects = {
            resonanceRadius: 2.0,
            waveSpeed: 2.0,
            energyDecay: 0.95,
            chainReactionThreshold: 0.8
        };
        
        // 渲染参数
        this.renderParams = {
            particleBaseSize: 10.0,
            connectionWidth: 2.0,
            glowIntensity: 1.5,
            timeScale: 1.0
        };
        
        // 时间和动画
        this.time = 0;
        this.deltaTime = 0;
        this.lastUpdateTime = 0;
        
        // 活跃粒子和连接计数
        this.activeParticles = 0;
        this.activeConnections = 0;
        
        console.log('⚛️ 3D粒子系统已创建');
    }

    /**
     * 初始化粒子系统
     */
    init() {
        if (!this.renderer.isInitialized) {
            console.error('WebGL渲染器未初始化');
            return false;
        }
        
        // 初始化粒子数据
        this.initializeParticleData();
        
        // 创建一些初始粒子
        this.createInitialParticles();
        
        console.log('✅ 3D粒子系统初始化完成');
        return true;
    }

    /**
     * 初始化粒子数据
     */
    initializeParticleData() {
        // 清空所有数据
        this.particlePositions.fill(0);
        this.particleColors.fill(0);
        this.particleSizes.fill(0);
        this.particleAlphas.fill(0);
        
        this.connectionPositions.fill(0);
        this.connectionColors.fill(0);
        this.connectionAlphas.fill(0);
        
        this.particles = [];
        this.connections = [];
        this.activeParticles = 0;
        this.activeConnections = 0;
    }

    /**
     * 创建初始粒子
     */
    createInitialParticles() {
        const count = 20;
        
        for (let i = 0; i < count; i++) {
            const angle = (i / count) * Math.PI * 2;
            const radius = 3 + Math.random() * 2;
            const height = (Math.random() - 0.5) * 4;
            
            this.addParticle({
                position: [
                    Math.cos(angle) * radius,
                    height,
                    Math.sin(angle) * radius
                ],
                velocity: [
                    (Math.random() - 0.5) * 0.02,
                    (Math.random() - 0.5) * 0.02,
                    (Math.random() - 0.5) * 0.02
                ],
                frequency: 440 + Math.random() * 880,
                energy: 0.5 + Math.random() * 0.5,
                size: 8 + Math.random() * 12,
                color: this.getQuantumColor(440 + Math.random() * 880)
            });
        }
    }

    /**
     * 添加粒子
     * @param {Object} config - 粒子配置
     */
    addParticle(config) {
        if (this.activeParticles >= this.maxParticles) {
            return null;
        }
        
        const particle = {
            id: this.generateId(),
            position: config.position || [0, 0, 0],
            velocity: config.velocity || [0, 0, 0],
            acceleration: [0, 0, 0],
            frequency: config.frequency || 440,
            energy: config.energy || 1.0,
            size: config.size || 10,
            color: config.color || [0, 1, 1],
            alpha: config.alpha || 1.0,
            life: config.life || 1.0,
            maxLife: config.maxLife || 1.0,
            resonating: false,
            resonanceTime: 0,
            connections: new Set()
        };
        
        this.particles.push(particle);
        this.updateParticleBuffer(this.activeParticles, particle);
        this.activeParticles++;
        
        return particle;
    }

    /**
     * 更新粒子缓冲区数据
     * @param {number} index - 粒子索引
     * @param {Object} particle - 粒子对象
     */
    updateParticleBuffer(index, particle) {
        const posIndex = index * 3;
        const colorIndex = index * 3;
        
        // 位置
        this.particlePositions[posIndex] = particle.position[0];
        this.particlePositions[posIndex + 1] = particle.position[1];
        this.particlePositions[posIndex + 2] = particle.position[2];
        
        // 颜色
        this.particleColors[colorIndex] = particle.color[0];
        this.particleColors[colorIndex + 1] = particle.color[1];
        this.particleColors[colorIndex + 2] = particle.color[2];
        
        // 大小和透明度
        this.particleSizes[index] = particle.size;
        this.particleAlphas[index] = particle.alpha;
    }

    /**
     * 添加连接
     * @param {Object} particle1 - 粒子1
     * @param {Object} particle2 - 粒子2
     * @param {Object} config - 连接配置
     */
    addConnection(particle1, particle2, config = {}) {
        if (this.activeConnections >= this.maxConnections) {
            return null;
        }
        
        const connection = {
            id: this.generateId(),
            particle1: particle1,
            particle2: particle2,
            strength: config.strength || 1.0,
            color: config.color || [0, 1, 1],
            alpha: config.alpha || 0.8,
            life: config.life || 1.0,
            maxLife: config.maxLife || 1.0,
            animated: config.animated !== false,
            flow: 0
        };
        
        this.connections.push(connection);
        this.updateConnectionBuffer(this.activeConnections, connection);
        this.activeConnections++;
        
        // 添加到粒子的连接集合
        particle1.connections.add(connection);
        particle2.connections.add(connection);
        
        return connection;
    }

    /**
     * 更新连接缓冲区数据
     * @param {number} index - 连接索引
     * @param {Object} connection - 连接对象
     */
    updateConnectionBuffer(index, connection) {
        const posIndex = index * 6;
        const colorIndex = index * 6;
        const alphaIndex = index * 2;
        
        // 位置（两个端点）
        this.connectionPositions[posIndex] = connection.particle1.position[0];
        this.connectionPositions[posIndex + 1] = connection.particle1.position[1];
        this.connectionPositions[posIndex + 2] = connection.particle1.position[2];
        this.connectionPositions[posIndex + 3] = connection.particle2.position[0];
        this.connectionPositions[posIndex + 4] = connection.particle2.position[1];
        this.connectionPositions[posIndex + 5] = connection.particle2.position[2];
        
        // 颜色
        for (let i = 0; i < 6; i++) {
            this.connectionColors[colorIndex + i] = connection.color[i % 3];
        }
        
        // 透明度
        this.connectionAlphas[alphaIndex] = connection.alpha;
        this.connectionAlphas[alphaIndex + 1] = connection.alpha;
    }

    /**
     * 更新粒子系统
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        this.deltaTime = deltaTime;
        this.time += deltaTime * this.renderParams.timeScale;
        
        // 更新粒子物理
        this.updateParticlePhysics();
        
        // 更新量子效果
        this.updateQuantumEffects();
        
        // 更新连接
        this.updateConnections();
        
        // 清理过期的粒子和连接
        this.cleanup();
        
        // 更新缓冲区
        this.updateBuffers();
    }

    /**
     * 更新粒子物理
     */
    updateParticlePhysics() {
        for (let i = 0; i < this.activeParticles; i++) {
            const particle = this.particles[i];
            if (!particle) continue;
            
            // 重置加速度
            particle.acceleration[0] = 0;
            particle.acceleration[1] = 0;
            particle.acceleration[2] = 0;
            
            // 应用重力
            particle.acceleration[0] += this.physics.gravity[0];
            particle.acceleration[1] += this.physics.gravity[1];
            particle.acceleration[2] += this.physics.gravity[2];
            
            // 粒子间相互作用
            this.applyParticleInteractions(particle, i);
            
            // 更新速度
            particle.velocity[0] += particle.acceleration[0] * this.deltaTime;
            particle.velocity[1] += particle.acceleration[1] * this.deltaTime;
            particle.velocity[2] += particle.acceleration[2] * this.deltaTime;
            
            // 应用阻尼
            particle.velocity[0] *= this.physics.damping;
            particle.velocity[1] *= this.physics.damping;
            particle.velocity[2] *= this.physics.damping;
            
            // 限制最大速度
            const speed = Math.sqrt(
                particle.velocity[0] * particle.velocity[0] +
                particle.velocity[1] * particle.velocity[1] +
                particle.velocity[2] * particle.velocity[2]
            );
            
            if (speed > this.physics.maxVelocity) {
                const scale = this.physics.maxVelocity / speed;
                particle.velocity[0] *= scale;
                particle.velocity[1] *= scale;
                particle.velocity[2] *= scale;
            }
            
            // 更新位置
            particle.position[0] += particle.velocity[0] * this.deltaTime;
            particle.position[1] += particle.velocity[1] * this.deltaTime;
            particle.position[2] += particle.velocity[2] * this.deltaTime;
            
            // 边界检查
            this.applyBoundaryConstraints(particle);
            
            // 更新生命周期
            particle.life -= this.deltaTime * 0.001;
            particle.alpha = Math.max(0, particle.life / particle.maxLife);
        }
    }

    /**
     * 应用粒子间相互作用
     * @param {Object} particle - 当前粒子
     * @param {number} index - 粒子索引
     */
    applyParticleInteractions(particle, index) {
        for (let j = 0; j < this.activeParticles; j++) {
            if (j === index) continue;
            
            const other = this.particles[j];
            if (!other) continue;
            
            const dx = other.position[0] - particle.position[0];
            const dy = other.position[1] - particle.position[1];
            const dz = other.position[2] - particle.position[2];
            const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
            
            if (distance < 0.1) continue; // 避免除零
            
            const force = this.calculateInteractionForce(particle, other, distance);
            const forceX = (dx / distance) * force;
            const forceY = (dy / distance) * force;
            const forceZ = (dz / distance) * force;
            
            particle.acceleration[0] += forceX;
            particle.acceleration[1] += forceY;
            particle.acceleration[2] += forceZ;
        }
    }

    /**
     * 计算粒子间相互作用力
     * @param {Object} particle1 - 粒子1
     * @param {Object} particle2 - 粒子2
     * @param {number} distance - 距离
     * @returns {number} 作用力大小
     */
    calculateInteractionForce(particle1, particle2, distance) {
        // 频率相似性
        const freqDiff = Math.abs(particle1.frequency - particle2.frequency);
        const freqSimilarity = Math.exp(-freqDiff / 200); // 频率越接近，相似性越高
        
        // 基于距离的力
        let force = 0;
        
        if (distance < 1.0) {
            // 近距离排斥
            force = -this.physics.repulsion / (distance * distance);
        } else if (distance < this.quantumEffects.resonanceRadius) {
            // 共鸣范围内的吸引
            force = this.physics.attraction * freqSimilarity / distance;
        }
        
        return force;
    }

    /**
     * 应用边界约束
     * @param {Object} particle - 粒子
     */
    applyBoundaryConstraints(particle) {
        const bounds = 8; // 边界范围
        
        // X轴边界
        if (particle.position[0] > bounds) {
            particle.position[0] = bounds;
            particle.velocity[0] *= -0.5;
        } else if (particle.position[0] < -bounds) {
            particle.position[0] = -bounds;
            particle.velocity[0] *= -0.5;
        }
        
        // Y轴边界
        if (particle.position[1] > bounds) {
            particle.position[1] = bounds;
            particle.velocity[1] *= -0.5;
        } else if (particle.position[1] < -bounds) {
            particle.position[1] = -bounds;
            particle.velocity[1] *= -0.5;
        }
        
        // Z轴边界
        if (particle.position[2] > bounds) {
            particle.position[2] = bounds;
            particle.velocity[2] *= -0.5;
        } else if (particle.position[2] < -bounds) {
            particle.position[2] = -bounds;
            particle.velocity[2] *= -0.5;
        }
    }

    /**
     * 更新量子效果
     */
    updateQuantumEffects() {
        // 检测共鸣
        this.detectResonance();
        
        // 更新共鸣状态
        this.updateResonanceStates();
        
        // 处理连锁反应
        this.processChainReactions();
    }

    /**
     * 检测粒子共鸣
     */
    detectResonance() {
        for (let i = 0; i < this.activeParticles; i++) {
            const particle = this.particles[i];
            if (!particle) continue;
            
            for (let j = i + 1; j < this.activeParticles; j++) {
                const other = this.particles[j];
                if (!other) continue;
                
                const distance = this.getDistance(particle.position, other.position);
                
                if (distance < this.quantumEffects.resonanceRadius) {
                    const freqDiff = Math.abs(particle.frequency - other.frequency);
                    const resonanceStrength = Math.exp(-freqDiff / 100) * (1 - distance / this.quantumEffects.resonanceRadius);
                    
                    if (resonanceStrength > this.quantumEffects.chainReactionThreshold) {
                        this.triggerResonance(particle, other, resonanceStrength);
                    }
                }
            }
        }
    }

    /**
     * 触发共鸣
     * @param {Object} particle1 - 粒子1
     * @param {Object} particle2 - 粒子2
     * @param {number} strength - 共鸣强度
     */
    triggerResonance(particle1, particle2, strength) {
        particle1.resonating = true;
        particle2.resonating = true;
        particle1.resonanceTime = this.time;
        particle2.resonanceTime = this.time;
        
        // 创建连接
        if (!this.hasConnection(particle1, particle2)) {
            this.addConnection(particle1, particle2, {
                strength: strength,
                color: this.getResonanceColor(particle1.frequency, particle2.frequency),
                life: 2.0,
                maxLife: 2.0
            });
        }
        
        // 增强粒子能量
        particle1.energy = Math.min(1.0, particle1.energy + strength * 0.1);
        particle2.energy = Math.min(1.0, particle2.energy + strength * 0.1);
    }

    /**
     * 获取距离
     * @param {Array} pos1 - 位置1
     * @param {Array} pos2 - 位置2
     * @returns {number} 距离
     */
    getDistance(pos1, pos2) {
        const dx = pos1[0] - pos2[0];
        const dy = pos1[1] - pos2[1];
        const dz = pos1[2] - pos2[2];
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }

    /**
     * 检查是否已有连接
     * @param {Object} particle1 - 粒子1
     * @param {Object} particle2 - 粒子2
     * @returns {boolean} 是否已有连接
     */
    hasConnection(particle1, particle2) {
        for (const connection of particle1.connections) {
            if ((connection.particle1 === particle1 && connection.particle2 === particle2) ||
                (connection.particle1 === particle2 && connection.particle2 === particle1)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取量子颜色
     * @param {number} frequency - 频率
     * @returns {Array} RGB颜色数组
     */
    getQuantumColor(frequency) {
        // 将频率映射到颜色
        const hue = (frequency % 1000) / 1000 * 360;
        return this.hslToRgb(hue, 0.8, 0.6);
    }

    /**
     * 获取共鸣颜色
     * @param {number} freq1 - 频率1
     * @param {number} freq2 - 频率2
     * @returns {Array} RGB颜色数组
     */
    getResonanceColor(freq1, freq2) {
        const avgFreq = (freq1 + freq2) / 2;
        const hue = (avgFreq % 1000) / 1000 * 360;
        return this.hslToRgb(hue, 1.0, 0.8);
    }

    /**
     * HSL转RGB
     * @param {number} h - 色相
     * @param {number} s - 饱和度
     * @param {number} l - 亮度
     * @returns {Array} RGB数组
     */
    hslToRgb(h, s, l) {
        h /= 360;
        const c = (1 - Math.abs(2 * l - 1)) * s;
        const x = c * (1 - Math.abs((h * 6) % 2 - 1));
        const m = l - c / 2;
        
        let r, g, b;
        
        if (h < 1/6) {
            [r, g, b] = [c, x, 0];
        } else if (h < 2/6) {
            [r, g, b] = [x, c, 0];
        } else if (h < 3/6) {
            [r, g, b] = [0, c, x];
        } else if (h < 4/6) {
            [r, g, b] = [0, x, c];
        } else if (h < 5/6) {
            [r, g, b] = [x, 0, c];
        } else {
            [r, g, b] = [c, 0, x];
        }
        
        return [r + m, g + m, b + m];
    }

    /**
     * 更新共鸣状态
     */
    updateResonanceStates() {
        for (let i = 0; i < this.activeParticles; i++) {
            const particle = this.particles[i];
            if (!particle) continue;

            if (particle.resonating) {
                const timeSinceResonance = this.time - particle.resonanceTime;

                if (timeSinceResonance > 1.0) {
                    particle.resonating = false;
                }

                // 更新粒子颜色和大小
                const intensity = Math.max(0, 1 - timeSinceResonance);
                particle.color = this.mixColors(
                    this.getQuantumColor(particle.frequency),
                    [1, 1, 1],
                    intensity * 0.5
                );
                particle.size = this.renderParams.particleBaseSize * (1 + intensity * 0.5);
            }

            // 能量衰减
            particle.energy *= this.quantumEffects.energyDecay;
        }
    }

    /**
     * 处理连锁反应
     */
    processChainReactions() {
        // 寻找高能量粒子
        const highEnergyParticles = this.particles.filter(p =>
            p && p.energy > this.quantumEffects.chainReactionThreshold
        );

        for (const particle of highEnergyParticles) {
            this.propagateEnergyWave(particle);
        }
    }

    /**
     * 传播能量波
     * @param {Object} sourceParticle - 源粒子
     */
    propagateEnergyWave(sourceParticle) {
        const waveRadius = this.quantumEffects.waveSpeed * (this.time - sourceParticle.resonanceTime);

        for (let i = 0; i < this.activeParticles; i++) {
            const particle = this.particles[i];
            if (!particle || particle === sourceParticle) continue;

            const distance = this.getDistance(sourceParticle.position, particle.position);

            if (distance < waveRadius && distance > waveRadius - 0.5) {
                // 粒子在能量波前沿
                const waveStrength = sourceParticle.energy * Math.exp(-distance / 2);
                particle.energy = Math.min(1.0, particle.energy + waveStrength * 0.1);

                // 添加波动效果
                const waveForce = waveStrength * 0.01;
                const dx = particle.position[0] - sourceParticle.position[0];
                const dy = particle.position[1] - sourceParticle.position[1];
                const dz = particle.position[2] - sourceParticle.position[2];
                const norm = Math.sqrt(dx * dx + dy * dy + dz * dz);

                if (norm > 0) {
                    particle.velocity[0] += (dx / norm) * waveForce;
                    particle.velocity[1] += (dy / norm) * waveForce;
                    particle.velocity[2] += (dz / norm) * waveForce;
                }
            }
        }
    }

    /**
     * 更新连接
     */
    updateConnections() {
        for (let i = this.activeConnections - 1; i >= 0; i--) {
            const connection = this.connections[i];
            if (!connection) continue;

            // 更新生命周期
            connection.life -= this.deltaTime * 0.001;
            connection.alpha = Math.max(0, connection.life / connection.maxLife);

            // 更新流动效果
            if (connection.animated) {
                connection.flow = (connection.flow + this.deltaTime * 2) % 1;
            }

            // 更新缓冲区
            this.updateConnectionBuffer(i, connection);
        }
    }

    /**
     * 清理过期的粒子和连接
     */
    cleanup() {
        // 清理过期粒子
        for (let i = this.activeParticles - 1; i >= 0; i--) {
            const particle = this.particles[i];
            if (!particle || particle.life <= 0) {
                this.removeParticle(i);
            }
        }

        // 清理过期连接
        for (let i = this.activeConnections - 1; i >= 0; i--) {
            const connection = this.connections[i];
            if (!connection || connection.life <= 0) {
                this.removeConnection(i);
            }
        }
    }

    /**
     * 移除粒子
     * @param {number} index - 粒子索引
     */
    removeParticle(index) {
        if (index < 0 || index >= this.activeParticles) return;

        const particle = this.particles[index];

        // 移除相关连接
        for (const connection of particle.connections) {
            const connIndex = this.connections.indexOf(connection);
            if (connIndex >= 0) {
                this.removeConnection(connIndex);
            }
        }

        // 移动最后一个粒子到当前位置
        if (index < this.activeParticles - 1) {
            this.particles[index] = this.particles[this.activeParticles - 1];
            this.updateParticleBuffer(index, this.particles[index]);
        }

        this.activeParticles--;
        this.particles[this.activeParticles] = null;
    }

    /**
     * 移除连接
     * @param {number} index - 连接索引
     */
    removeConnection(index) {
        if (index < 0 || index >= this.activeConnections) return;

        const connection = this.connections[index];

        // 从粒子的连接集合中移除
        connection.particle1.connections.delete(connection);
        connection.particle2.connections.delete(connection);

        // 移动最后一个连接到当前位置
        if (index < this.activeConnections - 1) {
            this.connections[index] = this.connections[this.activeConnections - 1];
            this.updateConnectionBuffer(index, this.connections[index]);
        }

        this.activeConnections--;
        this.connections[this.activeConnections] = null;
    }

    /**
     * 更新缓冲区
     */
    updateBuffers() {
        // 更新所有粒子缓冲区
        for (let i = 0; i < this.activeParticles; i++) {
            const particle = this.particles[i];
            if (particle) {
                this.updateParticleBuffer(i, particle);
            }
        }

        // 更新所有连接缓冲区
        for (let i = 0; i < this.activeConnections; i++) {
            const connection = this.connections[i];
            if (connection) {
                this.updateConnectionBuffer(i, connection);
            }
        }
    }

    /**
     * 渲染粒子系统
     */
    render() {
        if (!this.renderer.isInitialized) return;

        const gl = this.renderer.gl;

        // 渲染粒子
        this.renderParticles();

        // 渲染连接
        this.renderConnections();
    }

    /**
     * 渲染粒子
     */
    renderParticles() {
        if (this.activeParticles === 0) return;

        const gl = this.renderer.gl;
        const program = this.renderer.shaderPrograms.get('particle');

        if (!program) return;

        gl.useProgram(program);

        // 设置uniform变量
        const mvpLocation = gl.getUniformLocation(program, 'u_mvpMatrix');
        const timeLocation = gl.getUniformLocation(program, 'u_time');
        const cameraLocation = gl.getUniformLocation(program, 'u_cameraPosition');

        gl.uniformMatrix4fv(mvpLocation, false, this.renderer.matrices.mvp);
        gl.uniform1f(timeLocation, this.time);
        gl.uniform3fv(cameraLocation, this.renderer.camera.position);

        // 绑定属性
        this.bindParticleAttributes(program);

        // 绘制粒子
        gl.drawArrays(gl.POINTS, 0, this.activeParticles);

        this.renderer.stats.drawCalls++;
        this.renderer.stats.vertices += this.activeParticles;
    }

    /**
     * 渲染连接
     */
    renderConnections() {
        if (this.activeConnections === 0) return;

        const gl = this.renderer.gl;
        const program = this.renderer.shaderPrograms.get('connection');

        if (!program) return;

        gl.useProgram(program);

        // 设置uniform变量
        const mvpLocation = gl.getUniformLocation(program, 'u_mvpMatrix');
        const timeLocation = gl.getUniformLocation(program, 'u_time');

        gl.uniformMatrix4fv(mvpLocation, false, this.renderer.matrices.mvp);
        gl.uniform1f(timeLocation, this.time);

        // 绑定属性
        this.bindConnectionAttributes(program);

        // 绘制连接线
        gl.drawArrays(gl.LINES, 0, this.activeConnections * 2);

        this.renderer.stats.drawCalls++;
        this.renderer.stats.vertices += this.activeConnections * 2;
    }

    /**
     * 绑定粒子属性
     * @param {WebGLProgram} program - 着色器程序
     */
    bindParticleAttributes(program) {
        const gl = this.renderer.gl;

        // 位置属性
        const positionLocation = gl.getAttribLocation(program, 'a_position');
        gl.bindBuffer(gl.ARRAY_BUFFER, this.renderer.buffers.get('particlePosition'));
        gl.bufferData(gl.ARRAY_BUFFER, this.particlePositions, gl.DYNAMIC_DRAW);
        gl.enableVertexAttribArray(positionLocation);
        gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);

        // 颜色属性
        const colorLocation = gl.getAttribLocation(program, 'a_color');
        gl.bindBuffer(gl.ARRAY_BUFFER, this.renderer.buffers.get('particleColor'));
        gl.bufferData(gl.ARRAY_BUFFER, this.particleColors, gl.DYNAMIC_DRAW);
        gl.enableVertexAttribArray(colorLocation);
        gl.vertexAttribPointer(colorLocation, 3, gl.FLOAT, false, 0, 0);

        // 大小属性
        const sizeLocation = gl.getAttribLocation(program, 'a_size');
        gl.bindBuffer(gl.ARRAY_BUFFER, this.renderer.buffers.get('particleSize'));
        gl.bufferData(gl.ARRAY_BUFFER, this.particleSizes, gl.DYNAMIC_DRAW);
        gl.enableVertexAttribArray(sizeLocation);
        gl.vertexAttribPointer(sizeLocation, 1, gl.FLOAT, false, 0, 0);

        // 透明度属性
        const alphaLocation = gl.getAttribLocation(program, 'a_alpha');
        gl.bindBuffer(gl.ARRAY_BUFFER, this.renderer.buffers.get('particleAlpha'));
        gl.bufferData(gl.ARRAY_BUFFER, this.particleAlphas, gl.DYNAMIC_DRAW);
        gl.enableVertexAttribArray(alphaLocation);
        gl.vertexAttribPointer(alphaLocation, 1, gl.FLOAT, false, 0, 0);
    }

    /**
     * 绑定连接属性
     * @param {WebGLProgram} program - 着色器程序
     */
    bindConnectionAttributes(program) {
        const gl = this.renderer.gl;

        // 位置属性
        const positionLocation = gl.getAttribLocation(program, 'a_position');
        gl.bindBuffer(gl.ARRAY_BUFFER, this.renderer.buffers.get('connectionPosition'));
        gl.bufferData(gl.ARRAY_BUFFER, this.connectionPositions, gl.DYNAMIC_DRAW);
        gl.enableVertexAttribArray(positionLocation);
        gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);

        // 颜色属性
        const colorLocation = gl.getAttribLocation(program, 'a_color');
        gl.bindBuffer(gl.ARRAY_BUFFER, this.renderer.buffers.get('connectionColor'));
        gl.bufferData(gl.ARRAY_BUFFER, this.connectionColors, gl.DYNAMIC_DRAW);
        gl.enableVertexAttribArray(colorLocation);
        gl.vertexAttribPointer(colorLocation, 3, gl.FLOAT, false, 0, 0);

        // 透明度属性
        const alphaLocation = gl.getAttribLocation(program, 'a_alpha');
        gl.bindBuffer(gl.ARRAY_BUFFER, this.renderer.buffers.get('connectionAlpha'));
        gl.bufferData(gl.ARRAY_BUFFER, this.connectionAlphas, gl.DYNAMIC_DRAW);
        gl.enableVertexAttribArray(alphaLocation);
        gl.vertexAttribPointer(alphaLocation, 1, gl.FLOAT, false, 0, 0);
    }

    /**
     * 混合颜色
     * @param {Array} color1 - 颜色1
     * @param {Array} color2 - 颜色2
     * @param {number} factor - 混合因子
     * @returns {Array} 混合后的颜色
     */
    mixColors(color1, color2, factor) {
        return [
            color1[0] * (1 - factor) + color2[0] * factor,
            color1[1] * (1 - factor) + color2[1] * factor,
            color1[2] * (1 - factor) + color2[2] * factor
        ];
    }

    /**
     * 激活粒子
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} frequency - 频率
     */
    activateParticle(x, y, frequency) {
        // 将屏幕坐标转换为3D世界坐标
        const worldPos = this.screenToWorld(x, y);

        // 寻找最近的粒子
        let nearestParticle = null;
        let minDistance = Infinity;

        for (let i = 0; i < this.activeParticles; i++) {
            const particle = this.particles[i];
            if (!particle) continue;

            const distance = this.getDistance(worldPos, particle.position);
            if (distance < minDistance) {
                minDistance = distance;
                nearestParticle = particle;
            }
        }

        if (nearestParticle && minDistance < 1.0) {
            // 激活最近的粒子
            nearestParticle.frequency = frequency;
            nearestParticle.energy = 1.0;
            nearestParticle.resonating = true;
            nearestParticle.resonanceTime = this.time;
            nearestParticle.color = this.getQuantumColor(frequency);
        } else {
            // 创建新粒子
            this.addParticle({
                position: worldPos,
                frequency: frequency,
                energy: 1.0,
                color: this.getQuantumColor(frequency)
            });
        }
    }

    /**
     * 屏幕坐标转世界坐标
     * @param {number} screenX - 屏幕X坐标
     * @param {number} screenY - 屏幕Y坐标
     * @returns {Array} 世界坐标
     */
    screenToWorld(screenX, screenY) {
        // 简化的转换，假设在Z=0平面上
        const canvas = this.renderer.canvas;
        const x = (screenX / canvas.width - 0.5) * 10;
        const y = -(screenY / canvas.height - 0.5) * 10;
        const z = 0;

        return [x, y, z];
    }

    /**
     * 获取粒子统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            activeParticles: this.activeParticles,
            activeConnections: this.activeConnections,
            maxParticles: this.maxParticles,
            maxConnections: this.maxConnections,
            resonatingParticles: this.particles.filter(p => p && p.resonating).length
        };
    }

    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     */
    generateId() {
        return Math.random().toString(36).substr(2, 9);
    }
}

// 导出类
window.ParticleSystem3D = ParticleSystem3D;
