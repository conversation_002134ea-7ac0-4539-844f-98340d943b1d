/**
 * 量子共鸣者 - 数学工具函数
 * 提供游戏中需要的各种数学计算功能
 */

class MathUtils {
    /**
     * 线性插值
     * @param {number} a - 起始值
     * @param {number} b - 结束值
     * @param {number} t - 插值参数 (0-1)
     * @returns {number} 插值结果
     */
    static lerp(a, b, t) {
        return a + (b - a) * Math.max(0, Math.min(1, t));
    }

    /**
     * 平滑插值（使用三次贝塞尔曲线）
     * @param {number} a - 起始值
     * @param {number} b - 结束值
     * @param {number} t - 插值参数 (0-1)
     * @returns {number} 平滑插值结果
     */
    static smoothLerp(a, b, t) {
        t = Math.max(0, Math.min(1, t));
        t = t * t * (3 - 2 * t); // 平滑步函数
        return a + (b - a) * t;
    }

    /**
     * 将值限制在指定范围内
     * @param {number} value - 输入值
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number} 限制后的值
     */
    static clamp(value, min, max) {
        return Math.max(min, Math.min(max, value));
    }

    /**
     * 将值从一个范围映射到另一个范围
     * @param {number} value - 输入值
     * @param {number} inMin - 输入范围最小值
     * @param {number} inMax - 输入范围最大值
     * @param {number} outMin - 输出范围最小值
     * @param {number} outMax - 输出范围最大值
     * @returns {number} 映射后的值
     */
    static map(value, inMin, inMax, outMin, outMax) {
        return outMin + (outMax - outMin) * ((value - inMin) / (inMax - inMin));
    }

    /**
     * 计算两点之间的距离
     * @param {number} x1 - 点1的x坐标
     * @param {number} y1 - 点1的y坐标
     * @param {number} x2 - 点2的x坐标
     * @param {number} y2 - 点2的y坐标
     * @returns {number} 距离
     */
    static distance(x1, y1, x2, y2) {
        const dx = x2 - x1;
        const dy = y2 - y1;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * 计算两点之间的角度（弧度）
     * @param {number} x1 - 点1的x坐标
     * @param {number} y1 - 点1的y坐标
     * @param {number} x2 - 点2的x坐标
     * @param {number} y2 - 点2的y坐标
     * @returns {number} 角度（弧度）
     */
    static angle(x1, y1, x2, y2) {
        return Math.atan2(y2 - y1, x2 - x1);
    }

    /**
     * 弧度转角度
     * @param {number} radians - 弧度值
     * @returns {number} 角度值
     */
    static radiansToDegrees(radians) {
        return radians * (180 / Math.PI);
    }

    /**
     * 角度转弧度
     * @param {number} degrees - 角度值
     * @returns {number} 弧度值
     */
    static degreesToRadians(degrees) {
        return degrees * (Math.PI / 180);
    }

    /**
     * 生成指定范围内的随机数
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number} 随机数
     */
    static random(min = 0, max = 1) {
        return min + Math.random() * (max - min);
    }

    /**
     * 生成指定范围内的随机整数
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number} 随机整数
     */
    static randomInt(min, max) {
        return Math.floor(this.random(min, max + 1));
    }

    /**
     * 从数组中随机选择一个元素
     * @param {Array} array - 数组
     * @returns {*} 随机选择的元素
     */
    static randomChoice(array) {
        return array[this.randomInt(0, array.length - 1)];
    }

    /**
     * 生成高斯（正态）分布的随机数
     * @param {number} mean - 均值
     * @param {number} stdDev - 标准差
     * @returns {number} 高斯分布随机数
     */
    static randomGaussian(mean = 0, stdDev = 1) {
        // Box-Muller变换
        if (this._hasSpare) {
            this._hasSpare = false;
            return this._spare * stdDev + mean;
        }

        this._hasSpare = true;
        const u = Math.random();
        const v = Math.random();
        const mag = stdDev * Math.sqrt(-2 * Math.log(u));
        this._spare = mag * Math.cos(2 * Math.PI * v);
        return mag * Math.sin(2 * Math.PI * v) + mean;
    }

    /**
     * 计算向量的长度
     * @param {number} x - x分量
     * @param {number} y - y分量
     * @returns {number} 向量长度
     */
    static vectorLength(x, y) {
        return Math.sqrt(x * x + y * y);
    }

    /**
     * 向量归一化
     * @param {number} x - x分量
     * @param {number} y - y分量
     * @returns {Object} 归一化后的向量 {x, y}
     */
    static vectorNormalize(x, y) {
        const length = this.vectorLength(x, y);
        if (length === 0) return { x: 0, y: 0 };
        return { x: x / length, y: y / length };
    }

    /**
     * 向量点积
     * @param {number} x1 - 向量1的x分量
     * @param {number} y1 - 向量1的y分量
     * @param {number} x2 - 向量2的x分量
     * @param {number} y2 - 向量2的y分量
     * @returns {number} 点积结果
     */
    static vectorDot(x1, y1, x2, y2) {
        return x1 * x2 + y1 * y2;
    }

    /**
     * 向量叉积（2D中返回标量）
     * @param {number} x1 - 向量1的x分量
     * @param {number} y1 - 向量1的y分量
     * @param {number} x2 - 向量2的x分量
     * @param {number} y2 - 向量2的y分量
     * @returns {number} 叉积结果
     */
    static vectorCross(x1, y1, x2, y2) {
        return x1 * y2 - y1 * x2;
    }

    /**
     * 计算频率对应的波长
     * @param {number} frequency - 频率 (Hz)
     * @param {number} speed - 波速 (默认为声速 343 m/s)
     * @returns {number} 波长 (m)
     */
    static frequencyToWavelength(frequency, speed = 343) {
        return speed / frequency;
    }

    /**
     * 计算两个频率的共鸣强度
     * @param {number} freq1 - 频率1
     * @param {number} freq2 - 频率2
     * @param {number} tolerance - 容差范围
     * @returns {number} 共鸣强度 (0-1)
     */
    static calculateResonance(freq1, freq2, tolerance = 50) {
        const diff = Math.abs(freq1 - freq2);
        if (diff > tolerance) return 0;
        return 1 - (diff / tolerance);
    }

    /**
     * 计算量子粒子的能量级别
     * @param {number} frequency - 频率
     * @param {number} amplitude - 振幅
     * @returns {number} 能量级别
     */
    static calculateQuantumEnergy(frequency, amplitude) {
        // 简化的量子能量公式: E = h * f * A^2
        const h = 6.626e-34; // 普朗克常数（简化）
        return h * frequency * amplitude * amplitude * 1e34; // 放大以便显示
    }

    /**
     * 生成波形数据
     * @param {number} frequency - 频率
     * @param {number} amplitude - 振幅
     * @param {number} phase - 相位
     * @param {number} samples - 采样点数
     * @returns {Array} 波形数据数组
     */
    static generateWaveform(frequency, amplitude, phase = 0, samples = 256) {
        const waveform = [];
        for (let i = 0; i < samples; i++) {
            const t = i / samples;
            const value = amplitude * Math.sin(2 * Math.PI * frequency * t + phase);
            waveform.push(value);
        }
        return waveform;
    }

    /**
     * 计算快速傅里叶变换（简化版）
     * @param {Array} signal - 输入信号
     * @returns {Array} 频域数据
     */
    static simpleFFT(signal) {
        const N = signal.length;
        const spectrum = [];
        
        for (let k = 0; k < N / 2; k++) {
            let real = 0;
            let imag = 0;
            
            for (let n = 0; n < N; n++) {
                const angle = -2 * Math.PI * k * n / N;
                real += signal[n] * Math.cos(angle);
                imag += signal[n] * Math.sin(angle);
            }
            
            const magnitude = Math.sqrt(real * real + imag * imag);
            spectrum.push(magnitude);
        }
        
        return spectrum;
    }

    /**
     * 平滑数组数据
     * @param {Array} data - 输入数据
     * @param {number} factor - 平滑因子 (0-1)
     * @returns {Array} 平滑后的数据
     */
    static smoothArray(data, factor = 0.8) {
        if (data.length === 0) return data;
        
        const smoothed = [data[0]];
        for (let i = 1; i < data.length; i++) {
            smoothed[i] = smoothed[i - 1] * factor + data[i] * (1 - factor);
        }
        return smoothed;
    }

    /**
     * 计算数组的平均值
     * @param {Array} array - 数值数组
     * @returns {number} 平均值
     */
    static average(array) {
        if (array.length === 0) return 0;
        return array.reduce((sum, value) => sum + value, 0) / array.length;
    }

    /**
     * 计算数组的最大值
     * @param {Array} array - 数值数组
     * @returns {number} 最大值
     */
    static max(array) {
        return Math.max(...array);
    }

    /**
     * 计算数组的最小值
     * @param {Array} array - 数值数组
     * @returns {number} 最小值
     */
    static min(array) {
        return Math.min(...array);
    }

    /**
     * 检查点是否在圆形区域内
     * @param {number} px - 点的x坐标
     * @param {number} py - 点的y坐标
     * @param {number} cx - 圆心x坐标
     * @param {number} cy - 圆心y坐标
     * @param {number} radius - 圆的半径
     * @returns {boolean} 是否在圆内
     */
    static pointInCircle(px, py, cx, cy, radius) {
        return this.distance(px, py, cx, cy) <= radius;
    }

    /**
     * 检查点是否在矩形区域内
     * @param {number} px - 点的x坐标
     * @param {number} py - 点的y坐标
     * @param {number} rx - 矩形左上角x坐标
     * @param {number} ry - 矩形左上角y坐标
     * @param {number} width - 矩形宽度
     * @param {number} height - 矩形高度
     * @returns {boolean} 是否在矩形内
     */
    static pointInRect(px, py, rx, ry, width, height) {
        return px >= rx && px <= rx + width && py >= ry && py <= ry + height;
    }

    /**
     * 生成噪声值（Perlin噪声的简化版）
     * @param {number} x - x坐标
     * @param {number} y - y坐标
     * @returns {number} 噪声值 (-1 to 1)
     */
    static noise(x, y) {
        // 简化的噪声函数
        const n = Math.sin(x * 12.9898 + y * 78.233) * 43758.5453;
        return 2 * (n - Math.floor(n)) - 1;
    }

    /**
     * 缓动函数 - 缓入
     * @param {number} t - 时间参数 (0-1)
     * @returns {number} 缓动值
     */
    static easeIn(t) {
        return t * t;
    }

    /**
     * 缓动函数 - 缓出
     * @param {number} t - 时间参数 (0-1)
     * @returns {number} 缓动值
     */
    static easeOut(t) {
        return 1 - (1 - t) * (1 - t);
    }

    /**
     * 缓动函数 - 缓入缓出
     * @param {number} t - 时间参数 (0-1)
     * @returns {number} 缓动值
     */
    static easeInOut(t) {
        return t < 0.5 ? 2 * t * t : 1 - 2 * (1 - t) * (1 - t);
    }
}

// 静态变量用于高斯随机数生成
MathUtils._hasSpare = false;
MathUtils._spare = 0;

// 导出到全局
window.MathUtils = MathUtils;
