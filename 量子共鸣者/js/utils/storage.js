/**
 * 量子共鸣者 - 统一存储服务
 * 支持多种存储后端：IndexedDB > localStorage > Memory Storage
 * 提供统一的键值存储接口
 */

class StorageService {
    constructor() {
        this.storageType = 'memory'; // 默认存储类型
        this.memoryStorage = new Map(); // 内存存储备用
        this.dbName = 'QuantumResonanceDB';
        this.dbVersion = 1;
        this.storeName = 'gameData';
        this.db = null;
        this.isInitialized = false;
        this.initPromise = null;

        // 异步初始化存储服务
        this.initPromise = this.init();
    }

    /**
     * 初始化存储服务，自动选择最佳存储方案
     */
    async init() {
        try {
            // 尝试使用 IndexedDB
            if (this.isIndexedDBSupported()) {
                await this.initIndexedDB();
                this.storageType = 'indexeddb';
                console.log('🗄️ 存储服务：使用 IndexedDB');
                this.isInitialized = true;
                return;
            }
        } catch (error) {
            console.warn('⚠️ IndexedDB 初始化失败:', error);
        }

        try {
            // 尝试使用 localStorage
            if (this.isLocalStorageSupported()) {
                this.storageType = 'localstorage';
                console.log('🗄️ 存储服务：使用 localStorage');
                this.isInitialized = true;
                return;
            }
        } catch (error) {
            console.warn('⚠️ localStorage 不可用:', error);
        }

        // 使用内存存储作为最后备选
        this.storageType = 'memory';
        this.isInitialized = true;
        console.log('🗄️ 存储服务：使用内存存储（数据不会持久化）');
    }

    /**
     * 等待存储服务初始化完成
     */
    async waitForInit() {
        if (this.initPromise) {
            await this.initPromise;
        }
        return this.isInitialized;
    }

    /**
     * 检查 IndexedDB 支持
     */
    isIndexedDBSupported() {
        return 'indexedDB' in window && indexedDB !== null;
    }

    /**
     * 检查 localStorage 支持
     */
    isLocalStorageSupported() {
        try {
            const testKey = '__storage_test__';
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);
            return true;
        } catch (e) {
            return false;
        }
    }

    /**
     * 初始化 IndexedDB
     */
    async initIndexedDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => {
                reject(new Error('IndexedDB 打开失败'));
            };

            request.onsuccess = (event) => {
                this.db = event.target.result;
                resolve();
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // 创建对象存储
                if (!db.objectStoreNames.contains(this.storeName)) {
                    const store = db.createObjectStore(this.storeName, { keyPath: 'key' });
                    store.createIndex('key', 'key', { unique: true });
                }
            };
        });
    }

    /**
     * 保存数据
     * @param {string} key - 键名
     * @param {any} value - 值
     * @returns {Promise<void>}
     */
    async put(key, value) {
        try {
            const data = {
                key: key,
                value: value,
                timestamp: Date.now()
            };

            switch (this.storageType) {
                case 'indexeddb':
                    return await this.putIndexedDB(data);
                case 'localstorage':
                    return this.putLocalStorage(key, data);
                case 'memory':
                default:
                    return this.putMemory(key, data);
            }
        } catch (error) {
            console.error('❌ 数据保存失败:', error);
            throw error;
        }
    }

    /**
     * 读取数据
     * @param {string} key - 键名
     * @returns {Promise<any>} 值
     */
    async get(key) {
        try {
            switch (this.storageType) {
                case 'indexeddb':
                    return await this.getIndexedDB(key);
                case 'localstorage':
                    return this.getLocalStorage(key);
                case 'memory':
                default:
                    return this.getMemory(key);
            }
        } catch (error) {
            console.error('❌ 数据读取失败:', error);
            return null;
        }
    }

    /**
     * 删除数据
     * @param {string} key - 键名
     * @returns {Promise<void>}
     */
    async delete(key) {
        try {
            switch (this.storageType) {
                case 'indexeddb':
                    return await this.deleteIndexedDB(key);
                case 'localstorage':
                    return this.deleteLocalStorage(key);
                case 'memory':
                default:
                    return this.deleteMemory(key);
            }
        } catch (error) {
            console.error('❌ 数据删除失败:', error);
            throw error;
        }
    }

    /**
     * 列出指定前缀的所有键
     * @param {string} prefix - 键前缀
     * @returns {Promise<string[]>} 键列表
     */
    async list(prefix = '') {
        try {
            switch (this.storageType) {
                case 'indexeddb':
                    return await this.listIndexedDB(prefix);
                case 'localstorage':
                    return this.listLocalStorage(prefix);
                case 'memory':
                default:
                    return this.listMemory(prefix);
            }
        } catch (error) {
            console.error('❌ 键列表获取失败:', error);
            return [];
        }
    }

    // IndexedDB 操作方法
    async putIndexedDB(data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            const request = store.put(data);

            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }

    async getIndexedDB(key) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.storeName], 'readonly');
            const store = transaction.objectStore(this.storeName);
            const request = store.get(key);

            request.onsuccess = () => {
                const result = request.result;
                resolve(result ? result.value : null);
            };
            request.onerror = () => reject(request.error);
        });
    }

    async deleteIndexedDB(key) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            const request = store.delete(key);

            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }

    async listIndexedDB(prefix) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.storeName], 'readonly');
            const store = transaction.objectStore(this.storeName);
            const request = store.getAllKeys();

            request.onsuccess = () => {
                const keys = request.result.filter(key => key.startsWith(prefix));
                resolve(keys);
            };
            request.onerror = () => reject(request.error);
        });
    }

    // localStorage 操作方法
    putLocalStorage(key, data) {
        const prefixedKey = `${this.dbName}_${key}`;
        localStorage.setItem(prefixedKey, JSON.stringify(data));
    }

    getLocalStorage(key) {
        const prefixedKey = `${this.dbName}_${key}`;
        const item = localStorage.getItem(prefixedKey);
        if (item) {
            try {
                const data = JSON.parse(item);
                return data.value;
            } catch (e) {
                console.warn('⚠️ localStorage 数据解析失败:', e);
                return null;
            }
        }
        return null;
    }

    deleteLocalStorage(key) {
        const prefixedKey = `${this.dbName}_${key}`;
        localStorage.removeItem(prefixedKey);
    }

    listLocalStorage(prefix) {
        const keys = [];
        const fullPrefix = `${this.dbName}_${prefix}`;
        
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(fullPrefix)) {
                // 移除数据库前缀，返回原始键名
                const originalKey = key.substring(this.dbName.length + 1);
                keys.push(originalKey);
            }
        }
        
        return keys;
    }

    // 内存存储操作方法
    putMemory(key, data) {
        this.memoryStorage.set(key, data);
    }

    getMemory(key) {
        const data = this.memoryStorage.get(key);
        return data ? data.value : null;
    }

    deleteMemory(key) {
        this.memoryStorage.delete(key);
    }

    listMemory(prefix) {
        const keys = [];
        for (const key of this.memoryStorage.keys()) {
            if (key.startsWith(prefix)) {
                keys.push(key);
            }
        }
        return keys;
    }

    /**
     * 清空所有数据
     * @returns {Promise<void>}
     */
    async clear() {
        try {
            switch (this.storageType) {
                case 'indexeddb':
                    return await this.clearIndexedDB();
                case 'localstorage':
                    return this.clearLocalStorage();
                case 'memory':
                default:
                    return this.clearMemory();
            }
        } catch (error) {
            console.error('❌ 数据清空失败:', error);
            throw error;
        }
    }

    async clearIndexedDB() {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            const request = store.clear();

            request.onsuccess = () => resolve();
            request.onerror = () => reject(request.error);
        });
    }

    clearLocalStorage() {
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(`${this.dbName}_`)) {
                keysToRemove.push(key);
            }
        }
        keysToRemove.forEach(key => localStorage.removeItem(key));
    }

    clearMemory() {
        this.memoryStorage.clear();
    }

    /**
     * 获取存储统计信息
     * @returns {Promise<Object>} 统计信息
     */
    async getStats() {
        const keys = await this.list();
        return {
            storageType: this.storageType,
            totalKeys: keys.length,
            keys: keys
        };
    }

    // 便捷方法，使用同步接口
    set(key, value) {
        this.put(key, value).catch(error => {
            console.error('❌ 同步保存失败:', error);
        });
        return true;
    }

    // 便捷方法，使用同步接口
    get(key) {
        // 对于同步调用，我们需要返回缓存的值或默认值
        // 这里简化处理，实际应用中可能需要更复杂的缓存机制
        if (this.storageType === 'memory') {
            return this.getMemory(key);
        } else if (this.storageType === 'localstorage') {
            return this.getLocalStorage(key);
        }
        return null;
    }

    /**
     * 获取自定义关卡列表
     * @returns {Object} 自定义关卡数据
     */
    getCustomLevels() {
        return this.get('customLevels') || {};
    }

    /**
     * 保存自定义关卡
     * @param {string} levelId - 关卡ID
     * @param {Object} levelData - 关卡数据
     * @returns {boolean} 是否保存成功
     */
    saveCustomLevel(levelId, levelData) {
        try {
            const customLevels = this.getCustomLevels();
            customLevels[levelId] = levelData;
            return this.set('customLevels', customLevels);
        } catch (error) {
            console.error('❌ 保存自定义关卡失败:', error);
            return false;
        }
    }

    /**
     * 删除自定义关卡
     * @param {string} levelId - 关卡ID
     * @returns {boolean} 是否删除成功
     */
    deleteCustomLevel(levelId) {
        try {
            const customLevels = this.getCustomLevels();
            delete customLevels[levelId];
            return this.set('customLevels', customLevels);
        } catch (error) {
            console.error('❌ 删除自定义关卡失败:', error);
            return false;
        }
    }

    /**
     * 获取关卡进度
     * @returns {Object} 关卡进度数据
     */
    getLevelProgress() {
        return this.get('levelProgress') || {};
    }

    /**
     * 更新关卡进度
     * @param {string} progressKey - 进度键值
     * @param {Object} progressData - 进度数据
     * @returns {boolean} 是否更新成功
     */
    updateLevelProgress(progressKey, progressData) {
        try {
            const levelProgress = this.getLevelProgress();
            levelProgress[progressKey] = progressData;
            return this.set('levelProgress', levelProgress);
        } catch (error) {
            console.error('❌ 更新关卡进度失败:', error);
            return false;
        }
    }

    /**
     * 获取玩家统计数据
     * @returns {Object} 玩家统计数据
     */
    getPlayerStats() {
        return this.get('playerStats') || {
            totalPlayTime: 0,
            totalScore: 0,
            levelsCompleted: 0,
            achievementsUnlocked: 0,
            bestCombo: 0,
            totalParticlesActivated: 0,
            totalChainReactions: 0
        };
    }

    /**
     * 更新玩家统计数据
     * @param {Object} stats - 统计数据
     * @returns {boolean} 是否更新成功
     */
    updatePlayerStats(stats) {
        try {
            const currentStats = this.getPlayerStats();
            const updatedStats = { ...currentStats, ...stats };
            return this.set('playerStats', updatedStats);
        } catch (error) {
            console.error('❌ 更新玩家统计失败:', error);
            return false;
        }
    }

    /**
     * 获取成就数据
     * @returns {Object} 成就数据
     */
    getAchievements() {
        return this.get('achievements') || {};
    }

    /**
     * 解锁成就
     * @param {string} achievementId - 成就ID
     * @param {Object} achievementData - 成就数据
     * @returns {boolean} 是否解锁成功
     */
    unlockAchievement(achievementId, achievementData) {
        try {
            const achievements = this.getAchievements();
            achievements[achievementId] = {
                ...achievementData,
                unlockedAt: new Date().toISOString()
            };
            return this.set('achievements', achievements);
        } catch (error) {
            console.error('❌ 解锁成就失败:', error);
            return false;
        }
    }

    /**
     * 保存游戏回放
     * @param {string} replayId - 回放ID
     * @param {Object} replayData - 回放数据
     * @returns {boolean} 是否保存成功
     */
    saveReplay(replayId, replayData) {
        try {
            const replays = this.get('replays') || {};
            replays[replayId] = replayData;

            // 限制回放数量，只保留最新的50个
            const replayEntries = Object.entries(replays);
            if (replayEntries.length > 50) {
                replayEntries.sort((a, b) => new Date(b[1].timestamp) - new Date(a[1].timestamp));
                const limitedReplays = Object.fromEntries(replayEntries.slice(0, 50));
                return this.set('replays', limitedReplays);
            }

            return this.set('replays', replays);
        } catch (error) {
            console.error('❌ 保存回放失败:', error);
            return false;
        }
    }

    /**
     * 获取游戏回放列表
     * @returns {Object} 回放数据
     */
    getReplays() {
        return this.get('replays') || {};
    }

    /**
     * 删除游戏回放
     * @param {string} replayId - 回放ID
     * @returns {boolean} 是否删除成功
     */
    deleteReplay(replayId) {
        try {
            const replays = this.getReplays();
            delete replays[replayId];
            return this.set('replays', replays);
        } catch (error) {
            console.error('❌ 删除回放失败:', error);
            return false;
        }
    }
}

// 导出类到全局作用域
window.StorageService = StorageService;

// 创建全局存储服务实例
window.storageService = new StorageService();
