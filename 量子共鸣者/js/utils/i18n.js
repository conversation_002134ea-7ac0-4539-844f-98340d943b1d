/**
 * 量子共鸣者 - 国际化服务
 * 支持多语言切换和动态文本更新
 */

class I18nService {
    constructor() {
        this.currentLanguage = 'zh-CN'; // 默认语言
        this.fallbackLanguage = 'zh-CN'; // 备用语言
        this.translations = {}; // 翻译数据
        this.observers = []; // 语言变更观察者
        
        // 初始化翻译数据
        this.initTranslations();
        
        // 从存储中恢复语言设置
        this.loadLanguageFromStorage();
    }

    /**
     * 初始化国际化服务
     * 提供给应用程序调用的公共初始化方法
     */
    async init() {
        try {
            console.log('🌐 开始初始化国际化服务...');

            // 确保翻译数据已加载
            if (Object.keys(this.translations).length === 0) {
                this.initTranslations();
            }

            // 从存储中恢复语言设置
            this.loadLanguageFromStorage();

            // 更新页面文本以应用当前语言
            this.updatePageTexts();

            console.log('✅ 国际化服务初始化完成，当前语言:', this.currentLanguage);
            return true;
        } catch (error) {
            console.error('❌ 国际化服务初始化失败:', error);
            throw error;
        }
    }

    /**
     * 初始化翻译数据
     */
    initTranslations() {
        // 中文翻译
        this.translations['zh-CN'] = {
            // 通用
            'common.loading': '加载中...',
            'common.error': '错误',
            'common.success': '成功',
            'common.cancel': '取消',
            'common.confirm': '确认',
            'common.save': '保存',
            'common.delete': '删除',
            'common.edit': '编辑',
            'common.close': '关闭',
            'common.back': '返回',
            'common.next': '下一步',
            'common.previous': '上一步',
            'common.yes': '是',
            'common.no': '否',

            // 游戏标题和描述
            'game.title': '量子共鸣者',
            'game.subtitle': '捕捉决定性瞬间，引燃无限可能',
            'game.description': '通过操控量子粒子的共鸣频率，在多维空间中创造连锁反应',

            // 主菜单
            'menu.startGame': '开始游戏',
            'menu.levelEditor': '关卡编辑器',
            'menu.customLevels': '自定义关卡',
            'menu.achievements': '成就系统',
            'menu.leaderboard': '排行榜',
            'menu.settings': '设置',
            'menu.switchPlayer': '切换玩家',

            // 游戏界面
            'game.level': '关卡',
            'game.score': '得分',
            'game.combo': '连击',
            'game.frequency': '频率',
            'game.resonance': '共鸣强度',
            'game.pause': '暂停',
            'game.resume': '继续',
            'game.restart': '重新开始',
            'game.quit': '退出',

            // 暂停菜单
            'pause.title': '游戏暂停',
            'pause.resume': '继续游戏',
            'pause.restart': '重新开始',
            'pause.settings': '设置',
            'pause.quit': '退出游戏',

            // 设置界面
            'settings.title': '游戏设置',
            'settings.audio': '音频设置',
            'settings.graphics': '图形设置',
            'settings.controls': '控制设置',
            'settings.masterVolume': '主音量',
            'settings.musicVolume': '音乐音量',
            'settings.sfxVolume': '音效音量',
            'settings.particleQuality': '粒子质量',
            'settings.visualEffects': '视觉效果',
            'settings.sensitivity': '灵敏度',
            'settings.low': '低',
            'settings.medium': '中',
            'settings.high': '高',
            'settings.reset': '重置设置',
            'settings.save': '保存设置',

            // 玩家系统
            'player.title': '选择玩家',
            'player.create': '创建新玩家',
            'player.close': '关闭',
            'player.name': '玩家名称',
            'player.guest': '访客',
            'player.stats': '统计数据',
            'player.achievements': '成就',

            // 关卡编辑器
            'editor.title': '关卡编辑器',
            'editor.new': '新建关卡',
            'editor.load': '加载关卡',
            'editor.save': '保存关卡',
            'editor.test': '测试关卡',
            'editor.publish': '发布关卡',
            'editor.tools': '工具',
            'editor.properties': '属性',

            // 自定义关卡
            'customLevels.title': '自定义关卡',
            'customLevels.myLevels': '我的关卡',
            'customLevels.community': '社区关卡',
            'customLevels.featured': '精选关卡',
            'customLevels.search': '搜索关卡',
            'customLevels.sort': '排序方式',
            'customLevels.rating': '评分',
            'customLevels.difficulty': '难度',
            'customLevels.plays': '游玩次数',

            // 排行榜
            'leaderboard.title': '排行榜',
            'leaderboard.global': '全球排行',
            'leaderboard.friends': '好友排行',
            'leaderboard.weekly': '本周排行',
            'leaderboard.monthly': '本月排行',
            'leaderboard.rank': '排名',
            'leaderboard.player': '玩家',
            'leaderboard.score': '得分',

            // 成就系统
            'achievements.title': '成就',
            'achievements.locked': '未解锁',
            'achievements.unlocked': '已解锁',
            'achievements.progress': '进度',
            'achievements.reward': '奖励',

            // 错误信息
            'error.loadFailed': '加载失败',
            'error.saveFailed': '保存失败',
            'error.networkError': '网络错误',
            'error.invalidData': '数据无效',
            'error.permissionDenied': '权限被拒绝',

            // 成功信息
            'success.saved': '保存成功',
            'success.loaded': '加载成功',
            'success.published': '发布成功',
            'success.updated': '更新成功'
        };

        // 英文翻译
        this.translations['en-US'] = {
            // Common
            'common.loading': 'Loading...',
            'common.error': 'Error',
            'common.success': 'Success',
            'common.cancel': 'Cancel',
            'common.confirm': 'Confirm',
            'common.save': 'Save',
            'common.delete': 'Delete',
            'common.edit': 'Edit',
            'common.close': 'Close',
            'common.back': 'Back',
            'common.next': 'Next',
            'common.previous': 'Previous',
            'common.yes': 'Yes',
            'common.no': 'No',

            // Game title and description
            'game.title': 'Quantum Resonance',
            'game.subtitle': 'Capture decisive moments, ignite infinite possibilities',
            'game.description': 'Control quantum particle resonance frequencies to create chain reactions in multidimensional space',

            // Main menu
            'menu.startGame': 'Start Game',
            'menu.levelEditor': 'Level Editor',
            'menu.customLevels': 'Custom Levels',
            'menu.achievements': 'Achievements',
            'menu.leaderboard': 'Leaderboard',
            'menu.settings': 'Settings',
            'menu.switchPlayer': 'Switch Player',

            // Game interface
            'game.level': 'Level',
            'game.score': 'Score',
            'game.combo': 'Combo',
            'game.frequency': 'Frequency',
            'game.resonance': 'Resonance',
            'game.pause': 'Pause',
            'game.resume': 'Resume',
            'game.restart': 'Restart',
            'game.quit': 'Quit',

            // Pause menu
            'pause.title': 'Game Paused',
            'pause.resume': 'Resume Game',
            'pause.restart': 'Restart',
            'pause.settings': 'Settings',
            'pause.quit': 'Quit Game',

            // Settings
            'settings.title': 'Game Settings',
            'settings.audio': 'Audio Settings',
            'settings.graphics': 'Graphics Settings',
            'settings.controls': 'Control Settings',
            'settings.masterVolume': 'Master Volume',
            'settings.musicVolume': 'Music Volume',
            'settings.sfxVolume': 'SFX Volume',
            'settings.particleQuality': 'Particle Quality',
            'settings.visualEffects': 'Visual Effects',
            'settings.sensitivity': 'Sensitivity',
            'settings.low': 'Low',
            'settings.medium': 'Medium',
            'settings.high': 'High',
            'settings.reset': 'Reset Settings',
            'settings.save': 'Save Settings',

            // Player system
            'player.title': 'Select Player',
            'player.create': 'Create New Player',
            'player.close': 'Close',
            'player.name': 'Player Name',
            'player.guest': 'Guest',
            'player.stats': 'Statistics',
            'player.achievements': 'Achievements',

            // Level editor
            'editor.title': 'Level Editor',
            'editor.new': 'New Level',
            'editor.load': 'Load Level',
            'editor.save': 'Save Level',
            'editor.test': 'Test Level',
            'editor.publish': 'Publish Level',
            'editor.tools': 'Tools',
            'editor.properties': 'Properties',

            // Custom levels
            'customLevels.title': 'Custom Levels',
            'customLevels.myLevels': 'My Levels',
            'customLevels.community': 'Community Levels',
            'customLevels.featured': 'Featured Levels',
            'customLevels.search': 'Search Levels',
            'customLevels.sort': 'Sort By',
            'customLevels.rating': 'Rating',
            'customLevels.difficulty': 'Difficulty',
            'customLevels.plays': 'Plays',

            // Leaderboard
            'leaderboard.title': 'Leaderboard',
            'leaderboard.global': 'Global',
            'leaderboard.friends': 'Friends',
            'leaderboard.weekly': 'Weekly',
            'leaderboard.monthly': 'Monthly',
            'leaderboard.rank': 'Rank',
            'leaderboard.player': 'Player',
            'leaderboard.score': 'Score',

            // Achievements
            'achievements.title': 'Achievements',
            'achievements.locked': 'Locked',
            'achievements.unlocked': 'Unlocked',
            'achievements.progress': 'Progress',
            'achievements.reward': 'Reward',

            // Error messages
            'error.loadFailed': 'Load Failed',
            'error.saveFailed': 'Save Failed',
            'error.networkError': 'Network Error',
            'error.invalidData': 'Invalid Data',
            'error.permissionDenied': 'Permission Denied',

            // Success messages
            'success.saved': 'Saved Successfully',
            'success.loaded': 'Loaded Successfully',
            'success.published': 'Published Successfully',
            'success.updated': 'Updated Successfully'
        };
    }

    /**
     * 从存储中加载语言设置
     */
    async loadLanguageFromStorage() {
        try {
            const savedLanguage = await storageService.get('settings.language');
            if (savedLanguage && this.translations[savedLanguage]) {
                this.currentLanguage = savedLanguage;
            } else {
                // 尝试检测浏览器语言
                this.detectBrowserLanguage();
            }
        } catch (error) {
            console.warn('⚠️ 语言设置加载失败，使用默认语言:', error);
            this.detectBrowserLanguage();
        }
    }

    /**
     * 检测浏览器语言
     */
    detectBrowserLanguage() {
        const browserLang = navigator.language || navigator.userLanguage;
        
        // 检查是否有完全匹配的语言
        if (this.translations[browserLang]) {
            this.currentLanguage = browserLang;
            return;
        }
        
        // 检查是否有语言代码匹配（如 zh-CN -> zh）
        const langCode = browserLang.split('-')[0];
        const matchingLang = Object.keys(this.translations).find(lang => 
            lang.startsWith(langCode)
        );
        
        if (matchingLang) {
            this.currentLanguage = matchingLang;
        }
    }

    /**
     * 获取翻译文本
     * @param {string} key - 翻译键
     * @param {Object} params - 参数对象（用于模板替换）
     * @returns {string} 翻译后的文本
     */
    t(key, params = {}) {
        let text = this.getTranslation(key);
        
        // 参数替换
        if (params && typeof params === 'object') {
            Object.keys(params).forEach(param => {
                const placeholder = `{${param}}`;
                text = text.replace(new RegExp(placeholder, 'g'), params[param]);
            });
        }
        
        return text;
    }

    /**
     * 获取原始翻译文本
     * @param {string} key - 翻译键
     * @returns {string} 翻译文本
     */
    getTranslation(key) {
        // 尝试获取当前语言的翻译
        if (this.translations[this.currentLanguage] && 
            this.translations[this.currentLanguage][key]) {
            return this.translations[this.currentLanguage][key];
        }
        
        // 尝试获取备用语言的翻译
        if (this.translations[this.fallbackLanguage] && 
            this.translations[this.fallbackLanguage][key]) {
            return this.translations[this.fallbackLanguage][key];
        }
        
        // 返回键名作为最后备选
        console.warn(`⚠️ 翻译缺失: ${key}`);
        return key;
    }

    /**
     * 设置当前语言
     * @param {string} language - 语言代码
     */
    async setLanguage(language) {
        if (!this.translations[language]) {
            console.warn(`⚠️ 不支持的语言: ${language}`);
            return false;
        }
        
        const oldLanguage = this.currentLanguage;
        this.currentLanguage = language;
        
        try {
            // 保存到存储
            await storageService.put('settings.language', language);
            
            // 更新页面文本
            this.updatePageTexts();
            
            // 通知观察者
            this.notifyLanguageChange(oldLanguage, language);
            
            console.log(`🌍 语言已切换: ${oldLanguage} -> ${language}`);
            return true;
        } catch (error) {
            console.error('❌ 语言设置保存失败:', error);
            this.currentLanguage = oldLanguage; // 回滚
            return false;
        }
    }

    /**
     * 获取当前语言
     * @returns {string} 当前语言代码
     */
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    /**
     * 获取支持的语言列表
     * @returns {Array} 语言列表
     */
    getSupportedLanguages() {
        return Object.keys(this.translations).map(lang => ({
            code: lang,
            name: this.getLanguageName(lang)
        }));
    }

    /**
     * 获取语言显示名称
     * @param {string} langCode - 语言代码
     * @returns {string} 语言显示名称
     */
    getLanguageName(langCode) {
        const names = {
            'zh-CN': '中文',
            'en-US': 'English'
        };
        return names[langCode] || langCode;
    }

    /**
     * 更新页面中所有带有 data-i18n 属性的元素
     */
    updatePageTexts() {
        const elements = document.querySelectorAll('[data-i18n]');
        elements.forEach(element => {
            const key = element.getAttribute('data-i18n');
            const text = this.t(key);
            
            // 根据元素类型设置文本
            if (element.tagName === 'INPUT' && element.type === 'text') {
                element.placeholder = text;
            } else if (element.tagName === 'INPUT' && element.type === 'button') {
                element.value = text;
            } else {
                element.textContent = text;
            }
        });
    }

    /**
     * 添加语言变更观察者
     * @param {Function} callback - 回调函数
     */
    addLanguageChangeObserver(callback) {
        if (typeof callback === 'function') {
            this.observers.push(callback);
        }
    }

    /**
     * 移除语言变更观察者
     * @param {Function} callback - 回调函数
     */
    removeLanguageChangeObserver(callback) {
        const index = this.observers.indexOf(callback);
        if (index > -1) {
            this.observers.splice(index, 1);
        }
    }

    /**
     * 通知语言变更观察者
     * @param {string} oldLanguage - 旧语言
     * @param {string} newLanguage - 新语言
     */
    notifyLanguageChange(oldLanguage, newLanguage) {
        this.observers.forEach(callback => {
            try {
                callback(oldLanguage, newLanguage);
            } catch (error) {
                console.error('❌ 语言变更观察者执行失败:', error);
            }
        });
    }
}

// 导出类到全局作用域
window.I18nService = I18nService;

// 创建全局国际化服务实例
window.i18nService = new I18nService();

// 为了兼容性，也创建 i18n 别名
window.i18n = window.i18nService;
