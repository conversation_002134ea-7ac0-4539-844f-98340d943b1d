<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量子共鸣者 - 简单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: rgba(255,255,255,0.1);
        }
        .pass { border-left: 4px solid #00ff00; }
        .fail { border-left: 4px solid #ff0000; }
        button {
            background: #00d4ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
    </style>
</head>
<body>
    <h1>🎮 量子共鸣者 - 简单测试</h1>
    <button onclick="runTests()">运行测试</button>
    <div id="results"></div>

    <!-- 添加应用程序需要的DOM元素 -->
    <div id="loadingScreen" style="display: none;">
        <div id="loadingProgress"></div>
        <div id="loadingText">0%</div>
    </div>

    <div id="mainMenu" style="display: none;">
        <button id="startGameButton">开始游戏</button>
        <button id="levelSelectButton">选择关卡</button>
        <button id="settingsButton">设置</button>
        <button id="aboutButton">关于</button>
    </div>

    <div id="settingsScreen" style="display: none;">
        <h2>设置</h2>
    </div>

    <div id="aboutScreen" style="display: none;">
        <h2>关于</h2>
    </div>

    <div id="levelSelectScreen" style="display: none;">
        <h2>关卡选择</h2>
    </div>

    <canvas id="game-canvas" style="display: none;" width="800" height="600"></canvas>

    <button id="initAudioButton" style="display: none;">初始化音频</button>

    <!-- 只加载必要的脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/core/audio-engine.js"></script>
    <script src="js/core/physics-engine.js"></script>
    <script src="js/core/quantum-engine.js"></script>
    <script src="js/core/render-engine.js"></script>
    <script src="js/ui/ui-manager.js"></script>
    <script src="js/ui/level-select.js"></script>
    <script src="js/game/input-manager.js"></script>
    <script src="js/game/game-controller.js"></script>
    <script src="js/app.js"></script>

    <script>
        let app = null;
        
        // 启动应用程序
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                console.log('🚀 启动应用程序...');
                app = new QuantumResonanceApp();
                await app.init();
                console.log('✅ 应用程序启动完成');
            } catch (error) {
                console.error('❌ 应用程序启动失败:', error);
            }
        });

        function addResult(name, passed, message) {
            const div = document.createElement('div');
            div.className = `test-result ${passed ? 'pass' : 'fail'}`;
            div.innerHTML = `<strong>${name}:</strong> ${message}`;
            document.getElementById('results').appendChild(div);
        }

        function runTests() {
            document.getElementById('results').innerHTML = '';
            
            // 测试1: 游戏控制器存在
            const gameControllerExists = typeof window.gameController !== 'undefined';
            addResult(
                '游戏控制器存在性检查',
                gameControllerExists,
                gameControllerExists ? '✅ window.gameController 存在' : '❌ window.gameController 不存在'
            );

            // 测试2: 游戏控制器已初始化
            if (gameControllerExists) {
                const initialized = window.gameController.isInitialized === true;
                addResult(
                    '游戏控制器初始化检查',
                    initialized,
                    initialized ? '✅ 游戏控制器已初始化' : '❌ 游戏控制器未初始化'
                );
            } else {
                addResult('游戏控制器初始化检查', false, '❌ 游戏控制器不存在');
            }

            // 测试3: 关卡选择界面存在
            const levelSelectExists = typeof window.levelSelect !== 'undefined';
            addResult(
                '关卡选择界面存在性检查',
                levelSelectExists,
                levelSelectExists ? '✅ window.levelSelect 存在' : '❌ window.levelSelect 不存在'
            );

            // 测试4: startLevel方法存在
            if (levelSelectExists) {
                const hasStartLevel = typeof window.levelSelect.startLevel === 'function';
                addResult(
                    'startLevel方法检查',
                    hasStartLevel,
                    hasStartLevel ? '✅ startLevel方法存在' : '❌ startLevel方法不存在'
                );
            } else {
                addResult('startLevel方法检查', false, '❌ 关卡选择界面不存在');
            }

            // 测试5: hideWithoutReturnToMenu方法存在
            if (levelSelectExists) {
                const hasHideMethod = typeof window.levelSelect.hideWithoutReturnToMenu === 'function';
                addResult(
                    'hideWithoutReturnToMenu方法检查',
                    hasHideMethod,
                    hasHideMethod ? '✅ hideWithoutReturnToMenu方法存在' : '❌ hideWithoutReturnToMenu方法不存在'
                );
            } else {
                addResult('hideWithoutReturnToMenu方法检查', false, '❌ 关卡选择界面不存在');
            }

            // 统计结果
            const results = document.querySelectorAll('.test-result');
            const passed = document.querySelectorAll('.pass').length;
            const total = results.length;
            
            const summary = document.createElement('div');
            summary.className = `test-result ${passed === total ? 'pass' : 'fail'}`;
            summary.innerHTML = `<strong>总体测试结果:</strong> ${passed}/${total} 个测试通过 ${passed === total ? '🎉' : '😞'}`;
            document.getElementById('results').appendChild(summary);
        }
    </script>
</body>
</html>
