/**
 * PC端布局验证脚本
 * 验证关卡选择界面在PC环境下的布局是否正确
 */

console.log('🖥️ 开始PC端布局验证...');

// 等待页面完全加载
document.addEventListener('DOMContentLoaded', function() {
    // 确保关卡选择界面已经初始化后再进行验证
    initializeLevelSelectForTesting().then(() => {
        setTimeout(runPCLayoutVerification, 1000);
    });
});

/**
 * 为测试初始化关卡选择界面
 * 确保HTML结构已经生成
 */
async function initializeLevelSelectForTesting() {
    console.log('🔧 为PC端布局验证初始化关卡选择界面...');

    // 检查关卡选择实例是否存在
    if (!window.levelSelect) {
        console.error('❌ 关卡选择实例不存在');
        return;
    }

    // 如果还没有初始化，先初始化
    if (!window.levelSelect.isInitialized) {
        console.log('🔧 初始化关卡选择界面...');
        const initResult = window.levelSelect.init();
        if (!initResult) {
            console.error('❌ 关卡选择界面初始化失败');
            return;
        }
    }

    // 确保HTML结构已经生成（通过临时显示界面）
    const container = document.getElementById('levelSelectScreen');
    if (container && container.innerHTML.trim() === '') {
        console.log('🔧 生成关卡选择界面HTML结构...');

        // 临时显示界面以生成HTML结构
        const originalDisplay = container.style.display;
        const originalVisibility = container.style.visibility;

        // 设置为不可见但占用空间，这样可以生成HTML但用户看不到
        container.style.visibility = 'hidden';
        container.style.display = 'flex';

        // 调用show方法生成HTML结构
        window.levelSelect.show();

        // 等待一小段时间确保HTML生成完成
        await new Promise(resolve => setTimeout(resolve, 100));

        // 恢复原始状态
        container.style.display = originalDisplay;
        container.style.visibility = originalVisibility;
        container.classList.remove('active');

        console.log('✅ 关卡选择界面HTML结构已生成');
    }

    console.log('✅ 关卡选择界面初始化完成，可以开始验证');
}

function runPCLayoutVerification() {
    console.log('🎯 执行PC端布局验证测试');

    const results = {
        passed: 0,
        failed: 0,
        tests: []
    };

    // 首先验证关卡选择界面是否已经正确初始化
    console.log('\n🔧 预检查: 验证关卡选择界面初始化状态');

    const container = document.getElementById('levelSelectScreen');
    if (!container) {
        logTest(results, '❌ 关卡选择容器不存在', false);
        showPCLayoutResults(results);
        return;
    }

    if (container.innerHTML.trim() === '') {
        logTest(results, '❌ 关卡选择界面HTML结构未生成', false);
        showPCLayoutResults(results);
        return;
    }

    logTest(results, '✅ 关卡选择界面容器存在', true);
    logTest(results, '✅ 关卡选择界面HTML结构已生成', true);

    // 测试1: 检查屏幕尺寸
    console.log('\n📐 测试1: 屏幕尺寸检查');

    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    logTest(results, `屏幕尺寸: ${viewportWidth}x${viewportHeight}`, true);

    if (viewportWidth >= 1200) {
        logTest(results, '✅ 检测到PC端大屏幕环境', true);
    } else {
        logTest(results, '⚠️ 当前为小屏幕环境，PC端优化可能不生效', true);
    }
    
    // 测试2: 检查关卡预览区域
    console.log('\n🎨 测试2: 关卡预览区域检查');
    
    const levelPreview = document.getElementById('levelPreview');
    if (levelPreview) {
        const previewStyle = window.getComputedStyle(levelPreview);
        const maxHeight = previewStyle.maxHeight;
        const overflowY = previewStyle.overflowY;
        
        logTest(results, '✅ 关卡预览区域存在', true);
        logTest(results, `最大高度设置: ${maxHeight}`, maxHeight !== 'none');
        logTest(results, `垂直滚动设置: ${overflowY}`, overflowY === 'auto');
        
        // 检查实际尺寸
        const clientHeight = levelPreview.clientHeight;
        const scrollHeight = levelPreview.scrollHeight;
        const isScrollable = scrollHeight > clientHeight;
        
        logTest(results, `预览区域高度: ${clientHeight}px`, true);
        logTest(results, `内容总高度: ${scrollHeight}px`, true);
        logTest(results, `是否可滚动: ${isScrollable}`, true);
        
    } else {
        logTest(results, '❌ 关卡预览区域不存在', false);
    }
    
    // 测试3: 检查难度选择器位置
    console.log('\n🎯 测试3: 难度选择器位置检查');
    
    const difficultySelector = document.getElementById('difficultySelector');
    if (difficultySelector) {
        const rect = difficultySelector.getBoundingClientRect();
        const isInViewport = rect.top >= 0 && rect.bottom <= viewportHeight;
        const isPartiallyVisible = rect.top < viewportHeight && rect.bottom > 0;
        
        logTest(results, '✅ 难度选择器存在', true);
        logTest(results, `选择器位置: top=${Math.round(rect.top)}, bottom=${Math.round(rect.bottom)}`, true);
        
        if (isInViewport) {
            logTest(results, '✅ 难度选择器完全在视口内', true);
        } else if (isPartiallyVisible) {
            logTest(results, '⚠️ 难度选择器部分可见', true);
        } else {
            logTest(results, '❌ 难度选择器不在视口内', false);
        }
        
        // 检查样式
        const selectorStyle = window.getComputedStyle(difficultySelector);
        const flexShrink = selectorStyle.flexShrink;
        
        logTest(results, `flex-shrink设置: ${flexShrink}`, flexShrink === '0');
        
    } else {
        logTest(results, '❌ 难度选择器不存在', false);
    }
    
    // 测试4: 检查开始游戏按钮
    console.log('\n🚀 测试4: 开始游戏按钮检查');
    
    const startButton = document.getElementById('startLevelButton');
    if (startButton) {
        const buttonRect = startButton.getBoundingClientRect();
        const isButtonVisible = buttonRect.top >= 0 && buttonRect.bottom <= viewportHeight;
        
        logTest(results, '✅ 开始游戏按钮存在', true);
        logTest(results, `按钮位置: top=${Math.round(buttonRect.top)}, bottom=${Math.round(buttonRect.bottom)}`, true);
        
        if (isButtonVisible) {
            logTest(results, '✅ 开始游戏按钮在视口内', true);
        } else {
            logTest(results, '❌ 开始游戏按钮不在视口内', false);
        }
        
        // 检查按钮的父容器样式
        const actionsContainer = startButton.closest('.preview-actions');
        if (actionsContainer) {
            const actionsStyle = window.getComputedStyle(actionsContainer);
            const marginTop = actionsStyle.marginTop;
            const flexShrink = actionsStyle.flexShrink;
            
            logTest(results, `操作区域margin-top: ${marginTop}`, marginTop === 'auto');
            logTest(results, `操作区域flex-shrink: ${flexShrink}`, flexShrink === '0');
        }
        
    } else {
        logTest(results, '❌ 开始游戏按钮不存在', false);
    }
    
    // 测试5: 响应式样式检查
    console.log('\n📱 测试5: 响应式样式检查');
    
    if (viewportWidth >= 1200) {
        const levelSelectContent = document.querySelector('.level-select-content');
        if (levelSelectContent) {
            const contentStyle = window.getComputedStyle(levelSelectContent);
            const maxWidth = contentStyle.maxWidth;
            
            logTest(results, '✅ 关卡选择内容区域存在', true);
            logTest(results, `内容区域最大宽度: ${maxWidth}`, maxWidth !== 'none');
        }
        
        if (levelPreview) {
            const previewStyle = window.getComputedStyle(levelPreview);
            const maxWidth = previewStyle.maxWidth;
            
            // PC端应该有更大的最大宽度
            const maxWidthValue = parseInt(maxWidth);
            logTest(results, `PC端预览区域最大宽度: ${maxWidth}`, maxWidthValue >= 450);
        }
    }
    
    // 测试6: 滚动功能测试
    console.log('\n📜 测试6: 滚动功能测试');
    
    if (levelPreview && difficultySelector) {
        const originalScrollTop = levelPreview.scrollTop;
        
        // 尝试滚动到难度选择器
        difficultySelector.scrollIntoView({ block: 'center' });
        
        setTimeout(() => {
            const newScrollTop = levelPreview.scrollTop;
            const scrolled = newScrollTop !== originalScrollTop;
            
            logTest(results, `滚动前位置: ${originalScrollTop}px`, true);
            logTest(results, `滚动后位置: ${newScrollTop}px`, true);
            logTest(results, `滚动功能正常: ${scrolled}`, true);
            
            // 检查难度选择器是否现在可见
            const rect = difficultySelector.getBoundingClientRect();
            const previewRect = levelPreview.getBoundingClientRect();
            const isNowVisible = rect.top >= previewRect.top && rect.bottom <= previewRect.bottom;
            
            logTest(results, `滚动后难度选择器可见: ${isNowVisible}`, isNowVisible);
            
            // 恢复原始位置
            levelPreview.scrollTop = originalScrollTop;
            
            // 显示最终结果
            setTimeout(() => showPCLayoutResults(results), 500);
            
        }, 300);
    } else {
        showPCLayoutResults(results);
    }
}

function logTest(results, message, passed) {
    console.log(message);
    results.tests.push({ message, passed });
    if (passed) {
        results.passed++;
    } else {
        results.failed++;
    }
}

function showPCLayoutResults(results) {
    console.log('\n' + '='.repeat(60));
    console.log('🖥️ PC端布局验证结果');
    console.log('='.repeat(60));
    
    console.log(`✅ 通过测试: ${results.passed}`);
    console.log(`❌ 失败测试: ${results.failed}`);
    console.log(`📊 总计测试: ${results.tests.length}`);
    console.log(`📈 成功率: ${Math.round((results.passed / results.tests.length) * 100)}%`);
    
    console.log('\n📋 详细结果:');
    results.tests.forEach((test, index) => {
        const icon = test.passed ? '✅' : '❌';
        console.log(`${index + 1}. ${icon} ${test.message}`);
    });
    
    if (results.failed === 0) {
        console.log('\n🎉 PC端布局验证完全成功！');
        console.log('💡 难度选择器和所有UI元素都能正常显示和访问');
        
        // 显示成功通知
        if (window.notificationSystem) {
            setTimeout(() => {
                window.notificationSystem.success(
                    '所有UI元素都能正常显示，难度选择器不再超出屏幕。',
                    { title: 'PC端布局验证成功', duration: 5000 }
                );
            }, 1000);
        }
    } else {
        console.log('\n⚠️ PC端布局仍有问题需要解决');
        
        const criticalIssues = results.tests.filter(test => 
            !test.passed && (
                test.message.includes('难度选择器不在视口内') ||
                test.message.includes('开始游戏按钮不在视口内')
            )
        );
        
        if (criticalIssues.length > 0) {
            console.log('\n🚨 关键问题:');
            criticalIssues.forEach(issue => {
                console.log(`  - ${issue.message}`);
            });
        }
        
        // 显示警告通知
        if (window.notificationSystem) {
            setTimeout(() => {
                window.notificationSystem.warning(
                    `${results.failed} 个测试失败，请检查控制台了解详情。`,
                    { title: 'PC端布局验证发现问题', duration: 6000 }
                );
            }, 1000);
        }
    }
    
    console.log('='.repeat(60));
}

// 添加手动验证函数
window.verifyPCLayout = function() {
    console.log('🖥️ 开始手动PC端布局验证...');
    runPCLayoutVerification();
};

console.log('🖥️ PC端布局验证脚本已加载');
console.log('💡 可以在控制台运行 verifyPCLayout() 进行手动验证');
