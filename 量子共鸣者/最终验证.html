<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量子共鸣者 - 最终验证测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #ffffff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            color: #00d4ff;
            margin-bottom: 30px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            font-size: 2.5em;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            border-left: 4px solid #00d4ff;
        }
        
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.3);
            border-left: 4px solid #666;
            display: flex;
            align-items: center;
        }
        
        .pass { 
            border-left-color: #00ff00; 
            background: rgba(0, 255, 0, 0.1);
        }
        
        .fail { 
            border-left-color: #ff0000; 
            background: rgba(255, 0, 0, 0.1);
        }
        
        .status-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .status-pass { background: #00ff00; box-shadow: 0 0 10px rgba(0, 255, 0, 0.5); }
        .status-fail { background: #ff0000; box-shadow: 0 0 10px rgba(255, 0, 0, 0.5); }
        
        .summary {
            background: rgba(0, 212, 255, 0.2);
            border: 2px solid #00d4ff;
            padding: 25px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
        }
        
        button {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        
        button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
        }
        
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4ff, #00ff88);
            width: 0%;
            transition: width 0.5s ease;
        }
        
        .emoji {
            font-size: 1.5em;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 量子共鸣者 - 最终验证测试</h1>
        
        <div class="controls">
            <button onclick="runFinalTest()">🚀 运行最终测试</button>
            <button onclick="testI18nOnly()">🌐 仅测试 i18n</button>
            <button onclick="clearResults()">🧹 清空结果</button>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progress"></div>
        </div>
        
        <div id="test-sections"></div>
        
        <div id="final-summary" style="display: none;"></div>
    </div>

    <!-- 必要的DOM元素 -->
    <div id="loadingScreen" style="display: none;">
        <div id="loadingProgress" style="width: 0%; height: 4px; background: #00d4ff;"></div>
        <div id="loadingText">0%</div>
    </div>
    
    <div id="mainMenu" style="display: none;">
        <button id="startGameButton">开始游戏</button>
        <button id="levelSelectButton">选择关卡</button>
        <button id="settingsButton">设置</button>
        <button id="aboutButton">关于</button>
    </div>
    
    <div id="settingsScreen" style="display: none;"></div>
    <div id="aboutScreen" style="display: none;"></div>
    <div id="levelSelectScreen" style="display: none;"></div>
    <canvas id="game-canvas" style="display: none;" width="800" height="600"></canvas>
    <button id="initAudioButton" style="display: none;">初始化音频</button>

    <!-- 加载所有关键脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/ui/level-select.js"></script>
    <script src="js/game/game-controller.js"></script>

    <script>
        let testResults = [];
        let currentProgress = 0;
        let totalTests = 0;

        function updateProgress(current, total) {
            const percentage = (current / total) * 100;
            document.getElementById('progress').style.width = percentage + '%';
        }

        function createTestSection(title, emoji) {
            const section = document.createElement('div');
            section.className = 'test-section';
            section.innerHTML = `<h3><span class="emoji">${emoji}</span>${title}</h3>`;
            document.getElementById('test-sections').appendChild(section);
            return section;
        }

        function addResult(section, name, passed, message) {
            const result = { name, passed, message };
            testResults.push(result);
            
            const div = document.createElement('div');
            div.className = `test-result ${passed ? 'pass' : 'fail'}`;
            div.innerHTML = `
                <span class="status-indicator ${passed ? 'status-pass' : 'status-fail'}"></span>
                <div>
                    <strong>${name}:</strong><br>
                    <span style="opacity: 0.9;">${message}</span>
                </div>
            `;
            section.appendChild(div);
            
            currentProgress++;
            updateProgress(currentProgress, totalTests);
        }

        async function runFinalTest() {
            testResults = [];
            currentProgress = 0;
            totalTests = 12; // 预计测试数量
            
            document.getElementById('test-sections').innerHTML = '';
            document.getElementById('final-summary').style.display = 'none';
            updateProgress(0, totalTests);
            
            console.log('🎯 开始运行最终验证测试...');
            
            // 等待页面完全加载
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 测试 i18n 服务
            await testI18nService();
            
            // 测试游戏控制器
            await testGameController();
            
            // 测试关卡选择
            await testLevelSelect();
            
            // 测试应用程序初始化
            await testAppInitialization();
            
            // 生成最终报告
            generateFinalReport();
        }

        async function testI18nService() {
            const section = createTestSection('国际化服务测试', '🌐');
            
            // 测试 i18nService 存在
            const i18nServiceExists = typeof window.i18nService !== 'undefined';
            addResult(section, 'i18nService 存在', i18nServiceExists, 
                i18nServiceExists ? '✅ window.i18nService 已创建' : '❌ window.i18nService 不存在');
            
            // 测试 i18n 别名存在
            const i18nExists = typeof window.i18n !== 'undefined';
            addResult(section, 'i18n 别名存在', i18nExists, 
                i18nExists ? '✅ window.i18n 别名已创建' : '❌ window.i18n 别名不存在');
            
            // 测试 init 方法存在
            if (i18nExists) {
                const hasInit = typeof window.i18n.init === 'function';
                addResult(section, 'init 方法存在', hasInit, 
                    hasInit ? '✅ i18n.init 方法已定义' : '❌ i18n.init 方法不存在');
                
                // 测试 init 方法调用
                if (hasInit) {
                    try {
                        const result = await window.i18n.init();
                        addResult(section, 'init 方法调用', true, 
                            `✅ i18n.init() 调用成功，返回值: ${result}`);
                    } catch (error) {
                        addResult(section, 'init 方法调用', false, 
                            `❌ i18n.init() 调用失败: ${error.message}`);
                    }
                }
            }
        }

        async function testGameController() {
            const section = createTestSection('游戏控制器测试', '🎮');
            
            // 测试游戏控制器存在
            const gameControllerExists = typeof window.gameController !== 'undefined';
            addResult(section, '游戏控制器存在', gameControllerExists, 
                gameControllerExists ? '✅ window.gameController 已创建' : '❌ window.gameController 不存在');
            
            // 测试游戏控制器初始化
            if (gameControllerExists) {
                // 等待自动初始化完成
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const initialized = window.gameController.isInitialized === true;
                addResult(section, '游戏控制器初始化', initialized, 
                    initialized ? '✅ 游戏控制器已成功初始化' : '❌ 游戏控制器未初始化');
            }
        }

        async function testLevelSelect() {
            const section = createTestSection('关卡选择测试', '🎯');
            
            // 测试关卡选择存在
            const levelSelectExists = typeof window.levelSelect !== 'undefined';
            addResult(section, '关卡选择存在', levelSelectExists, 
                levelSelectExists ? '✅ window.levelSelect 已创建' : '❌ window.levelSelect 不存在');
            
            if (levelSelectExists) {
                // 测试 startLevel 方法
                const hasStartLevel = typeof window.levelSelect.startLevel === 'function';
                addResult(section, 'startLevel 方法', hasStartLevel, 
                    hasStartLevel ? '✅ startLevel 方法存在' : '❌ startLevel 方法不存在');
                
                // 测试 hideWithoutReturnToMenu 方法
                const hasHideMethod = typeof window.levelSelect.hideWithoutReturnToMenu === 'function';
                addResult(section, 'hideWithoutReturnToMenu 方法', hasHideMethod, 
                    hasHideMethod ? '✅ hideWithoutReturnToMenu 方法存在' : '❌ hideWithoutReturnToMenu 方法不存在');
                
                // 测试方法调用
                if (hasHideMethod) {
                    try {
                        window.levelSelect.hideWithoutReturnToMenu();
                        addResult(section, '方法调用测试', true, '✅ hideWithoutReturnToMenu 方法调用成功');
                    } catch (error) {
                        addResult(section, '方法调用测试', false, `❌ 方法调用失败: ${error.message}`);
                    }
                }
            }
        }

        async function testAppInitialization() {
            const section = createTestSection('应用程序初始化测试', '🚀');
            
            // 模拟应用程序初始化
            try {
                if (window.i18n && typeof window.i18n.init === 'function') {
                    await window.i18n.init();
                    addResult(section, '应用程序 i18n 初始化', true, '✅ 应用程序成功初始化 i18n 服务');
                } else {
                    addResult(section, '应用程序 i18n 初始化', false, '❌ 无法初始化 i18n 服务');
                }
            } catch (error) {
                addResult(section, '应用程序 i18n 初始化', false, `❌ 初始化失败: ${error.message}`);
            }
        }

        async function testI18nOnly() {
            testResults = [];
            currentProgress = 0;
            totalTests = 4;
            
            document.getElementById('test-sections').innerHTML = '';
            document.getElementById('final-summary').style.display = 'none';
            updateProgress(0, totalTests);
            
            await testI18nService();
            generateFinalReport();
        }

        function generateFinalReport() {
            const passed = testResults.filter(r => r.passed).length;
            const total = testResults.length;
            const success = passed === total;
            
            const summaryDiv = document.getElementById('final-summary');
            summaryDiv.style.display = 'block';
            summaryDiv.className = `summary`;
            
            const emoji = success ? '🎉' : '😞';
            const status = success ? '完美通过' : '部分失败';
            const color = success ? '#00ff00' : '#ff6600';
            
            summaryDiv.innerHTML = `
                <div style="font-size: 3em; margin-bottom: 20px;">${emoji}</div>
                <div style="color: ${color}; font-size: 1.5em; margin-bottom: 15px;">
                    最终测试结果: ${passed}/${total} 个测试${status}
                </div>
                <div style="font-size: 1.2em; opacity: 0.9;">
                    ${success ? 
                        '🎊 恭喜！量子共鸣者关卡选择功能已完全修复！<br>所有核心组件都正常工作，可以开始游戏了！' : 
                        '⚠️ 部分功能仍需修复，请查看上方详细测试结果。'
                    }
                </div>
            `;
            
            updateProgress(total, total);
        }

        function clearResults() {
            document.getElementById('test-sections').innerHTML = '';
            document.getElementById('final-summary').style.display = 'none';
            updateProgress(0, 1);
            testResults = [];
            currentProgress = 0;
        }

        // 页面加载完成后显示准备状态
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 最终验证页面加载完成');
            
            // 等待组件初始化后自动运行测试
            setTimeout(() => {
                console.log('⏰ 开始自动运行最终测试...');
                runFinalTest();
            }, 3000);
        });
    </script>
</body>
</html>
