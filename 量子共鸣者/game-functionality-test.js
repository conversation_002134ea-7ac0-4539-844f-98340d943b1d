/**
 * 量子共鸣者 - 游戏功能测试脚本
 * 测试游戏的核心功能和游戏逻辑
 */

class GameFunctionalityTest {
    constructor() {
        this.testResults = {
            passed: 0,
            failed: 0,
            total: 0,
            details: []
        };
        
        console.log('🎮 游戏功能测试器已创建');
    }

    /**
     * 运行完整的游戏功能测试
     */
    async runGameFunctionalityTest() {
        console.log('='.repeat(50));
        console.log('🎮 游戏功能测试开始');
        console.log('='.repeat(50));
        
        // 等待游戏初始化完成
        await this.waitForGameInitialization();
        
        // 测试游戏核心功能
        this.testGameInitialization();
        this.testLevelSystem();
        this.testPlayerSystem();
        this.testAchievementSystem();
        this.testLeaderboardSystem();
        this.testAudioSystem();
        this.testRenderSystem();
        this.testInputSystem();
        this.testStorageSystem();
        this.testUISystem();
        
        // 生成测试报告
        this.generateTestReport();
        
        console.log('='.repeat(50));
        console.log('🎮 游戏功能测试完成');
        console.log('='.repeat(50));
    }

    /**
     * 等待游戏初始化完成
     */
    async waitForGameInitialization() {
        return new Promise((resolve) => {
            const checkInitialization = () => {
                if (window.gameController && window.gameController.isInitialized) {
                    resolve();
                } else {
                    setTimeout(checkInitialization, 100);
                }
            };
            checkInitialization();
        });
    }

    /**
     * 测试辅助函数
     */
    runTest(testName, testFunction) {
        this.testResults.total++;
        
        try {
            const result = testFunction();
            if (result) {
                console.log(`✅ ${testName}: 通过`);
                this.testResults.passed++;
                this.testResults.details.push({ name: testName, status: 'passed', error: null });
            } else {
                console.log(`❌ ${testName}: 失败`);
                this.testResults.failed++;
                this.testResults.details.push({ name: testName, status: 'failed', error: 'Test returned false' });
            }
        } catch (error) {
            console.log(`❌ ${testName}: 异常 - ${error.message}`);
            this.testResults.failed++;
            this.testResults.details.push({ name: testName, status: 'error', error: error.message });
        }
    }

    /**
     * 测试游戏初始化
     */
    testGameInitialization() {
        console.log('\n🎮 测试游戏初始化...');
        
        this.runTest('游戏控制器存在', () => {
            return window.gameController !== undefined;
        });
        
        this.runTest('游戏控制器已初始化', () => {
            return window.gameController && gameController.isInitialized === true;
        });
        
        this.runTest('游戏状态正确', () => {
            return window.gameController && gameController.gameState === 'menu';
        });
    }

    /**
     * 测试关卡系统
     */
    testLevelSystem() {
        console.log('\n🎯 测试关卡系统...');
        
        this.runTest('关卡管理器存在', () => {
            return window.levelManager !== undefined;
        });
        
        this.runTest('内置关卡加载', () => {
            return window.levelManager && levelManager.builtInLevels && levelManager.builtInLevels.length > 0;
        });
        
        this.runTest('关卡验证功能', () => {
            if (!window.levelManager) return false;
            
            const testLevel = {
                id: 'test-level',
                name: '测试关卡',
                particles: [
                    { id: 'p1', x: 100, y: 100, frequency: 440, energy: 1 }
                ],
                connections: [],
                objectives: { targetScore: 1000 }
            };
            
            const validation = levelManager.validateLevel(testLevel);
            return validation.isValid;
        });
        
        this.runTest('关卡编辑器存在', () => {
            return window.levelEditor !== undefined;
        });
    }

    /**
     * 测试玩家系统
     */
    testPlayerSystem() {
        console.log('\n👤 测试玩家系统...');
        
        this.runTest('玩家管理器存在', () => {
            return window.playerManager !== undefined;
        });
        
        this.runTest('默认玩家创建', () => {
            if (!window.playerManager) return false;
            
            const player = playerManager.getCurrentPlayer();
            return player !== null;
        });
        
        this.runTest('玩家统计更新', () => {
            if (!window.playerManager) return false;
            
            const player = playerManager.getCurrentPlayer();
            if (!player) return false;
            
            const originalGames = player.stats.gamesPlayed;
            playerManager.updatePlayerStats({ gamesPlayed: 1 });
            
            const updatedPlayer = playerManager.getCurrentPlayer();
            return updatedPlayer.stats.gamesPlayed === originalGames + 1;
        });
    }

    /**
     * 测试成就系统
     */
    testAchievementSystem() {
        console.log('\n🏅 测试成就系统...');
        
        this.runTest('成就系统存在', () => {
            return window.playerManager && playerManager.achievements && playerManager.achievements.size > 0;
        });
        
        this.runTest('成就检查功能', () => {
            if (!window.playerManager) return false;
            
            const achievements = playerManager.checkAchievements();
            return Array.isArray(achievements);
        });
        
        this.runTest('成就UI存在', () => {
            return window.achievementsUI !== undefined;
        });
        
        this.runTest('成就UI初始化', () => {
            return window.achievementsUI && typeof achievementsUI.init === 'function';
        });
    }

    /**
     * 测试排行榜系统
     */
    testLeaderboardSystem() {
        console.log('\n🏆 测试排行榜系统...');
        
        this.runTest('排行榜功能存在', () => {
            return window.playerManager && typeof playerManager.getLeaderboard === 'function';
        });
        
        this.runTest('排行榜数据获取', () => {
            if (!window.playerManager) return false;
            
            const leaderboard = playerManager.getLeaderboard('highScores', 10);
            return Array.isArray(leaderboard);
        });
        
        this.runTest('排行榜UI存在', () => {
            return window.leaderboardUI !== undefined;
        });
        
        this.runTest('排行榜更新功能', () => {
            if (!window.playerManager) return false;
            
            const testEntry = {
                score: 1000,
                time: 60,
                combo: 10,
                chainReactions: 5,
                levelName: '测试关卡',
                timestamp: Date.now()
            };
            
            try {
                playerManager.updateLeaderboard(testEntry);
                return true;
            } catch (error) {
                return false;
            }
        });
    }

    /**
     * 测试音频系统
     */
    testAudioSystem() {
        console.log('\n🎵 测试音频系统...');
        
        this.runTest('音频引擎存在', () => {
            return window.audioEngine !== undefined;
        });
        
        this.runTest('音频上下文创建', () => {
            return window.audioEngine && audioEngine.audioContext !== null;
        });
        
        this.runTest('音频初始化', () => {
            return window.audioEngine && audioEngine.isInitialized === true;
        });
        
        this.runTest('音频播放功能', () => {
            if (!window.audioEngine) return false;
            
            try {
                // 测试播放一个简单的音调
                audioEngine.playTone(440, 0.1, 0.1);
                return true;
            } catch (error) {
                return false;
            }
        });
    }

    /**
     * 测试渲染系统
     */
    testRenderSystem() {
        console.log('\n🎨 测试渲染系统...');
        
        this.runTest('渲染引擎存在', () => {
            return window.renderEngine !== undefined;
        });
        
        this.runTest('Canvas元素获取', () => {
            return window.renderEngine && renderEngine.canvas !== null;
        });
        
        this.runTest('渲染上下文创建', () => {
            return window.renderEngine && renderEngine.ctx !== null;
        });
        
        this.runTest('渲染功能测试', () => {
            if (!window.renderEngine) return false;
            
            try {
                renderEngine.render(1.0);
                return true;
            } catch (error) {
                return false;
            }
        });
    }

    /**
     * 测试输入系统
     */
    testInputSystem() {
        console.log('\n🖱️ 测试输入系统...');
        
        this.runTest('输入管理器存在', () => {
            return window.gameController && gameController.inputManager !== undefined;
        });
        
        this.runTest('鼠标事件处理', () => {
            if (!window.gameController || !gameController.inputManager) return false;
            
            const inputManager = gameController.inputManager;
            return typeof inputManager.handleMouseClick === 'function';
        });
        
        this.runTest('键盘事件处理', () => {
            if (!window.gameController || !gameController.inputManager) return false;
            
            const inputManager = gameController.inputManager;
            return typeof inputManager.handleKeyPress === 'function';
        });
    }

    /**
     * 测试存储系统
     */
    testStorageSystem() {
        console.log('\n💾 测试存储系统...');
        
        this.runTest('存储服务存在', () => {
            return window.storageService !== undefined;
        });
        
        this.runTest('存储读写功能', () => {
            if (!window.storageService) return false;
            
            const testKey = 'game-test-key';
            const testValue = { test: 'value', timestamp: Date.now() };
            
            try {
                storageService.put(testKey, testValue);
                const retrieved = storageService.get(testKey);
                storageService.remove(testKey);
                
                return retrieved && retrieved.test === testValue.test;
            } catch (error) {
                return false;
            }
        });
        
        this.runTest('玩家数据持久化', () => {
            if (!window.playerManager) return false;
            
            try {
                const player = playerManager.getCurrentPlayer();
                playerManager.savePlayerData();
                return player !== null;
            } catch (error) {
                return false;
            }
        });
    }

    /**
     * 测试UI系统
     */
    testUISystem() {
        console.log('\n🖥️ 测试UI系统...');
        
        this.runTest('UI管理器存在', () => {
            return window.uiManager !== undefined;
        });
        
        this.runTest('屏幕切换功能', () => {
            return window.uiManager && typeof uiManager.showScreen === 'function';
        });
        
        this.runTest('模态框功能', () => {
            return window.uiManager && typeof uiManager.showModal === 'function';
        });
        
        this.runTest('国际化功能', () => {
            return window.i18n && typeof i18n.t === 'function';
        });
    }

    /**
     * 生成测试报告
     */
    generateTestReport() {
        console.log('\n📊 游戏功能测试报告:');
        console.log(`   通过: ${this.testResults.passed}`);
        console.log(`   失败: ${this.testResults.failed}`);
        console.log(`   总计: ${this.testResults.total}`);
        console.log(`   成功率: ${((this.testResults.passed / this.testResults.total) * 100).toFixed(1)}%`);
        
        if (this.testResults.failed > 0) {
            console.log('\n❌ 失败的测试:');
            this.testResults.details
                .filter(test => test.status !== 'passed')
                .forEach(test => {
                    console.log(`   - ${test.name}: ${test.error || '测试失败'}`);
                });
        } else {
            console.log('\n🎉 所有游戏功能测试通过！');
        }
    }

    /**
     * 导出测试结果
     */
    exportResults() {
        const results = {
            timestamp: new Date().toISOString(),
            summary: {
                passed: this.testResults.passed,
                failed: this.testResults.failed,
                total: this.testResults.total,
                successRate: (this.testResults.passed / this.testResults.total) * 100
            },
            details: this.testResults.details
        };
        
        const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `game-functionality-test-${Date.now()}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        
        console.log('📁 游戏功能测试结果已导出');
    }
}

// 创建全局游戏功能测试实例
window.gameFunctionalityTest = new GameFunctionalityTest();

// 添加快捷方法到控制台
window.runGameFunctionalityTest = () => gameFunctionalityTest.runGameFunctionalityTest();
window.exportGameTestResults = () => gameFunctionalityTest.exportResults();

console.log('🎮 游戏功能测试器已加载');
console.log('💡 使用 runGameFunctionalityTest() 开始测试');
console.log('💡 使用 exportGameTestResults() 导出结果');

// 自动运行游戏功能测试
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        gameFunctionalityTest.runGameFunctionalityTest();
    }, 3000); // 等待3秒确保所有系统初始化完成
});
