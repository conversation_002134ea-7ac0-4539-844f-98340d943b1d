/**
 * 量子共鸣者 - 性能测试脚本
 * 用于测试游戏的性能表现和优化建议
 */

class PerformanceTest {
    constructor() {
        this.metrics = {
            frameRate: [],
            memoryUsage: [],
            renderTime: [],
            audioLatency: [],
            loadTime: {}
        };
        
        this.isRunning = false;
        this.testDuration = 30000; // 30秒测试
        this.startTime = 0;
        
        console.log('🚀 性能测试器已创建');
    }

    /**
     * 开始性能测试
     */
    startTest() {
        if (this.isRunning) {
            console.warn('⚠️ 性能测试已在运行中');
            return;
        }
        
        console.log('🚀 开始性能测试...');
        this.isRunning = true;
        this.startTime = performance.now();
        
        // 重置指标
        this.resetMetrics();
        
        // 开始监控
        this.startMonitoring();
        
        // 设置测试结束定时器
        setTimeout(() => {
            this.stopTest();
        }, this.testDuration);
    }

    /**
     * 停止性能测试
     */
    stopTest() {
        if (!this.isRunning) return;
        
        this.isRunning = false;
        console.log('🏁 性能测试完成');
        
        // 分析结果
        this.analyzeResults();
    }

    /**
     * 重置性能指标
     */
    resetMetrics() {
        this.metrics.frameRate = [];
        this.metrics.memoryUsage = [];
        this.metrics.renderTime = [];
        this.metrics.audioLatency = [];
        this.metrics.loadTime = {};
    }

    /**
     * 开始监控性能指标
     */
    startMonitoring() {
        // 监控帧率
        this.monitorFrameRate();
        
        // 监控内存使用
        this.monitorMemoryUsage();
        
        // 监控渲染时间
        this.monitorRenderTime();
        
        // 监控音频延迟
        this.monitorAudioLatency();
        
        // 测试加载时间
        this.testLoadTimes();
    }

    /**
     * 监控帧率
     */
    monitorFrameRate() {
        let lastTime = performance.now();
        let frameCount = 0;
        
        const measureFPS = () => {
            if (!this.isRunning) return;
            
            const currentTime = performance.now();
            frameCount++;
            
            // 每秒计算一次FPS
            if (currentTime - lastTime >= 1000) {
                const fps = frameCount * 1000 / (currentTime - lastTime);
                this.metrics.frameRate.push(fps);
                
                frameCount = 0;
                lastTime = currentTime;
            }
            
            requestAnimationFrame(measureFPS);
        };
        
        requestAnimationFrame(measureFPS);
    }

    /**
     * 监控内存使用
     */
    monitorMemoryUsage() {
        const measureMemory = () => {
            if (!this.isRunning) return;
            
            if (performance.memory) {
                const memory = {
                    used: performance.memory.usedJSHeapSize / 1024 / 1024, // MB
                    total: performance.memory.totalJSHeapSize / 1024 / 1024, // MB
                    limit: performance.memory.jsHeapSizeLimit / 1024 / 1024 // MB
                };
                
                this.metrics.memoryUsage.push(memory);
            }
            
            setTimeout(measureMemory, 1000); // 每秒测量一次
        };
        
        measureMemory();
    }

    /**
     * 监控渲染时间
     */
    monitorRenderTime() {
        if (!window.renderEngine) return;
        
        const originalRender = renderEngine.render;
        
        renderEngine.render = (alpha) => {
            const startTime = performance.now();
            originalRender.call(renderEngine, alpha);
            const endTime = performance.now();
            
            if (this.isRunning) {
                this.metrics.renderTime.push(endTime - startTime);
            }
        };
    }

    /**
     * 监控音频延迟
     */
    monitorAudioLatency() {
        if (!window.audioEngine || !audioEngine.audioContext) return;
        
        const measureLatency = () => {
            if (!this.isRunning) return;
            
            const latency = audioEngine.audioContext.baseLatency || 0;
            this.metrics.audioLatency.push(latency * 1000); // 转换为毫秒
            
            setTimeout(measureLatency, 2000); // 每2秒测量一次
        };
        
        measureLatency();
    }

    /**
     * 测试加载时间
     */
    testLoadTimes() {
        // 测试图片加载时间
        this.testImageLoad();
        
        // 测试音频加载时间
        this.testAudioLoad();
        
        // 测试脚本加载时间
        this.testScriptLoad();
    }

    /**
     * 测试图片加载时间
     */
    testImageLoad() {
        const testImage = new Image();
        const startTime = performance.now();
        
        testImage.onload = () => {
            this.metrics.loadTime.image = performance.now() - startTime;
        };
        
        testImage.onerror = () => {
            this.metrics.loadTime.image = -1; // 加载失败
        };
        
        // 使用一个小的测试图片
        testImage.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    }

    /**
     * 测试音频加载时间
     */
    testAudioLoad() {
        if (!window.audioEngine || !audioEngine.audioContext) return;
        
        const startTime = performance.now();
        
        try {
            // 创建一个简单的音频缓冲区
            const buffer = audioEngine.audioContext.createBuffer(1, 44100, 44100);
            this.metrics.loadTime.audio = performance.now() - startTime;
        } catch (error) {
            this.metrics.loadTime.audio = -1;
        }
    }

    /**
     * 测试脚本加载时间
     */
    testScriptLoad() {
        const startTime = performance.now();
        
        // 创建一个小的测试脚本
        const script = document.createElement('script');
        script.textContent = 'window.testScriptLoaded = true;';
        
        script.onload = () => {
            this.metrics.loadTime.script = performance.now() - startTime;
            document.head.removeChild(script);
        };
        
        document.head.appendChild(script);
    }

    /**
     * 分析测试结果
     */
    analyzeResults() {
        console.log('='.repeat(50));
        console.log('📊 性能测试结果分析');
        console.log('='.repeat(50));
        
        // 分析帧率
        this.analyzeFrameRate();
        
        // 分析内存使用
        this.analyzeMemoryUsage();
        
        // 分析渲染时间
        this.analyzeRenderTime();
        
        // 分析音频延迟
        this.analyzeAudioLatency();
        
        // 分析加载时间
        this.analyzeLoadTimes();
        
        // 生成优化建议
        this.generateOptimizationSuggestions();
        
        console.log('='.repeat(50));
    }

    /**
     * 分析帧率
     */
    analyzeFrameRate() {
        if (this.metrics.frameRate.length === 0) return;
        
        const fps = this.metrics.frameRate;
        const avgFPS = fps.reduce((a, b) => a + b, 0) / fps.length;
        const minFPS = Math.min(...fps);
        const maxFPS = Math.max(...fps);
        
        console.log('\n🎯 帧率分析:');
        console.log(`   平均FPS: ${avgFPS.toFixed(1)}`);
        console.log(`   最低FPS: ${minFPS.toFixed(1)}`);
        console.log(`   最高FPS: ${maxFPS.toFixed(1)}`);
        
        if (avgFPS >= 60) {
            console.log('   ✅ 帧率表现优秀');
        } else if (avgFPS >= 30) {
            console.log('   ⚠️ 帧率表现一般，建议优化');
        } else {
            console.log('   ❌ 帧率表现较差，需要优化');
        }
    }

    /**
     * 分析内存使用
     */
    analyzeMemoryUsage() {
        if (this.metrics.memoryUsage.length === 0) return;
        
        const memory = this.metrics.memoryUsage;
        const avgUsed = memory.reduce((a, b) => a + b.used, 0) / memory.length;
        const maxUsed = Math.max(...memory.map(m => m.used));
        const avgTotal = memory.reduce((a, b) => a + b.total, 0) / memory.length;
        
        console.log('\n💾 内存使用分析:');
        console.log(`   平均使用: ${avgUsed.toFixed(1)} MB`);
        console.log(`   峰值使用: ${maxUsed.toFixed(1)} MB`);
        console.log(`   平均总量: ${avgTotal.toFixed(1)} MB`);
        console.log(`   使用率: ${((avgUsed / avgTotal) * 100).toFixed(1)}%`);
        
        if (avgUsed < 50) {
            console.log('   ✅ 内存使用良好');
        } else if (avgUsed < 100) {
            console.log('   ⚠️ 内存使用中等');
        } else {
            console.log('   ❌ 内存使用较高，建议优化');
        }
    }

    /**
     * 分析渲染时间
     */
    analyzeRenderTime() {
        if (this.metrics.renderTime.length === 0) return;
        
        const renderTimes = this.metrics.renderTime;
        const avgTime = renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length;
        const maxTime = Math.max(...renderTimes);
        
        console.log('\n🎨 渲染性能分析:');
        console.log(`   平均渲染时间: ${avgTime.toFixed(2)} ms`);
        console.log(`   最长渲染时间: ${maxTime.toFixed(2)} ms`);
        
        if (avgTime < 16.67) { // 60 FPS = 16.67ms per frame
            console.log('   ✅ 渲染性能优秀');
        } else if (avgTime < 33.33) { // 30 FPS = 33.33ms per frame
            console.log('   ⚠️ 渲染性能一般');
        } else {
            console.log('   ❌ 渲染性能较差');
        }
    }

    /**
     * 分析音频延迟
     */
    analyzeAudioLatency() {
        if (this.metrics.audioLatency.length === 0) return;
        
        const latencies = this.metrics.audioLatency;
        const avgLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
        
        console.log('\n🎵 音频延迟分析:');
        console.log(`   平均延迟: ${avgLatency.toFixed(2)} ms`);
        
        if (avgLatency < 20) {
            console.log('   ✅ 音频延迟优秀');
        } else if (avgLatency < 50) {
            console.log('   ⚠️ 音频延迟可接受');
        } else {
            console.log('   ❌ 音频延迟较高');
        }
    }

    /**
     * 分析加载时间
     */
    analyzeLoadTimes() {
        console.log('\n⏱️ 加载时间分析:');
        
        Object.entries(this.metrics.loadTime).forEach(([type, time]) => {
            if (time === -1) {
                console.log(`   ${type}: 加载失败`);
            } else {
                console.log(`   ${type}: ${time.toFixed(2)} ms`);
            }
        });
    }

    /**
     * 生成优化建议
     */
    generateOptimizationSuggestions() {
        console.log('\n💡 优化建议:');
        
        const suggestions = [];
        
        // 基于帧率的建议
        if (this.metrics.frameRate.length > 0) {
            const avgFPS = this.metrics.frameRate.reduce((a, b) => a + b, 0) / this.metrics.frameRate.length;
            if (avgFPS < 30) {
                suggestions.push('考虑减少粒子数量或简化渲染效果');
                suggestions.push('使用对象池来减少垃圾回收');
            }
        }
        
        // 基于内存的建议
        if (this.metrics.memoryUsage.length > 0) {
            const avgUsed = this.metrics.memoryUsage.reduce((a, b) => a + b.used, 0) / this.metrics.memoryUsage.length;
            if (avgUsed > 100) {
                suggestions.push('检查内存泄漏，及时清理不用的对象');
                suggestions.push('考虑使用更高效的数据结构');
            }
        }
        
        // 基于渲染时间的建议
        if (this.metrics.renderTime.length > 0) {
            const avgTime = this.metrics.renderTime.reduce((a, b) => a + b, 0) / this.metrics.renderTime.length;
            if (avgTime > 16.67) {
                suggestions.push('优化渲染管道，减少不必要的绘制调用');
                suggestions.push('考虑使用Canvas离屏渲染或WebGL优化');
            }
        }
        
        if (suggestions.length === 0) {
            console.log('   🎉 性能表现良好，无需特别优化！');
        } else {
            suggestions.forEach((suggestion, index) => {
                console.log(`   ${index + 1}. ${suggestion}`);
            });
        }
    }

    /**
     * 导出测试结果
     */
    exportResults() {
        const results = {
            timestamp: new Date().toISOString(),
            testDuration: this.testDuration,
            metrics: this.metrics,
            summary: this.generateSummary()
        };
        
        const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `performance-test-${Date.now()}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        
        console.log('📁 测试结果已导出');
    }

    /**
     * 生成测试摘要
     */
    generateSummary() {
        const summary = {};
        
        if (this.metrics.frameRate.length > 0) {
            const fps = this.metrics.frameRate;
            summary.frameRate = {
                avg: fps.reduce((a, b) => a + b, 0) / fps.length,
                min: Math.min(...fps),
                max: Math.max(...fps)
            };
        }
        
        if (this.metrics.memoryUsage.length > 0) {
            const memory = this.metrics.memoryUsage;
            summary.memory = {
                avgUsed: memory.reduce((a, b) => a + b.used, 0) / memory.length,
                maxUsed: Math.max(...memory.map(m => m.used))
            };
        }
        
        return summary;
    }
}

// 创建全局性能测试实例
window.performanceTest = new PerformanceTest();

// 添加快捷方法到控制台
window.startPerformanceTest = () => performanceTest.startTest();
window.stopPerformanceTest = () => performanceTest.stopTest();
window.exportPerformanceResults = () => performanceTest.exportResults();

console.log('🚀 性能测试器已加载');
console.log('💡 使用 startPerformanceTest() 开始测试');
console.log('💡 使用 stopPerformanceTest() 停止测试');
console.log('💡 使用 exportPerformanceResults() 导出结果');
