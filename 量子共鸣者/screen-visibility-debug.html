<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>屏幕显示问题调试 - 量子共鸣者</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #0d1421;
            color: #ffffff;
        }
        
        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 8px;
            z-index: 1000;
            max-width: 300px;
        }
        
        .debug-info {
            margin: 5px 0;
            font-size: 12px;
        }
        
        .debug-button {
            margin: 5px;
            padding: 8px 12px;
            background: #4a90e2;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .debug-button:hover {
            background: #357abd;
        }
        
        /* 原始CSS样式 - 来自main.css */
        .screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease-in-out;
            z-index: 1;
        }

        .screen.active {
            opacity: 1;
            visibility: visible;
        }
        
        /* 测试屏幕样式 */
        #test-screen-1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        #test-screen-2 {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        #game-screen {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            flex-direction: column;
        }
        
        .screen-content {
            text-align: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }
        
        .screen-title {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .screen-description {
            font-size: 1.2em;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .game-hud {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
        }
        
        .hud-info {
            color: #ffffff;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="debug-panel">
        <h3>屏幕显示调试面板</h3>
        <div class="debug-info" id="current-screen">当前屏幕: 无</div>
        <div class="debug-info" id="screen-count">屏幕数量: 0</div>
        <div class="debug-info" id="active-screens">活动屏幕: 0</div>
        
        <div style="margin-top: 15px;">
            <button class="debug-button" onclick="showScreen('test-screen-1')">显示测试屏幕1</button>
            <button class="debug-button" onclick="showScreen('test-screen-2')">显示测试屏幕2</button>
            <button class="debug-button" onclick="showScreen('game-screen')">显示游戏屏幕</button>
            <button class="debug-button" onclick="hideAllScreens()">隐藏所有屏幕</button>
            <button class="debug-button" onclick="debugScreenStates()">调试屏幕状态</button>
        </div>
    </div>

    <!-- 测试屏幕1 -->
    <div id="test-screen-1" class="screen active">
        <div class="screen-content">
            <h1 class="screen-title">测试屏幕 1</h1>
            <p class="screen-description">这是第一个测试屏幕，用于验证屏幕显示机制</p>
            <button class="debug-button" onclick="showScreen('test-screen-2')">切换到屏幕2</button>
        </div>
    </div>

    <!-- 测试屏幕2 -->
    <div id="test-screen-2" class="screen">
        <div class="screen-content">
            <h1 class="screen-title">测试屏幕 2</h1>
            <p class="screen-description">这是第二个测试屏幕，用于验证屏幕切换功能</p>
            <button class="debug-button" onclick="showScreen('game-screen')">切换到游戏屏幕</button>
        </div>
    </div>

    <!-- 游戏屏幕 -->
    <div id="game-screen" class="screen">
        <div class="game-hud">
            <div class="hud-info">关卡: 1</div>
            <div class="hud-info">分数: 0</div>
            <div class="hud-info">时间: 00:00</div>
        </div>
        <div class="screen-content">
            <h1 class="screen-title">量子共鸣者</h1>
            <p class="screen-description">游戏屏幕已成功显示！</p>
            <button class="debug-button" onclick="showScreen('test-screen-1')">返回测试屏幕1</button>
        </div>
    </div>

    <script>
        // 屏幕管理功能
        let currentScreen = 'test-screen-1';
        
        function showScreen(screenId) {
            console.log(`🔄 切换到屏幕: ${screenId}`);
            
            // 隐藏所有屏幕
            const allScreens = document.querySelectorAll('.screen');
            allScreens.forEach(screen => {
                screen.classList.remove('active');
                console.log(`❌ 隐藏屏幕: ${screen.id}`);
            });
            
            // 显示目标屏幕
            const targetScreen = document.getElementById(screenId);
            if (targetScreen) {
                targetScreen.classList.add('active');
                currentScreen = screenId;
                console.log(`✅ 显示屏幕: ${screenId}`);
                updateDebugInfo();
            } else {
                console.error(`❌ 屏幕不存在: ${screenId}`);
            }
        }
        
        function hideAllScreens() {
            console.log('🚫 隐藏所有屏幕');
            const allScreens = document.querySelectorAll('.screen');
            allScreens.forEach(screen => {
                screen.classList.remove('active');
            });
            currentScreen = '无';
            updateDebugInfo();
        }
        
        function debugScreenStates() {
            console.log('🔍 调试屏幕状态:');
            const allScreens = document.querySelectorAll('.screen');
            allScreens.forEach(screen => {
                const computedStyle = window.getComputedStyle(screen);
                const isActive = screen.classList.contains('active');
                
                console.log(`屏幕 ${screen.id}:`);
                console.log(`  - 是否有active类: ${isActive}`);
                console.log(`  - opacity: ${computedStyle.opacity}`);
                console.log(`  - visibility: ${computedStyle.visibility}`);
                console.log(`  - display: ${computedStyle.display}`);
                console.log(`  - z-index: ${computedStyle.zIndex}`);
                console.log('---');
            });
        }
        
        function updateDebugInfo() {
            const allScreens = document.querySelectorAll('.screen');
            const activeScreens = document.querySelectorAll('.screen.active');
            
            document.getElementById('current-screen').textContent = `当前屏幕: ${currentScreen}`;
            document.getElementById('screen-count').textContent = `屏幕数量: ${allScreens.length}`;
            document.getElementById('active-screens').textContent = `活动屏幕: ${activeScreens.length}`;
        }
        
        // 初始化调试信息
        updateDebugInfo();
        
        // 监听键盘事件进行快速切换
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case '1':
                    showScreen('test-screen-1');
                    break;
                case '2':
                    showScreen('test-screen-2');
                    break;
                case '3':
                    showScreen('game-screen');
                    break;
                case 'h':
                    hideAllScreens();
                    break;
                case 'd':
                    debugScreenStates();
                    break;
            }
        });
        
        console.log('🎮 屏幕显示调试工具已加载');
        console.log('💡 使用数字键1-3快速切换屏幕，h键隐藏所有屏幕，d键调试状态');
    </script>
</body>
</html>
