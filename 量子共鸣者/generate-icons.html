<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标生成器 - 量子共鸣者</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0f0f1e;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .icon-preview {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        
        .icon-canvas {
            border: 1px solid #333;
            background: white;
        }
        
        .download-link {
            display: block;
            color: #4a90e2;
            text-decoration: none;
            margin-top: 5px;
        }
        
        .download-link:hover {
            color: #357abd;
        }
        
        .generate-btn {
            background: #4a90e2;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px 0;
        }
        
        .generate-btn:hover {
            background: #357abd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 量子共鸣者图标生成器</h1>
        <p>生成PWA应用所需的各种尺寸图标</p>
        
        <button class="generate-btn" onclick="generateAllIcons()">生成所有图标</button>
        
        <div id="icons-container"></div>
    </div>

    <script>
        // 需要生成的图标尺寸
        const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];
        
        // 生成单个图标
        function generateIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // 创建量子共鸣者主题的图标
            // 背景渐变
            const gradient = ctx.createRadialGradient(size/2, size/2, 0, size/2, size/2, size/2);
            gradient.addColorStop(0, '#4a90e2');
            gradient.addColorStop(0.5, '#1a1a2e');
            gradient.addColorStop(1, '#0f0f1e');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // 绘制量子粒子效果
            const centerX = size / 2;
            const centerY = size / 2;
            const maxRadius = size * 0.4;
            
            // 主要的量子核心
            ctx.fillStyle = '#ffffff';
            ctx.shadowColor = '#4a90e2';
            ctx.shadowBlur = size * 0.05;
            ctx.beginPath();
            ctx.arc(centerX, centerY, size * 0.08, 0, Math.PI * 2);
            ctx.fill();
            ctx.shadowBlur = 0;
            
            // 环绕的量子轨道
            for (let i = 0; i < 3; i++) {
                const radius = maxRadius * (0.3 + i * 0.2);
                const particleCount = 6 + i * 2;
                
                ctx.strokeStyle = `rgba(74, 144, 226, ${0.8 - i * 0.2})`;
                ctx.lineWidth = size * 0.01;
                ctx.setLineDash([size * 0.02, size * 0.01]);
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
                ctx.stroke();
                ctx.setLineDash([]);
                
                // 轨道上的粒子
                for (let j = 0; j < particleCount; j++) {
                    const angle = (j / particleCount) * Math.PI * 2;
                    const x = centerX + Math.cos(angle) * radius;
                    const y = centerY + Math.sin(angle) * radius;
                    
                    ctx.fillStyle = `rgba(255, 255, 255, ${0.9 - i * 0.2})`;
                    ctx.beginPath();
                    ctx.arc(x, y, size * 0.015, 0, Math.PI * 2);
                    ctx.fill();
                }
            }
            
            // 添加能量波纹效果
            for (let i = 0; i < 2; i++) {
                const waveRadius = maxRadius * (0.6 + i * 0.3);
                ctx.strokeStyle = `rgba(74, 144, 226, ${0.3 - i * 0.1})`;
                ctx.lineWidth = size * 0.005;
                ctx.beginPath();
                ctx.arc(centerX, centerY, waveRadius, 0, Math.PI * 2);
                ctx.stroke();
            }
            
            return canvas;
        }
        
        // 生成所有图标
        function generateAllIcons() {
            const container = document.getElementById('icons-container');
            container.innerHTML = '';
            
            iconSizes.forEach(size => {
                const canvas = generateIcon(size);
                canvas.className = 'icon-canvas';
                
                // 创建预览容器
                const previewDiv = document.createElement('div');
                previewDiv.className = 'icon-preview';
                
                // 添加标题
                const title = document.createElement('h3');
                title.textContent = `${size}x${size}`;
                previewDiv.appendChild(title);
                
                // 添加画布
                previewDiv.appendChild(canvas);
                
                // 创建下载链接
                const downloadLink = document.createElement('a');
                downloadLink.className = 'download-link';
                downloadLink.textContent = `下载 icon-${size}x${size}.png`;
                downloadLink.href = canvas.toDataURL('image/png');
                downloadLink.download = `icon-${size}x${size}.png`;
                previewDiv.appendChild(downloadLink);
                
                container.appendChild(previewDiv);
            });
            
            // 显示使用说明
            const instructions = document.createElement('div');
            instructions.innerHTML = `
                <h2>📋 使用说明</h2>
                <p>1. 右键点击每个图标，选择"另存为"保存到 <code>assets/images/</code> 目录</p>
                <p>2. 或者点击下载链接直接下载</p>
                <p>3. 确保文件名与 manifest.json 中的路径匹配</p>
                <p>4. 重新启动服务器以应用新图标</p>
            `;
            instructions.style.marginTop = '30px';
            instructions.style.padding = '20px';
            instructions.style.background = '#1a1a2e';
            instructions.style.borderRadius = '5px';
            container.appendChild(instructions);
        }
        
        // 页面加载完成后自动生成图标
        document.addEventListener('DOMContentLoaded', function() {
            generateAllIcons();
        });
    </script>
</body>
</html>
