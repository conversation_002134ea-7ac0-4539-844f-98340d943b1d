<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量子共鸣者 - 方法测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: rgba(255,255,255,0.1);
        }
        .pass { border-left: 4px solid #00ff00; }
        .fail { border-left: 4px solid #ff0000; }
        button {
            background: #00d4ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .code-block {
            background: #000;
            color: #00ff00;
            font-family: monospace;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🎮 量子共鸣者 - 方法测试</h1>
    <button onclick="testMethods()">测试方法</button>
    <button onclick="inspectLevelSelect()">检查 levelSelect 对象</button>
    <button onclick="testMethodCall()">测试方法调用</button>
    <div id="results"></div>

    <!-- 只加载关卡选择脚本 -->
    <script src="js/ui/level-select.js"></script>

    <script>
        function addResult(name, passed, message) {
            const div = document.createElement('div');
            div.className = `test-result ${passed ? 'pass' : 'fail'}`;
            div.innerHTML = `<strong>${name}:</strong> ${message}`;
            document.getElementById('results').appendChild(div);
        }

        function addCodeBlock(title, code) {
            const div = document.createElement('div');
            div.innerHTML = `<strong>${title}:</strong>`;
            const codeDiv = document.createElement('div');
            codeDiv.className = 'code-block';
            codeDiv.textContent = code;
            div.appendChild(codeDiv);
            document.getElementById('results').appendChild(div);
        }

        function testMethods() {
            document.getElementById('results').innerHTML = '';
            
            // 测试 levelSelect 对象存在
            const levelSelectExists = typeof window.levelSelect !== 'undefined';
            addResult('levelSelect 对象存在', levelSelectExists, 
                levelSelectExists ? '✅ window.levelSelect 存在' : '❌ window.levelSelect 不存在');

            if (!levelSelectExists) {
                return;
            }

            // 测试对象类型
            addResult('levelSelect 类型', true, `类型: ${typeof window.levelSelect}`);
            addResult('levelSelect 构造函数', true, `构造函数: ${window.levelSelect.constructor.name}`);

            // 获取所有方法
            const prototype = Object.getPrototypeOf(window.levelSelect);
            const methods = Object.getOwnPropertyNames(prototype).filter(name => 
                typeof window.levelSelect[name] === 'function' && name !== 'constructor'
            );
            
            addCodeBlock('所有方法', methods.join('\n'));

            // 测试特定方法
            const methodsToTest = [
                'init',
                'show', 
                'hide',
                'startLevel',
                'hideWithoutReturnToMenu'
            ];

            methodsToTest.forEach(methodName => {
                const exists = typeof window.levelSelect[methodName] === 'function';
                addResult(`${methodName} 方法`, exists, 
                    exists ? `✅ ${methodName} 方法存在` : `❌ ${methodName} 方法不存在`);
            });
        }

        function inspectLevelSelect() {
            document.getElementById('results').innerHTML = '';
            
            if (!window.levelSelect) {
                addResult('检查失败', false, '❌ levelSelect 对象不存在');
                return;
            }

            // 检查对象属性
            const properties = Object.getOwnPropertyNames(window.levelSelect);
            addCodeBlock('对象属性', properties.join('\n'));

            // 检查原型链
            const proto = Object.getPrototypeOf(window.levelSelect);
            const protoMethods = Object.getOwnPropertyNames(proto);
            addCodeBlock('原型方法', protoMethods.join('\n'));

            // 尝试直接访问方法
            try {
                const method = window.levelSelect.hideWithoutReturnToMenu;
                addResult('直接访问 hideWithoutReturnToMenu', true, 
                    `方法类型: ${typeof method}, 方法存在: ${method !== undefined}`);
                
                if (method) {
                    addCodeBlock('方法源码（前100字符）', method.toString().substring(0, 100) + '...');
                }
            } catch (error) {
                addResult('直接访问失败', false, `错误: ${error.message}`);
            }
        }

        function testMethodCall() {
            document.getElementById('results').innerHTML = '';
            
            if (!window.levelSelect) {
                addResult('测试失败', false, '❌ levelSelect 对象不存在');
                return;
            }

            // 创建必要的DOM元素
            if (!document.getElementById('levelSelectScreen')) {
                const div = document.createElement('div');
                div.id = 'levelSelectScreen';
                div.style.display = 'none';
                document.body.appendChild(div);
                addResult('DOM元素创建', true, '✅ 创建了 levelSelectScreen 元素');
            }

            // 测试方法调用
            try {
                if (typeof window.levelSelect.hideWithoutReturnToMenu === 'function') {
                    // 先初始化
                    window.levelSelect.init();
                    
                    // 调用方法
                    window.levelSelect.hideWithoutReturnToMenu();
                    addResult('方法调用测试', true, '✅ hideWithoutReturnToMenu 方法调用成功');
                } else {
                    addResult('方法调用测试', false, '❌ hideWithoutReturnToMenu 方法不存在');
                }
            } catch (error) {
                addResult('方法调用测试', false, `❌ 调用失败: ${error.message}`);
            }
        }

        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(testMethods, 500);
        });
    </script>
</body>
</html>
