<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>详细调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: #fff;
        }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 500px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>详细调试</h1>
    <div id="console-output"></div>

    <script>
        // 重写 console 方法
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📋';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        // 捕获所有错误
        window.addEventListener('error', function(e) {
            console.error('JavaScript 错误:', e.message, '在', e.filename, '第', e.lineno, '行');
        });

        // 动态加载脚本并监控
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                console.log(`🔄 开始加载脚本: ${src}`);
                
                const script = document.createElement('script');
                script.src = src;
                
                script.onload = function() {
                    console.log(`✅ 脚本加载成功: ${src}`);
                    resolve();
                };
                
                script.onerror = function() {
                    console.error(`❌ 脚本加载失败: ${src}`);
                    reject(new Error(`Failed to load ${src}`));
                };
                
                document.head.appendChild(script);
            });
        }

        // 页面加载完成后开始测试
        window.addEventListener('load', async () => {
            console.log('🔍 开始详细调试...');
            
            try {
                // 加载 level-manager.js
                await loadScript('js/game/level-manager.js');
                
                // 等待一小段时间确保脚本执行完成
                await new Promise(resolve => setTimeout(resolve, 100));
                
                // 检查结果
                console.log('📋 检查加载结果...');
                console.log('📋 window.LevelManager:', typeof window.LevelManager);
                console.log('📋 window.levelManager:', typeof window.levelManager);
                
                if (window.LevelManager) {
                    console.log('📋 LevelManager 原型:', window.LevelManager.prototype);
                    console.log('📋 LevelManager.prototype.init:', typeof window.LevelManager.prototype.init);
                    
                    // 列出原型上的所有方法
                    console.log('📋 LevelManager 原型方法:');
                    const protoMethods = Object.getOwnPropertyNames(window.LevelManager.prototype);
                    protoMethods.forEach(method => {
                        if (method !== 'constructor') {
                            console.log(`  - ${method}: ${typeof window.LevelManager.prototype[method]}`);
                        }
                    });
                }
                
                if (window.levelManager) {
                    console.log('📋 levelManager 构造函数:', window.levelManager.constructor.name);
                    console.log('📋 levelManager 原型:', Object.getPrototypeOf(window.levelManager));
                    console.log('📋 levelManager.init:', typeof window.levelManager.init);
                    
                    // 检查实例上的方法
                    console.log('📋 levelManager 实例方法:');
                    for (let key in window.levelManager) {
                        if (typeof window.levelManager[key] === 'function') {
                            console.log(`  - ${key}: ${typeof window.levelManager[key]}`);
                        }
                    }
                    
                    // 检查原型链上的方法
                    const proto = Object.getPrototypeOf(window.levelManager);
                    console.log('📋 原型链上的方法:');
                    const methods = Object.getOwnPropertyNames(proto);
                    methods.forEach(method => {
                        if (method !== 'constructor' && typeof proto[method] === 'function') {
                            console.log(`  - ${method}: ${typeof proto[method]}`);
                            console.log(`    实例可访问: ${typeof window.levelManager[method]}`);
                        }
                    });
                    
                    // 特别检查 init 方法
                    console.log('📋 特别检查 init 方法:');
                    console.log(`  - 原型上的 init: ${typeof proto.init}`);
                    console.log(`  - 实例上的 init: ${typeof window.levelManager.init}`);
                    console.log(`  - hasOwnProperty('init'): ${window.levelManager.hasOwnProperty('init')}`);
                    console.log(`  - 'init' in levelManager: ${'init' in window.levelManager}`);
                    
                    // 如果 init 方法存在，尝试调用
                    if (typeof window.levelManager.init === 'function') {
                        console.log('✅ init 方法存在，尝试调用...');
                        try {
                            const result = window.levelManager.init();
                            console.log('✅ init 方法调用成功，返回值:', result);
                        } catch (error) {
                            console.error('❌ init 方法调用失败:', error);
                        }
                    } else {
                        console.error('❌ init 方法不存在');
                        
                        // 尝试从原型直接调用
                        if (proto.init) {
                            console.log('📋 尝试从原型直接调用...');
                            try {
                                const result = proto.init.call(window.levelManager);
                                console.log('✅ 原型方法调用成功，返回值:', result);
                            } catch (error) {
                                console.error('❌ 原型方法调用失败:', error);
                            }
                        }
                    }
                } else {
                    console.error('❌ levelManager 实例不存在');
                }
                
            } catch (error) {
                console.error('❌ 测试过程中出现错误:', error);
            }
            
            console.log('🔍 详细调试完成');
        });
    </script>
</body>
</html>
