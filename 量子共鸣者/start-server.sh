#!/bin/bash

# 量子共鸣者游戏启动脚本
# 用于启动本地开发服务器

echo "🎮 量子共鸣者游戏服务器启动脚本"
echo "=================================="

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到 Python3，请先安装 Python3"
    exit 1
fi

# 进入游戏目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "📁 当前目录: $SCRIPT_DIR"

# 检查必要文件
if [ ! -f "index.html" ]; then
    echo "❌ 错误: 找不到 index.html 文件"
    exit 1
fi

if [ ! -f "manifest.json" ]; then
    echo "❌ 错误: 找不到 manifest.json 文件"
    exit 1
fi

echo "✅ 文件检查完成"

# 启动服务器
echo "🚀 启动服务器..."
echo "📝 提示: 按 Ctrl+C 停止服务器"
echo ""

python3 server.py --port 8080
