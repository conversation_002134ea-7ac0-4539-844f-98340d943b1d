<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量子共鸣者 - 3D渲染测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            overflow: hidden;
        }

        .container {
            width: 100vw;
            height: 100vh;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .header {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #00ffff;
        }

        .header h1 {
            color: #00ffff;
            font-size: 24px;
            margin-bottom: 10px;
        }

        .controls {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #00ffff;
            min-width: 250px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            color: #00ffff;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .control-group input, .control-group select, .control-group button {
            width: 100%;
            padding: 8px;
            border: 1px solid #00ffff;
            background: rgba(0, 0, 0, 0.5);
            color: #ffffff;
            border-radius: 5px;
            font-size: 14px;
        }

        .control-group button {
            background: linear-gradient(45deg, #00ffff, #0080ff);
            color: #000;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-group button:hover {
            background: linear-gradient(45deg, #0080ff, #00ffff);
            transform: translateY(-2px);
        }

        .stats {
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #00ffff;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .stats-item {
            margin-bottom: 5px;
            color: #00ffff;
        }

        #gameCanvas {
            width: 100%;
            height: 100%;
            display: block;
            cursor: grab;
        }

        #gameCanvas:active {
            cursor: grabbing;
        }

        .instructions {
            position: absolute;
            bottom: 20px;
            right: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #00ffff;
            max-width: 300px;
        }

        .instructions h3 {
            color: #00ffff;
            margin-bottom: 10px;
        }

        .instructions ul {
            list-style: none;
            padding: 0;
        }

        .instructions li {
            margin-bottom: 5px;
            font-size: 12px;
            color: #cccccc;
        }

        .instructions li::before {
            content: "• ";
            color: #00ffff;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 200;
            text-align: center;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(0, 255, 255, 0.3);
            border-top: 3px solid #00ffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 200;
            background: rgba(255, 0, 0, 0.1);
            border: 2px solid #ff0000;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            max-width: 400px;
        }

        .error h2 {
            color: #ff0000;
            margin-bottom: 10px;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 加载界面 -->
        <div id="loading" class="loading">
            <div class="loading-spinner"></div>
            <h2>正在初始化3D渲染系统...</h2>
            <p>请稍候</p>
        </div>

        <!-- 错误界面 -->
        <div id="error" class="error hidden">
            <h2>初始化失败</h2>
            <p id="errorMessage">未知错误</p>
            <button onclick="location.reload()">重新加载</button>
        </div>

        <!-- 标题 -->
        <div class="header">
            <h1>🌌 量子共鸣者 3D渲染测试</h1>
            <p>WebGL 3D粒子系统演示</p>
        </div>

        <!-- 控制面板 -->
        <div class="controls">
            <div class="control-group">
                <label>渲染模式</label>
                <select id="renderMode">
                    <option value="3d">3D模式</option>
                    <option value="2d">2D模式</option>
                </select>
            </div>

            <div class="control-group">
                <label>粒子数量: <span id="particleCountValue">20</span></label>
                <input type="range" id="particleCount" min="10" max="100" value="20">
            </div>

            <div class="control-group">
                <label>自动旋转</label>
                <input type="checkbox" id="autoRotate" checked>
            </div>

            <div class="control-group">
                <label>显示连接</label>
                <input type="checkbox" id="showConnections" checked>
            </div>

            <div class="control-group">
                <button id="addParticle">添加粒子</button>
            </div>

            <div class="control-group">
                <button id="clearParticles">清空粒子</button>
            </div>

            <div class="control-group">
                <button id="resetCamera">重置相机</button>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="stats">
            <div class="stats-item">FPS: <span id="fps">0</span></div>
            <div class="stats-item">渲染时间: <span id="renderTime">0</span>ms</div>
            <div class="stats-item">粒子数: <span id="activeParticles">0</span></div>
            <div class="stats-item">连接数: <span id="activeConnections">0</span></div>
            <div class="stats-item">绘制调用: <span id="drawCalls">0</span></div>
            <div class="stats-item">顶点数: <span id="vertices">0</span></div>
        </div>

        <!-- 操作说明 -->
        <div class="instructions">
            <h3>操作说明</h3>
            <ul>
                <li>鼠标拖拽：旋转视角</li>
                <li>鼠标滚轮：缩放</li>
                <li>点击画布：添加粒子</li>
                <li>空格键：暂停/继续</li>
                <li>R键：重置相机</li>
                <li>F键：全屏切换</li>
            </ul>
        </div>

        <!-- 游戏画布 -->
        <canvas id="gameCanvas"></canvas>
    </div>

    <!-- JavaScript文件 -->
    <script src="js/utils/math-utils.js"></script>
    <script src="js/render/webgl-renderer.js"></script>
    <script src="js/render/particle-system-3d.js"></script>
    <script src="js/render/scene-manager-3d.js"></script>

    <script>
        // 3D渲染测试应用
        class Test3DApp {
            constructor() {
                this.sceneManager = null;
                this.isInitialized = false;
                this.lastTime = 0;
                this.animationId = null;
            }

            async init() {
                try {
                    console.log('🚀 开始初始化3D渲染测试...');

                    // 获取画布
                    const canvas = document.getElementById('gameCanvas');
                    if (!canvas) {
                        throw new Error('画布元素未找到');
                    }

                    // 初始化3D场景管理器
                    this.sceneManager = new SceneManager3D();
                    const success = await this.sceneManager.init(canvas);

                    if (!success) {
                        throw new Error('3D场景管理器初始化失败');
                    }

                    // 设置事件监听器
                    this.setupEventListeners();

                    // 隐藏加载界面
                    document.getElementById('loading').classList.add('hidden');

                    // 开始渲染循环
                    this.startRenderLoop();

                    this.isInitialized = true;
                    console.log('✅ 3D渲染测试初始化完成');

                } catch (error) {
                    console.error('❌ 初始化失败:', error);
                    this.showError(error.message);
                }
            }

            setupEventListeners() {
                // 渲染模式切换
                document.getElementById('renderMode').addEventListener('change', (e) => {
                    // 这里可以添加模式切换逻辑
                    console.log('渲染模式切换到:', e.target.value);
                });

                // 粒子数量控制
                const particleCountSlider = document.getElementById('particleCount');
                const particleCountValue = document.getElementById('particleCountValue');
                particleCountSlider.addEventListener('input', (e) => {
                    particleCountValue.textContent = e.target.value;
                    // 这里可以添加粒子数量调整逻辑
                });

                // 自动旋转控制
                document.getElementById('autoRotate').addEventListener('change', (e) => {
                    if (this.sceneManager) {
                        this.sceneManager.cameraController.autoRotate = e.target.checked;
                    }
                });

                // 添加粒子按钮
                document.getElementById('addParticle').addEventListener('click', () => {
                    if (this.sceneManager && this.sceneManager.particleSystem) {
                        const frequency = 200 + Math.random() * 1600;
                        this.sceneManager.particleSystem.addParticle({
                            position: [
                                (Math.random() - 0.5) * 6,
                                (Math.random() - 0.5) * 6,
                                (Math.random() - 0.5) * 6
                            ],
                            frequency: frequency,
                            energy: 0.8 + Math.random() * 0.2
                        });
                    }
                });

                // 清空粒子按钮
                document.getElementById('clearParticles').addEventListener('click', () => {
                    if (this.sceneManager && this.sceneManager.particleSystem) {
                        this.sceneManager.particleSystem.initializeParticleData();
                        this.sceneManager.particleSystem.createInitialParticles();
                    }
                });

                // 重置相机按钮
                document.getElementById('resetCamera').addEventListener('click', () => {
                    if (this.sceneManager) {
                        this.sceneManager.resetCamera();
                    }
                });
            }

            startRenderLoop() {
                const render = (currentTime) => {
                    const deltaTime = currentTime - this.lastTime;
                    this.lastTime = currentTime;

                    if (this.isInitialized && this.sceneManager) {
                        // 渲染场景
                        this.sceneManager.update(deltaTime);
                        this.sceneManager.render();

                        // 更新统计信息
                        this.updateStats();
                    }

                    this.animationId = requestAnimationFrame(render);
                };

                this.animationId = requestAnimationFrame(render);
            }

            updateStats() {
                if (!this.sceneManager) return;

                const stats = this.sceneManager.getStats();

                document.getElementById('fps').textContent = stats.fps;
                document.getElementById('renderTime').textContent = stats.frameTime.toFixed(2);

                if (stats.particles) {
                    document.getElementById('activeParticles').textContent = stats.particles.activeParticles;
                    document.getElementById('activeConnections').textContent = stats.particles.activeConnections;
                }

                if (stats.renderer) {
                    document.getElementById('drawCalls').textContent = stats.renderer.drawCalls;
                    document.getElementById('vertices').textContent = stats.renderer.vertices;
                }
            }

            showError(message) {
                document.getElementById('loading').classList.add('hidden');
                document.getElementById('errorMessage').textContent = message;
                document.getElementById('error').classList.remove('hidden');
            }

            destroy() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                    this.animationId = null;
                }

                if (this.sceneManager) {
                    this.sceneManager.destroy();
                    this.sceneManager = null;
                }

                this.isInitialized = false;
            }
        }

        // 启动应用
        const app = new Test3DApp();
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            app.init();
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            app.destroy();
        });
    </script>
</body>
</html>
