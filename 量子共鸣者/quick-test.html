<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速测试 - 量子共鸣者</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .test-btn {
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            font-size: 18px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .log {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .screen {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            z-index: 1000;
            padding: 20px;
            overflow-y: auto;
        }
        
        .screen.active {
            display: flex;
            flex-direction: column;
        }
    </style>
</head>
<body>
    <h1>🎯 关卡选择快速测试</h1>
    
    <button class="test-btn" onclick="simulateStartGame()">模拟点击开始游戏</button>
    <button class="test-btn" onclick="directShowLevelSelect()">直接显示关卡选择</button>
    <button class="test-btn" onclick="checkLevelSelectInstance()">检查实例状态</button>
    <button class="test-btn" onclick="clearLog()">清空日志</button>
    
    <div id="log" class="log">
        <div>📋 测试日志将在这里显示...</div>
    </div>
    
    <!-- 关卡选择屏幕 -->
    <div id="levelSelectScreen" class="screen level-select-screen">
        <!-- 关卡选择内容将由JavaScript动态生成 -->
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/ui/level-select.js"></script>
    <script src="js/ui/ui-manager.js"></script>
    
    <script>
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('log');
            const color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : '#74c0fc';
            logDiv.innerHTML += `<div style="color: ${color}; margin: 5px 0;">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '<div>📋 测试日志已清空...</div>';
        }
        
        function simulateStartGame() {
            log('🎮 模拟点击开始游戏按钮');
            
            try {
                // 模拟 UI Manager 的 showScreen 调用
                if (window.uiManager && window.uiManager.showScreen) {
                    log('✅ 找到 uiManager，调用 showScreen');
                    window.uiManager.showScreen('levelSelectScreen');
                } else {
                    log('⚠️ uiManager 不存在，直接调用关卡选择显示');
                    directShowLevelSelect();
                }
            } catch (error) {
                log(`❌ 模拟开始游戏失败: ${error.message}`, 'error');
            }
        }
        
        function directShowLevelSelect() {
            log('🎯 直接显示关卡选择界面');
            
            try {
                if (window.levelSelect) {
                    log('✅ 找到 levelSelect 实例');
                    
                    // 检查是否已初始化
                    if (!window.levelSelect.isInitialized) {
                        log('🔧 关卡选择未初始化，开始初始化...');
                        const initResult = window.levelSelect.init();
                        if (initResult) {
                            log('✅ 关卡选择初始化成功', 'success');
                        } else {
                            log('❌ 关卡选择初始化失败', 'error');
                            return;
                        }
                    }
                    
                    // 显示关卡选择界面
                    window.levelSelect.show();
                    log('✅ 关卡选择界面显示调用完成', 'success');
                    
                    // 检查DOM状态
                    setTimeout(() => {
                        const container = document.getElementById('levelSelectScreen');
                        if (container && container.style.display === 'flex') {
                            log('✅ 关卡选择界面已正确显示', 'success');
                            container.classList.add('active');
                            
                            // 检查内容
                            const levelGrid = document.getElementById('levelGrid');
                            if (levelGrid && levelGrid.children.length > 0) {
                                log(`✅ 关卡网格已生成，包含 ${levelGrid.children.length} 个关卡`, 'success');
                            } else {
                                log('⚠️ 关卡网格为空或未找到');
                            }
                        } else {
                            log('❌ 关卡选择界面显示失败', 'error');
                        }
                    }, 100);
                    
                } else {
                    log('❌ levelSelect 实例不存在', 'error');
                }
            } catch (error) {
                log(`❌ 显示关卡选择失败: ${error.message}`, 'error');
            }
        }
        
        function checkLevelSelectInstance() {
            log('🔍 检查关卡选择实例状态');
            
            if (window.levelSelect) {
                log('✅ levelSelect 实例存在');
                log(`📊 已初始化: ${window.levelSelect.isInitialized}`);
                log(`👁️ 可见状态: ${window.levelSelect.isVisible}`);
                log(`📦 关卡数量: ${window.levelSelect.levels ? window.levelSelect.levels.length : '未知'}`);
                log(`🎯 选中关卡: ${window.levelSelect.selectedLevel ? window.levelSelect.selectedLevel.name : '无'}`);
                
                // 检查DOM元素
                const container = document.getElementById('levelSelectScreen');
                log(`🏠 DOM容器: ${container ? '存在' : '不存在'}`);
                if (container) {
                    log(`📺 显示状态: ${container.style.display || 'default'}`);
                    log(`👶 子元素数量: ${container.children.length}`);
                }
                
                // 检查关卡数据
                if (window.levelSelect.levels) {
                    window.levelSelect.levels.forEach((level, index) => {
                        log(`📋 关卡 ${index + 1}: ${level.name} (${level.unlocked ? '已解锁' : '已锁定'})`);
                    });
                }
                
            } else {
                log('❌ levelSelect 实例不存在', 'error');
                log('🔧 尝试创建新实例...');
                try {
                    window.levelSelect = new LevelSelect();
                    log('✅ 新实例创建成功', 'success');
                } catch (error) {
                    log(`❌ 创建实例失败: ${error.message}`, 'error');
                }
            }
        }
        
        // 页面加载完成后检查
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 页面加载完成');
            
            // 检查必要的类和实例
            if (typeof LevelSelect !== 'undefined') {
                log('✅ LevelSelect 类已加载');
            } else {
                log('❌ LevelSelect 类未找到', 'error');
            }
            
            if (window.levelSelect) {
                log('✅ levelSelect 实例已存在');
            } else {
                log('⚠️ levelSelect 实例不存在，这是正常的');
            }
            
            setTimeout(checkLevelSelectInstance, 500);
        });
    </script>
</body>
</html>
