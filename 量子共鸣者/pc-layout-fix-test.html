<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PC端布局修复测试 - 量子共鸣者</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/ui-components.css">
    <link rel="stylesheet" href="styles/game.css">
    <link rel="stylesheet" href="styles/responsive.css">
    
    <style>
        /* 测试页面特定样式 */
        body {
            margin: 0;
            padding: 0;
            background: var(--background-primary);
            color: var(--text-primary);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        .test-header {
            background: var(--ui-surface);
            padding: 20px;
            border-bottom: 2px solid var(--ui-border);
            text-align: center;
        }
        
        .test-header h1 {
            color: var(--ui-primary);
            margin: 0 0 10px 0;
            font-size: 28px;
        }
        
        .test-info {
            color: var(--text-secondary);
            font-size: 16px;
        }
        
        .test-controls {
            background: var(--ui-surface);
            padding: 15px;
            border-bottom: 1px solid var(--ui-border);
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .test-button {
            background: var(--ui-primary);
            color: var(--background-primary);
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: var(--ui-primary-hover);
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }
        
        .test-button:disabled {
            background: var(--ui-disabled);
            cursor: not-allowed;
            opacity: 0.6;
        }
        
        .console-output {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 200px;
            background: rgba(0, 0, 0, 0.9);
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            padding: 10px;
            overflow-y: auto;
            border-top: 2px solid var(--ui-border);
            z-index: 1000;
        }
        
        .console-line {
            margin-bottom: 2px;
            white-space: pre-wrap;
        }
        
        .console-error {
            color: #ff4444;
        }
        
        .console-success {
            color: #44ff44;
        }
        
        .console-warning {
            color: #ffaa44;
        }
        
        .console-info {
            color: #4444ff;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>🖥️ PC端布局修复测试</h1>
        <div class="test-info">
            测试关卡选择界面在PC端的布局和显示效果
        </div>
    </div>
    
    <div class="test-controls">
        <button class="test-button" onclick="runPCLayoutTest()">🔧 运行PC端布局验证</button>
        <button class="test-button" onclick="runFixVerificationTest()">🎯 运行修复验证测试</button>
        <button class="test-button" onclick="showLevelSelect()">📱 显示关卡选择界面</button>
        <button class="test-button" onclick="hideLevelSelect()">❌ 隐藏关卡选择界面</button>
        <button class="test-button" onclick="clearConsole()">🧹 清空控制台</button>
    </div>
    
    <!-- 关卡选择屏幕 -->
    <div id="levelSelectScreen" class="screen level-select-screen">
        <!-- 关卡选择内容将由JavaScript动态生成 -->
    </div>
    
    <!-- 控制台输出 -->
    <div class="console-output" id="consoleOutput">
        <div class="console-line console-info">🖥️ PC端布局修复测试控制台已就绪</div>
        <div class="console-line console-info">💡 点击上方按钮开始测试</div>
    </div>
    
    <!-- 核心脚本 -->
    <script src="js/core/game-core.js"></script>
    <script src="js/core/level-manager.js"></script>
    <script src="js/core/audio-manager.js"></script>
    <script src="js/core/settings-manager.js"></script>
    
    <!-- UI组件 -->
    <script src="js/ui/ui-manager.js"></script>
    <script src="js/ui/level-select.js"></script>
    <script src="js/ui/level-editor.js"></script>
    
    <!-- 验证脚本 -->
    <script src="pc-layout-verification.js"></script>
    <script src="fix-verification.js"></script>
    
    <script>
        // 控制台输出管理
        const consoleOutput = document.getElementById('consoleOutput');
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        function addConsoleMessage(message, type = 'info') {
            const line = document.createElement('div');
            line.className = `console-line console-${type}`;
            line.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            consoleOutput.appendChild(line);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        // 重写console方法以显示在页面上
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addConsoleMessage(args.join(' '), 'info');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addConsoleMessage(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addConsoleMessage(args.join(' '), 'warning');
        };
        
        // 测试函数
        function runPCLayoutTest() {
            addConsoleMessage('🔧 开始运行PC端布局验证测试...', 'info');
            if (typeof verifyPCLayout === 'function') {
                verifyPCLayout();
            } else {
                addConsoleMessage('❌ PC端布局验证函数未找到', 'error');
            }
        }
        
        function runFixVerificationTest() {
            addConsoleMessage('🎯 开始运行修复验证测试...', 'info');
            if (typeof testLevelSelect === 'function') {
                testLevelSelect();
            } else {
                addConsoleMessage('❌ 修复验证函数未找到', 'error');
            }
        }
        
        function showLevelSelect() {
            addConsoleMessage('📱 显示关卡选择界面...', 'info');
            if (window.levelSelect) {
                window.levelSelect.show();
                addConsoleMessage('✅ 关卡选择界面已显示', 'success');
            } else {
                addConsoleMessage('❌ 关卡选择实例不存在', 'error');
            }
        }
        
        function hideLevelSelect() {
            addConsoleMessage('❌ 隐藏关卡选择界面...', 'info');
            if (window.levelSelect) {
                window.levelSelect.hide();
                addConsoleMessage('✅ 关卡选择界面已隐藏', 'success');
            } else {
                addConsoleMessage('❌ 关卡选择实例不存在', 'error');
            }
        }
        
        function clearConsole() {
            consoleOutput.innerHTML = '<div class="console-line console-info">🧹 控制台已清空</div>';
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addConsoleMessage('🚀 页面加载完成，开始初始化...', 'info');
            
            // 等待一段时间确保所有脚本加载完成
            setTimeout(() => {
                if (window.levelSelect) {
                    addConsoleMessage('✅ 关卡选择实例已创建', 'success');
                } else {
                    addConsoleMessage('❌ 关卡选择实例未创建', 'error');
                }
                
                addConsoleMessage('💡 可以开始测试了！', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
