<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频系统修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: #fff;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: #16213e;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #0f3460;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #2d5a27; border: 1px solid #4caf50; }
        .error { background: #5a2727; border: 1px solid #f44336; }
        .warning { background: #5a5527; border: 1px solid #ff9800; }
        .info { background: #27455a; border: 1px solid #2196f3; }
        button {
            background: #0f3460;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #1e5f8b;
        }
        #log {
            background: #000;
            color: #0f0;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            border: 1px solid #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 音频系统修复测试</h1>
        
        <div class="test-section">
            <h2>系统状态检查</h2>
            <div id="system-status"></div>
            <button onclick="checkSystems()">检查系统状态</button>
        </div>
        
        <div class="test-section">
            <h2>音频引擎测试</h2>
            <div id="audio-status"></div>
            <button onclick="testAudioEngine()">测试音频引擎</button>
            <button onclick="testAudioSequencer()">测试音频序列器</button>
            <button onclick="testAudioSynthesizer()">测试音频合成器</button>
        </div>
        
        <div class="test-section">
            <h2>控制台日志</h2>
            <div id="log"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <!-- 核心脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/utils/math-utils.js"></script>
    <script src="js/core/audio-engine.js"></script>
    <script src="js/audio/synthesizer.js"></script>
    <script src="js/audio/sequencer.js"></script>
    <script src="js/audio/effects.js"></script>
    <script src="js/audio/visualizer.js"></script>
    <script src="js/audio/audio-manager.js"></script>

    <script>
        // 重写console.log来显示在页面上
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToLog(message, type = 'info') {
            const log = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#f44336' : type === 'warn' ? '#ff9800' : '#0f0';
            log.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'info');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog(args.join(' '), 'warn');
        };

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function checkSystems() {
            console.log('🔍 开始检查系统状态...');
            
            // 检查存储服务
            if (window.storageService) {
                await window.storageService.waitForInit();
                updateStatus('system-status', '✅ 存储服务已初始化', 'success');
                console.log('✅ 存储服务状态正常');
            } else {
                updateStatus('system-status', '❌ 存储服务未找到', 'error');
                console.error('❌ 存储服务未找到');
            }
            
            // 检查音频引擎
            if (window.audioEngine) {
                console.log('✅ 音频引擎实例存在');
            } else {
                console.error('❌ 音频引擎实例不存在');
            }
            
            // 检查其他类
            const classes = ['AudioSynthesizer', 'AudioSequencer', 'AudioEffect', 'AudioManager'];
            classes.forEach(className => {
                if (window[className]) {
                    console.log(`✅ ${className} 类已加载`);
                } else {
                    console.error(`❌ ${className} 类未找到`);
                }
            });
        }

        async function testAudioEngine() {
            console.log('🎵 测试音频引擎初始化...');
            
            try {
                if (!window.audioEngine) {
                    throw new Error('音频引擎实例不存在');
                }
                
                const result = await window.audioEngine.init();
                if (result) {
                    updateStatus('audio-status', '✅ 音频引擎初始化成功', 'success');
                    console.log('✅ 音频引擎初始化成功');
                } else {
                    updateStatus('audio-status', '❌ 音频引擎初始化失败', 'error');
                    console.error('❌ 音频引擎初始化失败');
                }
            } catch (error) {
                updateStatus('audio-status', `❌ 音频引擎测试失败: ${error.message}`, 'error');
                console.error('❌ 音频引擎测试失败:', error);
            }
        }

        async function testAudioSequencer() {
            console.log('🎼 测试音频序列器...');
            
            try {
                if (!window.AudioSequencer) {
                    throw new Error('AudioSequencer 类不存在');
                }
                
                if (!window.audioEngine || !window.audioEngine.isInitialized) {
                    throw new Error('音频引擎未初始化');
                }
                
                const sequencer = new AudioSequencer(window.audioEngine);
                sequencer.setPattern('quantum');
                console.log('✅ 音频序列器创建成功');
                
                // 测试播放
                sequencer.start();
                setTimeout(() => {
                    sequencer.stop();
                    console.log('✅ 音频序列器播放测试完成');
                }, 2000);
                
            } catch (error) {
                console.error('❌ 音频序列器测试失败:', error);
            }
        }

        async function testAudioSynthesizer() {
            console.log('🎹 测试音频合成器...');
            
            try {
                if (!window.AudioSynthesizer) {
                    throw new Error('AudioSynthesizer 类不存在');
                }
                
                if (!window.audioEngine || !window.audioEngine.isInitialized) {
                    throw new Error('音频引擎未初始化');
                }
                
                const synthesizer = new AudioSynthesizer(window.audioEngine);
                
                // 测试播放音符
                synthesizer.playNote(440, 0.5); // A4
                console.log('✅ 音频合成器播放测试完成');
                
            } catch (error) {
                console.error('❌ 音频合成器测试失败:', error);
            }
        }

        // 页面加载完成后自动检查系统
        window.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 音频系统修复测试页面已加载');
            setTimeout(checkSystems, 1000);
        });
    </script>
</body>
</html>
