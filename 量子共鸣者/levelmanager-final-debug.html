<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LevelManager 最终调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: #fff;
        }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 600px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .test-button {
            background: #e94560;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>LevelManager 最终调试</h1>
    <button class="test-button" onclick="testStep1()">步骤1: 只加载 level-manager.js</button>
    <button class="test-button" onclick="testStep2()">步骤2: 加载 level.js</button>
    <button class="test-button" onclick="clearConsole()">清空控制台</button>
    <div id="console-output"></div>

    <script>
        // 重写 console 方法
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📋';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        function clearConsole() {
            consoleOutput.textContent = '';
        }

        function testStep1() {
            console.log('🔍 步骤1: 只加载 level-manager.js');
            
            // 清除现有的 levelManager
            if (window.levelManager) {
                console.log('📋 清除现有的 levelManager');
                delete window.levelManager;
            }
            
            if (window.LevelManager) {
                console.log('📋 清除现有的 LevelManager 类');
                delete window.LevelManager;
            }
            
            // 动态加载 level-manager.js
            const script = document.createElement('script');
            script.src = 'js/game/level-manager.js';
            script.onload = function() {
                console.log('✅ level-manager.js 加载完成');
                
                // 检查结果
                console.log('📋 检查 LevelManager 类:', typeof window.LevelManager);
                console.log('📋 检查 levelManager 实例:', typeof window.levelManager);
                
                if (window.levelManager) {
                    console.log('📋 levelManager 构造函数:', window.levelManager.constructor.name);
                    console.log('📋 levelManager.init 方法:', typeof window.levelManager.init);
                    
                    if (typeof window.levelManager.init === 'function') {
                        console.log('✅ init 方法存在，尝试调用...');
                        try {
                            const result = window.levelManager.init();
                            console.log('✅ init 方法调用成功，返回值:', result);
                        } catch (error) {
                            console.error('❌ init 方法调用失败:', error);
                        }
                    } else {
                        console.error('❌ init 方法不存在');
                    }
                }
            };
            script.onerror = function() {
                console.error('❌ level-manager.js 加载失败');
            };
            document.head.appendChild(script);
        }

        function testStep2() {
            console.log('🔍 步骤2: 加载 level.js (这会覆盖 levelManager)');
            
            // 动态加载 level.js
            const script = document.createElement('script');
            script.src = 'js/game/level.js';
            script.onload = function() {
                console.log('✅ level.js 加载完成');
                
                // 检查结果
                console.log('📋 检查 LevelRegistry 类:', typeof window.LevelRegistry);
                console.log('📋 检查 levelRegistry 实例:', typeof window.levelRegistry);
                console.log('📋 检查 levelManager 实例:', typeof window.levelManager);
                
                if (window.levelManager) {
                    console.log('📋 levelManager 构造函数:', window.levelManager.constructor.name);
                    console.log('📋 levelManager.init 方法:', typeof window.levelManager.init);
                    
                    // 列出所有方法
                    console.log('📋 levelManager 的所有方法:');
                    const proto = Object.getPrototypeOf(window.levelManager);
                    const methods = Object.getOwnPropertyNames(proto).filter(name => 
                        typeof window.levelManager[name] === 'function' && name !== 'constructor'
                    );
                    methods.forEach(method => {
                        console.log(`  - ${method}(): ${typeof window.levelManager[method]}`);
                    });
                } else {
                    console.error('❌ levelManager 实例不存在');
                }
            };
            script.onerror = function() {
                console.error('❌ level.js 加载失败');
            };
            document.head.appendChild(script);
        }

        // 页面加载完成后显示初始状态
        window.addEventListener('load', () => {
            console.log('🔍 页面加载完成，检查初始状态...');
            console.log('📋 window.LevelManager:', typeof window.LevelManager);
            console.log('📋 window.levelManager:', typeof window.levelManager);
            console.log('📋 window.LevelRegistry:', typeof window.LevelRegistry);
            console.log('📋 window.levelRegistry:', typeof window.levelRegistry);
        });
    </script>
</body>
</html>
