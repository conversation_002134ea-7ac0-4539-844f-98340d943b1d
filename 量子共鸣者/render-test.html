<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渲染引擎测试 - 量子共鸣者</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0f0f1e;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .canvas-container {
            width: 100%;
            height: 400px;
            border: 2px solid #333;
            margin: 20px 0;
            position: relative;
            background: #1a1a2e;
        }
        
        #game-canvas {
            width: 100%;
            height: 100%;
            display: block;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.success {
            background: #2d5a2d;
            color: #90ee90;
        }
        
        .status.error {
            background: #5a2d2d;
            color: #ff6b6b;
        }
        
        .status.info {
            background: #2d4a5a;
            color: #87ceeb;
        }
        
        .test-button {
            background: #4a90e2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #357abd;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 渲染引擎测试</h1>
        
        <div id="status-container">
            <div class="status info">正在初始化渲染引擎...</div>
        </div>
        
        <div class="canvas-container">
            <canvas id="game-canvas"></canvas>
        </div>
        
        <div class="controls">
            <button class="test-button" onclick="testBasicRendering()">测试基础渲染</button>
            <button class="test-button" onclick="testParticles()">测试粒子效果</button>
            <button class="test-button" onclick="testResize()">测试画布调整</button>
            <button class="test-button" onclick="clearCanvas()">清空画布</button>
        </div>
        
        <div id="debug-info">
            <h3>调试信息</h3>
            <pre id="debug-output"></pre>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/core/render-engine.js"></script>
    
    <script>
        let renderEngine = null;
        let debugOutput = document.getElementById('debug-output');
        let statusContainer = document.getElementById('status-container');
        
        // 添加状态消息
        function addStatus(message, type = 'info') {
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            statusContainer.appendChild(statusDiv);
            
            // 添加到调试输出
            debugOutput.textContent += `[${new Date().toLocaleTimeString()}] ${message}\n`;
            debugOutput.scrollTop = debugOutput.scrollHeight;
        }
        
        // 初始化渲染引擎
        async function initRenderEngine() {
            try {
                addStatus('🎨 创建渲染引擎实例...', 'info');
                renderEngine = new RenderEngine();
                
                addStatus('🎯 获取画布元素...', 'info');
                const canvas = document.getElementById('game-canvas');
                if (!canvas) {
                    throw new Error('未找到画布元素');
                }
                
                addStatus('⚙️ 初始化渲染引擎...', 'info');
                const success = await renderEngine.init(canvas);
                
                if (success !== false) {
                    addStatus('✅ 渲染引擎初始化成功！', 'success');
                    addStatus(`📊 画布尺寸: ${renderEngine.width}x${renderEngine.height}`, 'info');
                    addStatus(`🖥️ 设备像素比: ${renderEngine.pixelRatio}`, 'info');
                    addStatus(`🎮 渲染模式: ${renderEngine.renderMode}`, 'info');
                    addStatus(`🌐 WebGL支持: ${renderEngine.webglSupported ? '是' : '否'}`, 'info');
                } else {
                    throw new Error('渲染引擎初始化失败');
                }
                
            } catch (error) {
                addStatus(`❌ 渲染引擎初始化失败: ${error.message}`, 'error');
                console.error('渲染引擎初始化错误:', error);
            }
        }
        
        // 测试基础渲染
        function testBasicRendering() {
            if (!renderEngine) {
                addStatus('❌ 渲染引擎未初始化', 'error');
                return;
            }
            
            try {
                addStatus('🎨 开始基础渲染测试...', 'info');
                
                // 清空画布
                renderEngine.clear();
                
                // 绘制一些基本图形
                const ctx = renderEngine.ctx;
                
                // 绘制背景渐变
                const gradient = ctx.createLinearGradient(0, 0, renderEngine.width, renderEngine.height);
                gradient.addColorStop(0, '#1a1a2e');
                gradient.addColorStop(1, '#16213e');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, renderEngine.width, renderEngine.height);
                
                // 绘制一些测试图形
                ctx.fillStyle = '#4a90e2';
                ctx.fillRect(50, 50, 100, 100);
                
                ctx.fillStyle = '#e74c3c';
                ctx.beginPath();
                ctx.arc(250, 100, 50, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.strokeStyle = '#2ecc71';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(350, 50);
                ctx.lineTo(450, 150);
                ctx.lineTo(350, 150);
                ctx.closePath();
                ctx.stroke();
                
                addStatus('✅ 基础渲染测试完成', 'success');
                
            } catch (error) {
                addStatus(`❌ 基础渲染测试失败: ${error.message}`, 'error');
                console.error('基础渲染测试错误:', error);
            }
        }
        
        // 测试粒子效果
        function testParticles() {
            if (!renderEngine) {
                addStatus('❌ 渲染引擎未初始化', 'error');
                return;
            }
            
            try {
                addStatus('✨ 开始粒子效果测试...', 'info');
                
                // 清空画布
                renderEngine.clear();
                
                const ctx = renderEngine.ctx;
                
                // 创建一些随机粒子
                for (let i = 0; i < 50; i++) {
                    const x = Math.random() * renderEngine.width;
                    const y = Math.random() * renderEngine.height;
                    const size = Math.random() * 5 + 2;
                    const hue = Math.random() * 360;
                    
                    ctx.fillStyle = `hsl(${hue}, 70%, 60%)`;
                    ctx.beginPath();
                    ctx.arc(x, y, size, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // 添加发光效果
                    ctx.shadowColor = `hsl(${hue}, 70%, 60%)`;
                    ctx.shadowBlur = 10;
                    ctx.fill();
                    ctx.shadowBlur = 0;
                }
                
                addStatus('✅ 粒子效果测试完成', 'success');
                
            } catch (error) {
                addStatus(`❌ 粒子效果测试失败: ${error.message}`, 'error');
                console.error('粒子效果测试错误:', error);
            }
        }
        
        // 测试画布调整
        function testResize() {
            if (!renderEngine) {
                addStatus('❌ 渲染引擎未初始化', 'error');
                return;
            }
            
            try {
                addStatus('📏 开始画布调整测试...', 'info');
                
                renderEngine.resize();
                
                addStatus(`✅ 画布调整完成，新尺寸: ${renderEngine.width}x${renderEngine.height}`, 'success');
                
            } catch (error) {
                addStatus(`❌ 画布调整测试失败: ${error.message}`, 'error');
                console.error('画布调整测试错误:', error);
            }
        }
        
        // 清空画布
        function clearCanvas() {
            if (!renderEngine) {
                addStatus('❌ 渲染引擎未初始化', 'error');
                return;
            }
            
            try {
                renderEngine.clear();
                addStatus('🧹 画布已清空', 'info');
            } catch (error) {
                addStatus(`❌ 清空画布失败: ${error.message}`, 'error');
                console.error('清空画布错误:', error);
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addStatus('📄 页面加载完成，开始初始化...', 'info');
            initRenderEngine();
        });
        
        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            if (renderEngine) {
                testResize();
            }
        });
    </script>
</body>
</html>
