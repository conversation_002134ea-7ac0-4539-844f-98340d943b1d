<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复验证测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #0f0f23;
            color: #ffffff;
            padding: 20px;
            margin: 0;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .test-section {
            background: #1a1a2e;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #4CAF50;
        }
        
        .test-section.error {
            border-left-color: #f44336;
        }
        
        .test-section.warning {
            border-left-color: #ff9800;
        }
        
        .test-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
        }
        
        .status-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }
        
        .status-success {
            background: #4CAF50;
            color: white;
        }
        
        .status-error {
            background: #f44336;
            color: white;
        }
        
        .status-loading {
            background: #ff9800;
            color: white;
        }
        
        .test-btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        
        .test-btn:hover {
            background: #1976D2;
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        
        .icon-item {
            text-align: center;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
        }
        
        .icon-item img {
            width: 48px;
            height: 48px;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .icon-item .icon-name {
            font-size: 12px;
            margin-top: 5px;
            opacity: 0.8;
        }
        
        .log-output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Split-Second Spark 修复验证</h1>
        <p>这个页面用于验证所有修复是否正常工作</p>
        
        <!-- 图标加载测试 -->
        <div class="test-section">
            <h2>📱 图标文件加载测试</h2>
            <p>检查所有PWA图标文件是否正确加载</p>
            
            <button class="test-btn" onclick="testIcons()">开始测试图标</button>
            
            <div id="icon-test-results"></div>
            
            <div class="icon-grid" id="icon-grid">
                <!-- 图标将动态加载到这里 -->
            </div>
        </div>
        
        <!-- 游戏ID测试 -->
        <div class="test-section">
            <h2>🎮 游戏ID匹配测试</h2>
            <p>验证HTML中的data-game属性与JavaScript配置是否匹配</p>
            
            <button class="test-btn" onclick="testGameIds()">测试游戏ID</button>
            
            <div id="game-id-results"></div>
        </div>
        
        <!-- 游戏启动测试 -->
        <div class="test-section">
            <h2>🚀 游戏启动功能测试</h2>
            <p>测试游戏启动逻辑是否正常工作</p>
            
            <button class="test-btn" onclick="testGameLaunch()">测试游戏启动</button>
            
            <div id="game-launch-results"></div>
        </div>
        
        <!-- PWA功能测试 -->
        <div class="test-section">
            <h2>📲 PWA功能测试</h2>
            <p>检查PWA相关功能是否正常</p>
            
            <button class="test-btn" onclick="testPWA()">测试PWA功能</button>
            
            <div id="pwa-results"></div>
        </div>
        
        <!-- 控制台日志 -->
        <div class="test-section">
            <h2>📋 测试日志</h2>
            <div class="log-output" id="test-log"></div>
            <button class="test-btn" onclick="clearLog()">清空日志</button>
        </div>
        
        <!-- 快速链接 -->
        <div class="test-section">
            <h2>🔗 快速链接</h2>
            <button class="test-btn" onclick="window.open('/', '_blank')">打开主界面</button>
            <button class="test-btn" onclick="window.open('/manifest.json', '_blank')">查看Manifest</button>
            <button class="test-btn" onclick="window.open('/create-basic-icons.html', '_blank')">图标生成器</button>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const typeIcon = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logElement.innerHTML += `[${timestamp}] ${typeIcon} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[Test] ${message}`);
        }
        
        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }
        
        // 图标测试
        async function testIcons() {
            log('开始测试图标文件加载...');
            const resultsDiv = document.getElementById('icon-test-results');
            const iconGrid = document.getElementById('icon-grid');
            
            const icons = [
                'favicon-16x16', 'favicon-32x32', 'icon-72x72', 'icon-96x96',
                'icon-128x128', 'icon-144x144', 'icon-152x152', 'icon-192x192',
                'icon-384x384', 'icon-512x512', 'temporal-icon-96x96',
                'spark-icon-96x96', 'quantum-icon-96x96'
            ];
            
            let successCount = 0;
            let errorCount = 0;
            iconGrid.innerHTML = '';
            
            for (const iconName of icons) {
                const iconPath = `/assets/images/${iconName}.png`;
                
                try {
                    const response = await fetch(iconPath);
                    if (response.ok) {
                        successCount++;
                        log(`✅ ${iconName}.png 加载成功`, 'success');
                        
                        // 添加到图标网格
                        const iconItem = document.createElement('div');
                        iconItem.className = 'icon-item';
                        iconItem.innerHTML = `
                            <img src="${iconPath}" alt="${iconName}" onerror="this.style.display='none'">
                            <div class="icon-name">${iconName}</div>
                        `;
                        iconGrid.appendChild(iconItem);
                    } else {
                        errorCount++;
                        log(`❌ ${iconName}.png 加载失败 (${response.status})`, 'error');
                    }
                } catch (error) {
                    errorCount++;
                    log(`❌ ${iconName}.png 加载错误: ${error.message}`, 'error');
                }
            }
            
            const summary = `图标测试完成: ${successCount} 成功, ${errorCount} 失败`;
            log(summary, errorCount > 0 ? 'warning' : 'success');
            
            resultsDiv.innerHTML = `
                <div class="test-item">
                    <div class="status-icon ${errorCount > 0 ? 'status-error' : 'status-success'}">
                        ${errorCount > 0 ? '✗' : '✓'}
                    </div>
                    <div>${summary}</div>
                </div>
            `;
        }
        
        // 游戏ID测试
        function testGameIds() {
            log('开始测试游戏ID匹配...');
            const resultsDiv = document.getElementById('game-id-results');
            
            // 模拟游戏配置 (从实际代码中获取)
            const expectedGameIds = ['temporal', 'spark', 'quantum'];
            const gameNames = ['时空织梦者', '瞬光捕手', '量子共鸣者'];
            
            let results = [];
            let allMatch = true;
            
            expectedGameIds.forEach((gameId, index) => {
                const cardSelector = `.game-card[data-game="${gameId}"]`;
                const cardElement = document.querySelector(cardSelector);
                
                if (cardElement) {
                    results.push({
                        gameId,
                        gameName: gameNames[index],
                        status: 'success',
                        message: `${gameNames[index]} ID匹配正确`
                    });
                    log(`✅ ${gameNames[index]} (${gameId}) ID匹配正确`, 'success');
                } else {
                    results.push({
                        gameId,
                        gameName: gameNames[index],
                        status: 'error',
                        message: `${gameNames[index]} ID不匹配或元素不存在`
                    });
                    log(`❌ ${gameNames[index]} (${gameId}) ID不匹配`, 'error');
                    allMatch = false;
                }
            });
            
            resultsDiv.innerHTML = results.map(result => `
                <div class="test-item">
                    <div class="status-icon status-${result.status}">
                        ${result.status === 'success' ? '✓' : '✗'}
                    </div>
                    <div>${result.message}</div>
                </div>
            `).join('');
            
            log(`游戏ID测试完成: ${allMatch ? '全部匹配' : '存在不匹配'}`, allMatch ? 'success' : 'error');
        }
        
        // 游戏启动测试
        function testGameLaunch() {
            log('开始测试游戏启动功能...');
            const resultsDiv = document.getElementById('game-launch-results');
            
            // 检查游戏启动相关的JavaScript是否加载
            const tests = [
                {
                    name: 'GameLauncher类',
                    test: () => typeof window.GameLauncher !== 'undefined',
                    message: 'GameLauncher类已定义'
                },
                {
                    name: 'gameLauncher实例',
                    test: () => typeof window.gameLauncher !== 'undefined',
                    message: 'gameLauncher全局实例已创建'
                },
                {
                    name: '游戏按钮事件',
                    test: () => {
                        const playBtns = document.querySelectorAll('.play-btn');
                        return playBtns.length > 0;
                    },
                    message: '游戏启动按钮已找到'
                },
                {
                    name: '游戏卡片',
                    test: () => {
                        const gameCards = document.querySelectorAll('.game-card[data-game]');
                        return gameCards.length === 3;
                    },
                    message: '找到3个游戏卡片'
                }
            ];
            
            let allPassed = true;
            const testResults = tests.map(test => {
                const passed = test.test();
                if (!passed) allPassed = false;
                
                log(`${passed ? '✅' : '❌'} ${test.name}: ${test.message}`, passed ? 'success' : 'error');
                
                return {
                    name: test.name,
                    passed,
                    message: test.message
                };
            });
            
            resultsDiv.innerHTML = testResults.map(result => `
                <div class="test-item">
                    <div class="status-icon status-${result.passed ? 'success' : 'error'}">
                        ${result.passed ? '✓' : '✗'}
                    </div>
                    <div>${result.name}: ${result.message}</div>
                </div>
            `).join('');
            
            log(`游戏启动测试完成: ${allPassed ? '全部通过' : '存在问题'}`, allPassed ? 'success' : 'error');
        }
        
        // PWA功能测试
        async function testPWA() {
            log('开始测试PWA功能...');
            const resultsDiv = document.getElementById('pwa-results');
            
            const tests = [
                {
                    name: 'Manifest文件',
                    test: async () => {
                        try {
                            const response = await fetch('/manifest.json');
                            return response.ok;
                        } catch {
                            return false;
                        }
                    },
                    message: 'manifest.json文件可访问'
                },
                {
                    name: 'Service Worker支持',
                    test: () => 'serviceWorker' in navigator,
                    message: '浏览器支持Service Worker'
                },
                {
                    name: 'PWA安装提示',
                    test: () => window.matchMedia('(display-mode: standalone)').matches || 
                                window.navigator.standalone === true,
                    message: 'PWA安装状态检测'
                },
                {
                    name: 'Chrome开发者工具配置',
                    test: async () => {
                        try {
                            const response = await fetch('/.well-known/appspecific/com.chrome.devtools.json');
                            return response.ok;
                        } catch {
                            return false;
                        }
                    },
                    message: 'Chrome开发者工具配置文件存在'
                }
            ];
            
            let results = [];
            for (const test of tests) {
                const passed = await test.test();
                results.push({
                    name: test.name,
                    passed,
                    message: test.message
                });
                
                log(`${passed ? '✅' : '❌'} ${test.name}: ${test.message}`, passed ? 'success' : 'error');
            }
            
            resultsDiv.innerHTML = results.map(result => `
                <div class="test-item">
                    <div class="status-icon status-${result.passed ? 'success' : 'error'}">
                        ${result.passed ? '✓' : '✗'}
                    </div>
                    <div>${result.name}: ${result.message}</div>
                </div>
            `).join('');
            
            const allPassed = results.every(r => r.passed);
            log(`PWA功能测试完成: ${allPassed ? '全部通过' : '部分功能需要改进'}`, allPassed ? 'success' : 'warning');
        }
        
        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', () => {
            log('测试页面加载完成', 'success');
            log('点击各个测试按钮开始验证修复效果', 'info');
        });
    </script>
</body>
</html>
