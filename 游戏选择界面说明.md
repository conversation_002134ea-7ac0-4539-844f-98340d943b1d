# Split-Second Spark 游戏选择界面

## 概述

本项目为 Split-Second Spark 游戏集合创建了一个统一的游戏选择界面，作为三个独立游戏的入口点：
- **时空织梦者** (Temporal Dream Weaver) - 时间操控解谜游戏
- **瞬光捕手** (Split-Second Spark) - 反应速度挑战游戏  
- **量子共鸣者** (Quantum Resonance) - 音乐节奏物理模拟游戏

## 功能特性

### 🎮 核心功能
- **游戏选择界面** - 美观的卡片式游戏展示
- **游戏启动** - 一键启动任意游戏
- **游戏预览** - 详细的游戏信息和操作说明
- **设置管理** - 主题、语言、性能等个性化设置

### 🌍 国际化支持
- **多语言** - 支持中文和英文界面
- **动态切换** - 实时语言切换无需刷新
- **本地化存储** - 记住用户语言偏好

### 💾 数据存储
- **多层存储** - IndexedDB > localStorage > 内存存储
- **自动降级** - 根据浏览器支持自动选择最佳存储方案
- **数据持久化** - 用户设置和游戏统计数据保存

### 🎨 界面设计
- **响应式设计** - 适配桌面、平板、手机等各种设备
- **深色主题** - 现代化的深色界面设计
- **动画效果** - 流畅的过渡动画和交互反馈
- **视觉特效** - 每个游戏独特的动画效果

### ⚙️ 性能优化
- **模块化架构** - 清晰的代码组织结构
- **懒加载** - 按需加载资源
- **性能设置** - 可调节的视觉效果级别
- **帧率控制** - 可配置的帧率限制

## 技术架构

### 📁 文件结构
```
/
├── index.html              # 主入口文件
├── styles/                 # 样式文件目录
│   ├── main.css           # 主样式文件
│   ├── game-selector.css  # 游戏选择器样式
│   └── responsive.css     # 响应式样式
├── js/                    # JavaScript模块目录
│   ├── utils/             # 工具模块
│   │   ├── storage.js     # 存储服务
│   │   └── i18n.js        # 国际化服务
│   ├── core/              # 核心模块
│   │   └── game-launcher.js # 游戏启动器
│   ├── ui/                # UI模块
│   │   ├── modal-manager.js    # 模态框管理器
│   │   └── settings-manager.js # 设置管理器
│   └── main.js            # 主应用程序
├── 时空织梦者/             # 时空织梦者游戏目录
├── 瞬光捕手/               # 瞬光捕手游戏目录
└── 量子共鸣者/             # 量子共鸣者游戏目录
```

### 🏗️ 模块设计

#### 存储服务 (StorageService)
- **多后端支持** - IndexedDB、localStorage、内存存储
- **统一接口** - 简化的键值存储API
- **自动初始化** - 智能选择最佳存储方案
- **错误处理** - 完善的异常处理机制

#### 国际化服务 (I18nService)
- **翻译管理** - 集中管理所有翻译文本
- **语言检测** - 自动检测浏览器语言
- **动态应用** - 实时更新页面文本
- **回退机制** - 缺失翻译时的回退策略

#### 游戏启动器 (GameLauncher)
- **游戏管理** - 统一管理所有游戏信息
- **启动逻辑** - 处理游戏启动和跳转
- **兼容性检查** - 验证浏览器兼容性
- **统计记录** - 记录游戏启动统计

#### 模态框管理器 (ModalManager)
- **模态框控制** - 统一的模态框显示/隐藏
- **事件处理** - 键盘和鼠标事件处理
- **内容管理** - 动态内容加载和更新
- **用户交互** - 流畅的用户交互体验

#### 设置管理器 (SettingsManager)
- **设置存储** - 用户设置的持久化存储
- **实时应用** - 设置变更的实时应用
- **默认值** - 完善的默认设置系统
- **变更监听** - 设置变更事件通知

## 使用说明

### 🚀 启动应用
1. 在项目根目录启动HTTP服务器
2. 访问 `http://localhost:8000`
3. 等待应用初始化完成

### 🎮 游戏操作
- **选择游戏** - 点击游戏卡片查看详情
- **开始游戏** - 点击"开始游戏"按钮启动
- **预览游戏** - 点击"预览"按钮查看详细信息
- **返回主界面** - 在游戏中按ESC键返回

### ⚙️ 设置配置
- **主题切换** - 深色/浅色/自动主题
- **语言切换** - 中文/英文界面语言
- **性能调节** - 高/中/低视觉效果级别
- **帧率限制** - 60FPS/30FPS/自动

### 📱 设备适配
- **桌面设备** - 完整功能和最佳体验
- **平板设备** - 优化的触摸交互
- **手机设备** - 紧凑的移动端布局
- **横屏模式** - 特殊的横屏适配

## 开发特性

### 🔧 开发工具
- **控制台日志** - 详细的调试信息输出
- **错误处理** - 完善的错误捕获和处理
- **性能监控** - 可选的FPS显示
- **热重载** - 开发时的实时更新

### 🧪 测试支持
- **兼容性测试** - 浏览器兼容性检查
- **功能测试** - 各模块功能验证
- **性能测试** - 性能指标监控
- **用户体验测试** - 交互体验验证

### 📊 分析统计
- **使用统计** - 游戏启动次数统计
- **性能分析** - 应用性能数据收集
- **错误报告** - 自动错误信息收集
- **用户行为** - 用户操作行为分析

## 浏览器兼容性

### ✅ 支持的浏览器
- **Chrome** 80+ (推荐)
- **Firefox** 75+
- **Safari** 13+
- **Edge** 80+

### 🔧 技术要求
- **ES6+** - 现代JavaScript语法
- **CSS Grid** - 网格布局支持
- **Web Storage** - 本地存储支持
- **Canvas 2D** - 2D图形渲染

### ⚠️ 已知限制
- **IE浏览器** - 不支持Internet Explorer
- **旧版Safari** - 部分功能可能受限
- **移动端浏览器** - 某些高级功能可能不可用

## 更新日志

### v1.0.0 (2024-07-31)
- ✨ 初始版本发布
- 🎮 实现三个游戏的统一入口
- 🌍 添加中英文双语支持
- 💾 实现多层存储系统
- 🎨 完成响应式界面设计
- ⚙️ 添加完整的设置系统
- 📱 优化移动端体验

## 贡献指南

### 🤝 参与开发
1. Fork 项目仓库
2. 创建功能分支
3. 提交代码变更
4. 创建Pull Request

### 📝 代码规范
- **注释** - 使用中文注释说明关键逻辑
- **命名** - 使用有意义的变量和函数名
- **格式** - 保持一致的代码格式
- **测试** - 为新功能添加测试

### 🐛 问题报告
- **详细描述** - 提供详细的问题描述
- **重现步骤** - 说明问题重现步骤
- **环境信息** - 提供浏览器和系统信息
- **截图** - 如有必要提供截图

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

---

**Split-Second Spark Team**  
© 2024 All rights reserved.
