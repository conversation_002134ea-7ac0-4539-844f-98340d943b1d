<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #0f0f23;
            color: #ffffff;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .icon-preview {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        
        .icon-canvas {
            border: 1px solid #333;
            background: #fff;
            margin-bottom: 5px;
        }
        
        .download-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 5px 10px;
            cursor: pointer;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .download-btn:hover {
            background: #45a049;
        }
        
        .generate-btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            border-radius: 5px;
            font-size: 16px;
            margin: 20px 0;
        }
        
        .generate-btn:hover {
            background: #1976D2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Split-Second Spark 图标生成器</h1>
        <p>点击下面的按钮生成应用所需的图标文件</p>
        
        <button class="generate-btn" onclick="generateAllIcons()">生成所有图标</button>
        
        <div id="icons-container"></div>
    </div>

    <script>
        // 图标尺寸配置
        const iconSizes = [
            { size: 16, name: 'favicon-16x16' },
            { size: 32, name: 'favicon-32x32' },
            { size: 72, name: 'icon-72x72' },
            { size: 96, name: 'icon-96x96' },
            { size: 128, name: 'icon-128x128' },
            { size: 144, name: 'icon-144x144' },
            { size: 152, name: 'icon-152x152' },
            { size: 192, name: 'icon-192x192' },
            { size: 384, name: 'icon-384x384' },
            { size: 512, name: 'icon-512x512' }
        ];

        // 游戏特定图标
        const gameIcons = [
            { size: 96, name: 'temporal-icon-96x96', theme: 'temporal' },
            { size: 96, name: 'spark-icon-96x96', theme: 'spark' },
            { size: 96, name: 'quantum-icon-96x96', theme: 'quantum' }
        ];

        /**
         * 生成主应用图标
         */
        function generateMainIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');

            // 背景渐变
            const gradient = ctx.createRadialGradient(size/2, size/2, 0, size/2, size/2, size/2);
            gradient.addColorStop(0, '#4a90e2');
            gradient.addColorStop(0.5, '#2c5aa0');
            gradient.addColorStop(1, '#0f0f23');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);

            // 绘制闪电图标
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            
            const centerX = size / 2;
            const centerY = size / 2;
            const scale = size / 100;
            
            // 闪电形状
            ctx.moveTo(centerX - 15 * scale, centerY - 25 * scale);
            ctx.lineTo(centerX + 5 * scale, centerY - 25 * scale);
            ctx.lineTo(centerX - 5 * scale, centerY - 5 * scale);
            ctx.lineTo(centerX + 15 * scale, centerY - 5 * scale);
            ctx.lineTo(centerX + 5 * scale, centerY + 25 * scale);
            ctx.lineTo(centerX - 5 * scale, centerY + 5 * scale);
            ctx.lineTo(centerX - 15 * scale, centerY + 5 * scale);
            ctx.closePath();
            ctx.fill();

            // 添加光晕效果
            ctx.shadowColor = '#ffffff';
            ctx.shadowBlur = 10 * scale;
            ctx.fill();

            return canvas;
        }

        /**
         * 生成游戏特定图标
         */
        function generateGameIcon(size, theme) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');

            const centerX = size / 2;
            const centerY = size / 2;
            const scale = size / 100;

            // 根据主题设置颜色
            let colors;
            switch (theme) {
                case 'temporal':
                    colors = { bg: '#6a4c93', accent: '#c06c84', light: '#f8b500' };
                    break;
                case 'spark':
                    colors = { bg: '#ff6b6b', accent: '#feca57', light: '#ffffff' };
                    break;
                case 'quantum':
                    colors = { bg: '#4834d4', accent: '#686de0', light: '#30e3ca' };
                    break;
                default:
                    colors = { bg: '#4a90e2', accent: '#2c5aa0', light: '#ffffff' };
            }

            // 背景渐变
            const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, size/2);
            gradient.addColorStop(0, colors.accent);
            gradient.addColorStop(1, colors.bg);
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);

            // 绘制主题相关图形
            ctx.fillStyle = colors.light;
            ctx.strokeStyle = colors.light;
            ctx.lineWidth = 2 * scale;

            switch (theme) {
                case 'temporal':
                    // 时钟图标
                    ctx.beginPath();
                    ctx.arc(centerX, centerY, 25 * scale, 0, 2 * Math.PI);
                    ctx.stroke();
                    
                    // 时针
                    ctx.beginPath();
                    ctx.moveTo(centerX, centerY);
                    ctx.lineTo(centerX, centerY - 15 * scale);
                    ctx.stroke();
                    
                    // 分针
                    ctx.beginPath();
                    ctx.moveTo(centerX, centerY);
                    ctx.lineTo(centerX + 20 * scale, centerY);
                    ctx.stroke();
                    break;

                case 'spark':
                    // 闪电图标
                    ctx.beginPath();
                    ctx.moveTo(centerX - 10 * scale, centerY - 20 * scale);
                    ctx.lineTo(centerX + 5 * scale, centerY - 20 * scale);
                    ctx.lineTo(centerX - 5 * scale, centerY);
                    ctx.lineTo(centerX + 10 * scale, centerY);
                    ctx.lineTo(centerX - 5 * scale, centerY + 20 * scale);
                    ctx.lineTo(centerX + 5 * scale, centerY);
                    ctx.lineTo(centerX - 10 * scale, centerY);
                    ctx.closePath();
                    ctx.fill();
                    break;

                case 'quantum':
                    // 原子轨道图标
                    ctx.beginPath();
                    ctx.arc(centerX, centerY, 20 * scale, 0, 2 * Math.PI);
                    ctx.stroke();
                    
                    // 轨道1
                    ctx.save();
                    ctx.translate(centerX, centerY);
                    ctx.rotate(Math.PI / 4);
                    ctx.beginPath();
                    ctx.ellipse(0, 0, 25 * scale, 10 * scale, 0, 0, 2 * Math.PI);
                    ctx.stroke();
                    ctx.restore();
                    
                    // 轨道2
                    ctx.save();
                    ctx.translate(centerX, centerY);
                    ctx.rotate(-Math.PI / 4);
                    ctx.beginPath();
                    ctx.ellipse(0, 0, 25 * scale, 10 * scale, 0, 0, 2 * Math.PI);
                    ctx.stroke();
                    ctx.restore();
                    
                    // 中心点
                    ctx.beginPath();
                    ctx.arc(centerX, centerY, 3 * scale, 0, 2 * Math.PI);
                    ctx.fill();
                    break;
            }

            return canvas;
        }

        /**
         * 下载canvas为PNG文件
         */
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename + '.png';
            link.href = canvas.toDataURL();
            link.click();
        }

        /**
         * 生成所有图标
         */
        function generateAllIcons() {
            const container = document.getElementById('icons-container');
            container.innerHTML = '';

            // 生成主应用图标
            iconSizes.forEach(config => {
                const canvas = generateMainIcon(config.size);
                canvas.className = 'icon-canvas';
                
                const preview = document.createElement('div');
                preview.className = 'icon-preview';
                
                const label = document.createElement('div');
                label.textContent = `${config.name} (${config.size}x${config.size})`;
                
                const downloadBtn = document.createElement('button');
                downloadBtn.className = 'download-btn';
                downloadBtn.textContent = '下载';
                downloadBtn.onclick = () => downloadCanvas(canvas, config.name);
                
                preview.appendChild(canvas);
                preview.appendChild(label);
                preview.appendChild(downloadBtn);
                container.appendChild(preview);
            });

            // 生成游戏特定图标
            gameIcons.forEach(config => {
                const canvas = generateGameIcon(config.size, config.theme);
                canvas.className = 'icon-canvas';
                
                const preview = document.createElement('div');
                preview.className = 'icon-preview';
                
                const label = document.createElement('div');
                label.textContent = `${config.name} (${config.size}x${config.size})`;
                
                const downloadBtn = document.createElement('button');
                downloadBtn.className = 'download-btn';
                downloadBtn.textContent = '下载';
                downloadBtn.onclick = () => downloadCanvas(canvas, config.name);
                
                preview.appendChild(canvas);
                preview.appendChild(label);
                preview.appendChild(downloadBtn);
                container.appendChild(preview);
            });

            // 添加批量下载按钮
            const batchDownloadBtn = document.createElement('button');
            batchDownloadBtn.className = 'generate-btn';
            batchDownloadBtn.textContent = '批量下载所有图标';
            batchDownloadBtn.onclick = downloadAllIcons;
            container.appendChild(batchDownloadBtn);
        }

        /**
         * 批量下载所有图标
         */
        function downloadAllIcons() {
            // 下载主应用图标
            iconSizes.forEach(config => {
                const canvas = generateMainIcon(config.size);
                setTimeout(() => downloadCanvas(canvas, config.name), 100);
            });

            // 下载游戏特定图标
            gameIcons.forEach((config, index) => {
                const canvas = generateGameIcon(config.size, config.theme);
                setTimeout(() => downloadCanvas(canvas, config.name), 200 + index * 100);
            });

            alert('图标生成完成！请将下载的图标文件放置到 assets/images/ 目录中。');
        }

        // 页面加载完成后自动生成预览
        window.addEventListener('load', generateAllIcons);
    </script>
</body>
</html>
