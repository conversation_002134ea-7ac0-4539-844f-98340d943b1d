# 游戏需求说明

## 1. 游戏概述

- **游戏主题**: 捕捉决定性瞬间，引燃无限可能。
- **游戏名称**: 根据游戏的主题设计一个 3 到 8 个字的名称
- **游戏类型**: 根据游戏的主题设计
- **国际化**: 支持中英文

## 2. 核心功能需求

### 2.1. 跨平台支持

- **PC 端**: 游戏界面和操作需适配主流浏览器（Chrome, Firefox, Safari, Edge）的桌面版本。支持键盘和鼠标操作。
- **移动端**: 游戏界面需采用响应式设计，完美适配手机和平板等移动设备。主要操作通过触摸完成。
- **静态部署**: 整个游戏应为纯前端项目，可以部署在任何静态文件服务器上（如 GitHub Pages, Vercel, Netlify 等），无需后端服务。

### 2.2. 游戏画面与体验

- **画面精美**
- **可玩性高**
- **耐玩性高**

### 2.3. 自定义 KV 存储服务

- **目的**: 实现一个统一的键值(Key-Value)存储服务，用于管理所有玩家数据。该服务需支持多种后端适配器。
- **默认实现**: 默认使用浏览器的 `localStorage` 或 `IndexedDB` 作为存储后端。
- **接口要求**:
    - `put(key, value)`: 保存数据。
    - `get(key)`: 读取数据。
    - `delete(key)`: 删除数据。
    - `list(prefix)`: 列出所有指定前缀的 key。
- **可扩展性**: 架构上应易于扩展，未来可以接入其他存储服务（如云存储、私有服务器等）。

### 2.4. 玩家信息与状态管理

- **实现方式**: 通过自定义的 KV 存储服务。
- **玩家账号**:
    - 支持创建和切换账号。
    - 每个账号独立存储其游戏进度、分数和设置。
- **游戏状态保存**:
    - 自动保存玩家的游戏进度和当前状态。
    - 重新进入游戏时，自动恢复上一次的游戏状态。

### 2.5. 排行榜系统

- **实现方式**: 通过自定义的 KV 存储服务。

### 2.6. 自定义关卡

- **实现方式**: 通过自定义的 KV 存储服务。
- **关卡编辑器**:
    - 提供一个可视化的界面，让玩家可以创建关卡定制内容。
    - 提供保存、加载和测试功能。
- **关卡管理**:
    - 玩家只能编辑和删除自己创建的关卡。
    - 关卡数据以特定格式（如 JSON）存储。
- **关卡分享与游玩**:
    - **获取列表**: 游戏内应有一个界面，用于展示所有已分享的自定义关卡列表, 排序方式以玩家点赞或者踩的积分计算, 每个玩家每个自定义关卡只能点赞或者踩一次, 其次通过关卡的复杂度进行排序。
    - **游玩**: 玩家可以点击列表中的任意关卡进行游戏。

## 3. 实现方式

通过在当前项目创建一个游戏名称的目录, 并在该目录实现
