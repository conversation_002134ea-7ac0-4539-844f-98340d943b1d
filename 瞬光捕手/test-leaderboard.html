<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排行榜功能测试 - 瞬光捕手</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .test-section h2 {
            color: #4ecdc4;
            border-bottom: 2px solid #4ecdc4;
            padding-bottom: 10px;
        }
        
        .button {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid #4CAF50;
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.3);
            border: 1px solid #f44336;
        }
        
        .status.info {
            background: rgba(33, 150, 243, 0.3);
            border: 1px solid #2196F3;
        }
        
        .leaderboard-display {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .leaderboard-entry {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .leaderboard-entry:last-child {
            border-bottom: none;
        }
        
        .rank {
            font-weight: bold;
            color: #4ecdc4;
            width: 40px;
        }
        
        .player-name {
            flex: 1;
            margin: 0 10px;
        }
        
        .score {
            font-weight: bold;
            color: #ffeb3b;
        }
        
        .log {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 5px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏆 排行榜功能测试</h1>
        
        <div class="test-section">
            <h2>初始化状态</h2>
            <div id="init-status" class="status info">正在检查初始化状态...</div>
            <button class="button" onclick="checkInitialization()">检查初始化</button>
        </div>
        
        <div class="test-section">
            <h2>排行榜管理器测试</h2>
            <div id="manager-status" class="status info">等待测试...</div>
            <button class="button" onclick="testLeaderboardManager()">测试管理器</button>
            <button class="button" onclick="testSubmitScore()">提交测试分数</button>
            <button class="button" onclick="testGetLeaderboard()">获取排行榜</button>
            <button class="button" onclick="testPlayerRank()">查询玩家排名</button>
        </div>
        
        <div class="test-section">
            <h2>排行榜显示</h2>
            <div id="leaderboard-status" class="status info">等待加载...</div>
            <div>
                <label>排行榜类型: </label>
                <select id="leaderboard-type">
                    <option value="global_high_score">全球排行</option>
                    <option value="daily_high_score">今日排行</option>
                    <option value="weekly_high_score">本周排行</option>
                    <option value="perfect_hits">完美击中</option>
                    <option value="combo_record">连击记录</option>
                </select>
                <button class="button" onclick="displayLeaderboard()">显示排行榜</button>
                <button class="button" onclick="clearLeaderboard()">清空排行榜</button>
            </div>
            <div id="leaderboard-display" class="leaderboard-display"></div>
        </div>
        
        <div class="test-section">
            <h2>数据重置测试</h2>
            <div id="reset-status" class="status info">等待操作...</div>
            <button class="button" onclick="testResetDaily()">重置每日排行榜</button>
            <button class="button" onclick="testResetWeekly()">重置每周排行榜</button>
            <button class="button" onclick="testBackupRestore()">测试备份恢复</button>
        </div>
        
        <div class="test-section">
            <h2>控制台日志</h2>
            <div id="console-log" class="log"></div>
            <button class="button" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/core/player-manager.js"></script>
    <script src="js/core/leaderboard-manager.js"></script>
    
    <script>
        // 重写console.log以显示在页面上
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToLog(message, type = 'log') {
            const logElement = document.getElementById('console-log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : '📝';
            logElement.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog(args.join(' '), 'warn');
        };
        
        // 测试函数
        async function checkInitialization() {
            const statusEl = document.getElementById('init-status');
            
            try {
                // 初始化存储服务
                await storageService.init();
                console.log('✅ 存储服务初始化完成');
                
                // 初始化国际化服务
                await i18nService.init();
                console.log('✅ 国际化服务初始化完成');
                
                // 初始化玩家管理器
                await playerManager.init();
                console.log('✅ 玩家管理器初始化完成');
                
                // 初始化排行榜管理器
                await leaderboardManager.init();
                console.log('✅ 排行榜管理器初始化完成');
                
                statusEl.className = 'status success';
                statusEl.textContent = '✅ 所有模块初始化成功';
                
            } catch (error) {
                console.error('初始化失败:', error);
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 初始化失败: ' + error.message;
            }
        }
        
        function testLeaderboardManager() {
            const statusEl = document.getElementById('manager-status');
            
            try {
                if (!window.leaderboardManager) {
                    throw new Error('排行榜管理器未定义');
                }
                
                if (!leaderboardManager.initialized) {
                    throw new Error('排行榜管理器未初始化');
                }
                
                // 测试获取可用排行榜
                const availableLeaderboards = leaderboardManager.getAvailableLeaderboards();
                console.log('可用排行榜:', availableLeaderboards);
                
                statusEl.className = 'status success';
                statusEl.textContent = `✅ 排行榜管理器正常，共${availableLeaderboards.length}个排行榜类型`;
                
            } catch (error) {
                console.error('排行榜管理器测试失败:', error);
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 测试失败: ' + error.message;
            }
        }
        
        async function testSubmitScore() {
            const statusEl = document.getElementById('manager-status');
            
            try {
                // 创建测试分数数据
                const testScore = {
                    score: Math.floor(Math.random() * 100000) + 10000,
                    level: Math.floor(Math.random() * 10) + 1,
                    perfectHits: Math.floor(Math.random() * 50),
                    combo: Math.floor(Math.random() * 20) + 1,
                    duration: Math.floor(Math.random() * 300000) + 60000,
                    accuracy: Math.random() * 100
                };
                
                console.log('提交测试分数:', testScore);
                
                // 提交到全球排行榜
                const result = await leaderboardManager.submitScore(
                    leaderboardManager.leaderboardTypes.GLOBAL_HIGH_SCORE,
                    testScore
                );
                
                if (result.success) {
                    console.log('✅ 分数提交成功，排名:', result.rank);
                    statusEl.className = 'status success';
                    statusEl.textContent = `✅ 分数提交成功，排名: ${result.rank}`;
                } else {
                    console.warn('⚠️ 分数提交失败:', result.reason);
                    statusEl.className = 'status error';
                    statusEl.textContent = `❌ 分数提交失败: ${result.reason}`;
                }
                
            } catch (error) {
                console.error('提交分数测试失败:', error);
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 提交失败: ' + error.message;
            }
        }
        
        function testGetLeaderboard() {
            const statusEl = document.getElementById('manager-status');
            
            try {
                const leaderboard = leaderboardManager.getLeaderboard('global_high_score', 10);
                
                if (leaderboard) {
                    console.log('获取排行榜成功:', leaderboard);
                    statusEl.className = 'status success';
                    statusEl.textContent = `✅ 获取排行榜成功，共${leaderboard.entries.length}条记录`;
                } else {
                    console.log('排行榜为空');
                    statusEl.className = 'status info';
                    statusEl.textContent = 'ℹ️ 排行榜为空';
                }
                
            } catch (error) {
                console.error('获取排行榜失败:', error);
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 获取失败: ' + error.message;
            }
        }
        
        function testPlayerRank() {
            const statusEl = document.getElementById('manager-status');
            
            try {
                const currentPlayer = playerManager.getCurrentPlayer();
                if (!currentPlayer) {
                    throw new Error('当前没有玩家');
                }
                
                const playerRank = leaderboardManager.getPlayerRank('global_high_score', currentPlayer.id);
                
                if (playerRank) {
                    console.log('玩家排名:', playerRank);
                    statusEl.className = 'status success';
                    statusEl.textContent = `✅ 玩家排名: ${playerRank.rank}/${playerRank.totalEntries}`;
                } else {
                    console.log('玩家未上榜');
                    statusEl.className = 'status info';
                    statusEl.textContent = 'ℹ️ 玩家未上榜';
                }
                
            } catch (error) {
                console.error('查询玩家排名失败:', error);
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 查询失败: ' + error.message;
            }
        }
        
        function displayLeaderboard() {
            const statusEl = document.getElementById('leaderboard-status');
            const displayEl = document.getElementById('leaderboard-display');
            const typeSelect = document.getElementById('leaderboard-type');
            
            try {
                const type = typeSelect.value;
                const leaderboard = leaderboardManager.getLeaderboard(type, 20);
                
                if (!leaderboard || leaderboard.entries.length === 0) {
                    displayEl.innerHTML = '<div style="text-align: center; color: #666;">暂无排行榜数据</div>';
                    statusEl.className = 'status info';
                    statusEl.textContent = 'ℹ️ 排行榜为空';
                    return;
                }
                
                let html = `<h3>${leaderboard.title}</h3>`;
                html += `<div style="font-size: 12px; color: #999; margin-bottom: 10px;">
                    最后更新: ${new Date(leaderboard.lastUpdated).toLocaleString()}
                </div>`;
                
                leaderboard.entries.forEach((entry, index) => {
                    html += `
                        <div class="leaderboard-entry">
                            <div class="rank">#${index + 1}</div>
                            <div class="player-name">${entry.playerName}</div>
                            <div class="score">${leaderboardManager.formatScore(entry.score)}</div>
                        </div>
                    `;
                });
                
                displayEl.innerHTML = html;
                statusEl.className = 'status success';
                statusEl.textContent = `✅ 显示${leaderboard.entries.length}条记录`;
                
            } catch (error) {
                console.error('显示排行榜失败:', error);
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 显示失败: ' + error.message;
            }
        }
        
        async function clearLeaderboard() {
            const statusEl = document.getElementById('leaderboard-status');
            const typeSelect = document.getElementById('leaderboard-type');
            
            try {
                const type = typeSelect.value;
                await leaderboardManager.resetLeaderboard(type);
                
                console.log('排行榜已清空:', type);
                statusEl.className = 'status success';
                statusEl.textContent = '✅ 排行榜已清空';
                
                // 刷新显示
                displayLeaderboard();
                
            } catch (error) {
                console.error('清空排行榜失败:', error);
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 清空失败: ' + error.message;
            }
        }
        
        async function testResetDaily() {
            const statusEl = document.getElementById('reset-status');
            
            try {
                await leaderboardManager.resetLeaderboard('daily_high_score');
                console.log('每日排行榜已重置');
                statusEl.className = 'status success';
                statusEl.textContent = '✅ 每日排行榜已重置';
                
            } catch (error) {
                console.error('重置每日排行榜失败:', error);
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 重置失败: ' + error.message;
            }
        }
        
        async function testResetWeekly() {
            const statusEl = document.getElementById('reset-status');
            
            try {
                await leaderboardManager.resetLeaderboard('weekly_high_score');
                console.log('每周排行榜已重置');
                statusEl.className = 'status success';
                statusEl.textContent = '✅ 每周排行榜已重置';
                
            } catch (error) {
                console.error('重置每周排行榜失败:', error);
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 重置失败: ' + error.message;
            }
        }
        
        async function testBackupRestore() {
            const statusEl = document.getElementById('reset-status');
            
            try {
                // 这里只是测试备份功能，实际的恢复需要更复杂的逻辑
                const leaderboard = leaderboardManager.getLeaderboard('global_high_score');
                if (leaderboard) {
                    await leaderboardManager.backupLeaderboard(leaderboard);
                    console.log('排行榜备份完成');
                    statusEl.className = 'status success';
                    statusEl.textContent = '✅ 排行榜备份完成';
                } else {
                    statusEl.className = 'status info';
                    statusEl.textContent = 'ℹ️ 没有可备份的排行榜数据';
                }
                
            } catch (error) {
                console.error('备份测试失败:', error);
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 备份失败: ' + error.message;
            }
        }
        
        function clearLog() {
            document.getElementById('console-log').textContent = '';
        }
        
        // 页面加载完成后自动检查初始化
        window.addEventListener('load', () => {
            setTimeout(checkInitialization, 1000);
        });
    </script>
</body>
</html>
