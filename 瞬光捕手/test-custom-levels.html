<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自定义关卡功能测试 - 瞬光捕手</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 2rem;
            margin: 0;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 12px;
            padding: 2rem;
            backdrop-filter: blur(10px);
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        
        .test-section h3 {
            color: #4CAF50;
            margin-bottom: 1rem;
        }
        
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            margin: 0.5rem;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }
        
        .test-button:hover {
            background: linear-gradient(45deg, #45a049, #3d8b40);
            transform: translateY(-1px);
        }
        
        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.85rem;
            white-space: pre-wrap;
        }
        
        .success {
            border-left: 4px solid #4CAF50;
        }
        
        .error {
            border-left: 4px solid #F44336;
        }
        
        .warning {
            border-left: 4px solid #FF9800;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎮 自定义关卡功能测试</h1>
        <p>这个页面用于测试自定义关卡系统的各项功能</p>
        
        <div class="test-section">
            <h3>📋 基础功能测试</h3>
            <button class="test-button" onclick="testStorageService()">测试存储服务</button>
            <button class="test-button" onclick="testLevelManager()">测试关卡管理器</button>
            <button class="test-button" onclick="testI18nService()">测试国际化服务</button>
            <div id="basic-test-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>🎯 关卡管理测试</h3>
            <button class="test-button" onclick="testCreateLevel()">创建测试关卡</button>
            <button class="test-button" onclick="testLoadLevels()">加载关卡列表</button>
            <button class="test-button" onclick="testRateLevel()">测试评分功能</button>
            <button class="test-button" onclick="testDeleteLevel()">删除测试关卡</button>
            <div id="level-test-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>🎨 界面功能测试</h3>
            <button class="test-button" onclick="testScreenManager()">测试屏幕管理器</button>
            <button class="test-button" onclick="testCustomLevelsUI()">测试自定义关卡界面</button>
            <button class="test-button" onclick="testLevelDetailDialog()">测试关卡详情对话框</button>
            <div id="ui-test-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>📱 响应式设计测试</h3>
            <button class="test-button" onclick="testResponsiveDesign()">测试响应式布局</button>
            <button class="test-button" onclick="testMobileInteraction()">测试移动端交互</button>
            <div id="responsive-test-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>🚀 性能测试</h3>
            <button class="test-button" onclick="testPerformance()">测试加载性能</button>
            <button class="test-button" onclick="testMemoryUsage()">测试内存使用</button>
            <div id="performance-test-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>🔧 集成测试</h3>
            <button class="test-button" onclick="runAllTests()">运行所有测试</button>
            <button class="test-button" onclick="generateTestReport()">生成测试报告</button>
            <div id="integration-test-result" class="test-result"></div>
        </div>
    </div>

    <script>
        // 测试结果记录
        let testResults = [];
        
        // 工具函数
        function logResult(section, test, status, message, details = '') {
            const result = {
                section,
                test,
                status,
                message,
                details,
                timestamp: new Date().toISOString()
            };
            testResults.push(result);
            
            const resultDiv = document.getElementById(section + '-test-result');
            const statusClass = status === 'success' ? 'success' : status === 'error' ? 'error' : 'warning';
            resultDiv.className = `test-result ${statusClass}`;
            resultDiv.textContent = `[${status.toUpperCase()}] ${test}: ${message}\n${details}`;
        }
        
        // 基础功能测试
        function testStorageService() {
            try {
                if (typeof storageService !== 'undefined') {
                    logResult('basic', '存储服务', 'success', '存储服务已加载', '可以进行数据存储操作');
                } else {
                    logResult('basic', '存储服务', 'error', '存储服务未找到', '请检查storage.js是否正确加载');
                }
            } catch (error) {
                logResult('basic', '存储服务', 'error', '测试失败', error.message);
            }
        }
        
        function testLevelManager() {
            try {
                if (typeof levelManager !== 'undefined') {
                    const methods = ['createCustomLevel', 'getAllLevels', 'rateLevelLevel', 'deleteCustomLevel'];
                    const missingMethods = methods.filter(method => typeof levelManager[method] !== 'function');
                    
                    if (missingMethods.length === 0) {
                        logResult('basic', '关卡管理器', 'success', '所有必需方法都存在', `检查的方法: ${methods.join(', ')}`);
                    } else {
                        logResult('basic', '关卡管理器', 'warning', '部分方法缺失', `缺失方法: ${missingMethods.join(', ')}`);
                    }
                } else {
                    logResult('basic', '关卡管理器', 'error', '关卡管理器未找到', '请检查level-manager.js是否正确加载');
                }
            } catch (error) {
                logResult('basic', '关卡管理器', 'error', '测试失败', error.message);
            }
        }
        
        function testI18nService() {
            try {
                if (typeof i18nService !== 'undefined') {
                    const testKey = 'customLevels.title';
                    const translation = i18nService.t(testKey);
                    
                    if (translation && translation !== testKey) {
                        logResult('basic', '国际化服务', 'success', '翻译功能正常', `${testKey} -> ${translation}`);
                    } else {
                        logResult('basic', '国际化服务', 'warning', '翻译键未找到', `无法翻译: ${testKey}`);
                    }
                } else {
                    logResult('basic', '国际化服务', 'error', '国际化服务未找到', '请检查i18n.js是否正确加载');
                }
            } catch (error) {
                logResult('basic', '国际化服务', 'error', '测试失败', error.message);
            }
        }
        
        // 关卡管理测试
        function testCreateLevel() {
            logResult('level', '创建关卡', 'warning', '需要完整游戏环境', '此测试需要在完整的游戏环境中运行');
        }
        
        function testLoadLevels() {
            logResult('level', '加载关卡', 'warning', '需要完整游戏环境', '此测试需要在完整的游戏环境中运行');
        }
        
        function testRateLevel() {
            logResult('level', '评分功能', 'warning', '需要完整游戏环境', '此测试需要在完整的游戏环境中运行');
        }
        
        function testDeleteLevel() {
            logResult('level', '删除关卡', 'warning', '需要完整游戏环境', '此测试需要在完整的游戏环境中运行');
        }
        
        // 界面功能测试
        function testScreenManager() {
            try {
                if (typeof screenManager !== 'undefined') {
                    const methods = ['showCustomLevels', 'showLevelDetail', 'playLevel'];
                    const missingMethods = methods.filter(method => typeof screenManager[method] !== 'function');
                    
                    if (missingMethods.length === 0) {
                        logResult('ui', '屏幕管理器', 'success', '自定义关卡相关方法存在', `检查的方法: ${methods.join(', ')}`);
                    } else {
                        logResult('ui', '屏幕管理器', 'warning', '部分方法缺失', `缺失方法: ${missingMethods.join(', ')}`);
                    }
                } else {
                    logResult('ui', '屏幕管理器', 'error', '屏幕管理器未找到', '请检查screen-manager.js是否正确加载');
                }
            } catch (error) {
                logResult('ui', '屏幕管理器', 'error', '测试失败', error.message);
            }
        }
        
        function testCustomLevelsUI() {
            const customLevelsScreen = document.getElementById('custom-levels-screen');
            if (customLevelsScreen) {
                logResult('ui', '自定义关卡界面', 'success', 'HTML结构存在', '界面元素已正确添加到DOM');
            } else {
                logResult('ui', '自定义关卡界面', 'error', 'HTML结构缺失', '请检查index.html中的自定义关卡界面');
            }
        }
        
        function testLevelDetailDialog() {
            const levelDetailDialog = document.getElementById('level-detail-dialog');
            if (levelDetailDialog) {
                logResult('ui', '关卡详情对话框', 'success', 'HTML结构存在', '对话框元素已正确添加到DOM');
            } else {
                logResult('ui', '关卡详情对话框', 'error', 'HTML结构缺失', '请检查index.html中的关卡详情对话框');
            }
        }
        
        // 响应式设计测试
        function testResponsiveDesign() {
            const viewport = window.innerWidth;
            let deviceType = 'desktop';
            
            if (viewport <= 480) deviceType = 'small-mobile';
            else if (viewport <= 768) deviceType = 'mobile';
            else if (viewport <= 1024) deviceType = 'tablet';
            
            logResult('responsive', '响应式布局', 'success', `当前设备类型: ${deviceType}`, `视口宽度: ${viewport}px`);
        }
        
        function testMobileInteraction() {
            const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
            logResult('responsive', '移动端交互', 'success', `触摸支持: ${isTouchDevice ? '是' : '否'}`, `用户代理: ${navigator.userAgent.substring(0, 50)}...`);
        }
        
        // 性能测试
        function testPerformance() {
            const startTime = performance.now();
            
            // 模拟一些操作
            for (let i = 0; i < 10000; i++) {
                const div = document.createElement('div');
                div.textContent = 'test';
            }
            
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            logResult('performance', '加载性能', 'success', `操作耗时: ${duration.toFixed(2)}ms`, '性能测试完成');
        }
        
        function testMemoryUsage() {
            if (performance.memory) {
                const memory = performance.memory;
                const used = (memory.usedJSHeapSize / 1024 / 1024).toFixed(2);
                const total = (memory.totalJSHeapSize / 1024 / 1024).toFixed(2);
                const limit = (memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2);
                
                logResult('performance', '内存使用', 'success', `已使用: ${used}MB / ${total}MB`, `内存限制: ${limit}MB`);
            } else {
                logResult('performance', '内存使用', 'warning', '浏览器不支持内存监控', '请使用Chrome浏览器进行内存测试');
            }
        }
        
        // 集成测试
        function runAllTests() {
            testResults = [];
            
            testStorageService();
            testLevelManager();
            testI18nService();
            testScreenManager();
            testCustomLevelsUI();
            testLevelDetailDialog();
            testResponsiveDesign();
            testMobileInteraction();
            testPerformance();
            testMemoryUsage();
            
            const successCount = testResults.filter(r => r.status === 'success').length;
            const warningCount = testResults.filter(r => r.status === 'warning').length;
            const errorCount = testResults.filter(r => r.status === 'error').length;
            
            logResult('integration', '综合测试', 'success', '所有测试完成', 
                `成功: ${successCount}, 警告: ${warningCount}, 错误: ${errorCount}`);
        }
        
        function generateTestReport() {
            const report = {
                timestamp: new Date().toISOString(),
                summary: {
                    total: testResults.length,
                    success: testResults.filter(r => r.status === 'success').length,
                    warning: testResults.filter(r => r.status === 'warning').length,
                    error: testResults.filter(r => r.status === 'error').length
                },
                details: testResults
            };
            
            const reportJson = JSON.stringify(report, null, 2);
            const blob = new Blob([reportJson], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `custom-levels-test-report-${Date.now()}.json`;
            a.click();
            
            URL.revokeObjectURL(url);
            
            logResult('integration', '测试报告', 'success', '报告已生成', `包含 ${testResults.length} 个测试结果`);
        }
    </script>
</body>
</html>
