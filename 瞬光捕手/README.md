# 瞬光捕手 (Split-Second Spark)

> 捕捉决定性瞬间，引燃无限可能

## 🎮 游戏简介

瞬光捕手是一款考验反应速度和时机把握的休闲游戏。玩家需要在屏幕上出现的光点达到最佳时机时精准点击，获得高分并解锁更多关卡。

### 🌟 核心玩法

- **精准时机**：光点会经历不同阶段，在完美时机点击可获得最高分数
- **连击系统**：连续成功捕捉光点可获得连击奖励
- **关卡进阶**：随着关卡提升，光点移动速度和数量会增加
- **技能挑战**：考验玩家的反应速度、专注力和时机判断

## 🚀 功能特性

### ✨ 核心功能
- 🎯 **精准捕捉**：多阶段光点系统（接近→完美→良好→消失）
- 🔥 **连击奖励**：连续成功可获得分数倍数奖励
- 📈 **难度递增**：关卡越高，挑战越大
- 💫 **视觉效果**：精美的光效和粒子系统

### 🎨 界面设计
- 📱 **响应式设计**：完美适配PC端和移动端
- 🌍 **多语言支持**：中文/英文切换
- 🎨 **精美UI**：现代化的渐变设计和动画效果
- 🌙 **深色主题**：护眼的深色配色方案

### 👤 玩家系统
- 👥 **多玩家支持**：创建和切换不同玩家账号
- 📊 **数据统计**：记录最高分、游戏次数、完美击中等
- 💾 **自动保存**：游戏进度和设置自动保存
- 🏆 **成就系统**：追踪玩家游戏成就

### 🛠️ 技术特性
- 🗄️ **智能存储**：支持IndexedDB/localStorage/内存存储
- ⚡ **高性能**：60FPS流畅游戏体验
- 🎮 **多输入支持**：鼠标、键盘、触摸、手柄
- 🔧 **错误处理**：完善的错误捕获和恢复机制

## 🎯 操作说明

### 🖱️ PC端操作
- **鼠标左键**：点击捕捉光点
- **空格键/回车键**：在画布中心捕捉光点
- **P键**：暂停游戏
- **ESC键**：暂停游戏或返回主菜单
- **F11键**：切换全屏模式

### 📱 移动端操作
- **触摸屏幕**：点击光点位置进行捕捉
- **触摸控制区域**：在画布中心进行捕捉
- **双击**：特殊功能（预留）
- **长按**：显示菜单（预留）

### 🎮 手柄操作
- **A按钮**：在画布中心捕捉光点
- **左摇杆**：移动光标（预留功能）

## 📁 项目结构

```
瞬光捕手/
├── index.html              # 主页面
├── styles/                 # 样式文件
│   ├── main.css           # 主样式
│   └── responsive.css     # 响应式样式
├── js/                    # JavaScript文件
│   ├── utils/             # 工具模块
│   │   ├── storage.js     # 存储服务
│   │   └── i18n.js        # 国际化服务
│   ├── core/              # 核心模块
│   │   ├── game-engine.js # 游戏引擎
│   │   ├── player-manager.js # 玩家管理
│   │   └── level-manager.js  # 关卡管理
│   ├── ui/                # 界面模块
│   │   ├── screen-manager.js # 屏幕管理
│   │   └── input-handler.js  # 输入处理
│   └── main.js            # 主程序入口
└── README.md              # 项目说明
```

## 🔧 技术架构

### 🏗️ 模块化设计
- **存储服务**：统一的KV存储接口，支持多种后端
- **国际化服务**：完整的多语言支持系统
- **玩家管理**：账号创建、切换、数据管理
- **游戏引擎**：核心游戏逻辑和渲染系统
- **屏幕管理**：界面切换和状态管理
- **输入处理**：统一的输入事件处理

### 💾 存储系统
```javascript
// 自动选择最佳存储方案
IndexedDB > localStorage > Memory Storage

// 统一接口
await storageService.put(key, value);
const data = await storageService.get(key);
await storageService.delete(key);
const keys = await storageService.list(prefix);
```

### 🌍 国际化系统
```javascript
// 多语言支持
i18nService.t('game.title');        // 瞬光捕手
i18nService.t('game.score');        // 得分
await i18nService.setLanguage('en-US');
```

## 🚀 快速开始

### 📋 环境要求
- 现代浏览器（Chrome 60+, Firefox 55+, Safari 12+, Edge 79+）
- 支持ES6+语法
- 支持Canvas 2D API
- 建议启用JavaScript

### 🔧 部署方式

#### 1. 本地运行
```bash
# 直接打开index.html文件
# 或使用本地服务器
python -m http.server 8000
# 访问 http://localhost:8000
```

#### 2. 静态部署
- **GitHub Pages**：上传到GitHub仓库，启用Pages
- **Vercel**：连接GitHub仓库，自动部署
- **Netlify**：拖拽文件夹到Netlify
- **其他静态托管**：上传所有文件到服务器

#### 3. CDN部署
```html
<!-- 可以将资源文件部署到CDN -->
<link rel="stylesheet" href="https://cdn.example.com/styles/main.css">
<script src="https://cdn.example.com/js/main.js"></script>
```

## 🎨 自定义配置

### 🎮 游戏参数调整
```javascript
// 在game-engine.js中修改关卡配置
this.levelConfig = {
    sparkSpawnRate: 2000,    // 光点生成间隔
    sparkSpeed: 1.0,         // 光点移动速度
    sparkCount: 1,           // 同时光点数量
    perfectWindow: 100,      // 完美时机窗口
    goodWindow: 200,         // 良好时机窗口
};
```

### 🎨 界面主题定制
```css
/* 在main.css中修改主题色彩 */
:root {
    --primary-color: #ff6b6b;
    --secondary-color: #4ecdc4;
    --background-gradient: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}
```

### 🌍 添加新语言
```javascript
// 在i18n.js中添加新语言
this.translations['ja-JP'] = {
    'game.title': 'スプリットセカンドスパーク',
    // ... 其他翻译
};
```

## 🐛 故障排除

### 常见问题

1. **游戏无法加载**
   - 检查浏览器控制台错误信息
   - 确认浏览器支持ES6语法
   - 尝试清除浏览器缓存

2. **数据无法保存**
   - 检查浏览器是否禁用了本地存储
   - 尝试在隐私模式下运行
   - 查看存储空间是否已满

3. **触摸操作无响应**
   - 确认设备支持触摸事件
   - 检查是否有其他元素遮挡
   - 尝试刷新页面

4. **性能问题**
   - 关闭其他占用资源的标签页
   - 检查设备硬件性能
   - 降低浏览器缩放比例

### 🔍 调试模式
```javascript
// 在浏览器控制台中启用调试
localStorage.setItem('debug', 'true');
location.reload();
```

## 📈 未来规划

### 🚧 开发中功能
- 🏗️ **关卡编辑器**：可视化关卡创建工具
- 🌐 **关卡分享**：自定义关卡分享和评分系统
- 🏆 **排行榜**：全球和好友排行榜
- 🎵 **音效系统**：背景音乐和音效支持
- 🎨 **主题系统**：多种视觉主题选择

### 💡 计划功能
- 🤖 **AI挑战**：与AI对战模式
- 👥 **多人模式**：实时对战功能
- 🎯 **每日挑战**：每日特殊关卡
- 🏅 **成就系统**：更多成就和奖励
- 📱 **PWA支持**：离线游戏和安装支持

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交问题和功能请求！如果您想贡献代码，请：

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📞 联系方式

- 项目链接：[https://github.com/yourusername/split-second-spark](https://github.com/yourusername/split-second-spark)
- 问题反馈：[Issues](https://github.com/yourusername/split-second-spark/issues)

---

**享受游戏，捕捉每一个决定性瞬间！** ✨
