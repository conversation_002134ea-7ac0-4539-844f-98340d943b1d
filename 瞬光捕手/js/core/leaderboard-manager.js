/**
 * 瞬光捕手 - 排行榜管理器
 * 负责排行榜数据的管理和展示
 */

class LeaderboardManager {
    constructor() {
        this.leaderboards = new Map();
        this.initialized = false;
        
        // 排行榜类型 - 分离官方和自定义关卡
        this.leaderboardTypes = {
            // 官方关卡排行榜
            OFFICIAL_GLOBAL_HIGH_SCORE: 'official_global_high_score',
            OFFICIAL_DAILY_HIGH_SCORE: 'official_daily_high_score',
            OFFICIAL_WEEKLY_HIGH_SCORE: 'official_weekly_high_score',
            OFFICIAL_MONTHLY_HIGH_SCORE: 'official_monthly_high_score',
            OFFICIAL_PERFECT_HITS: 'official_perfect_hits',
            OFFICIAL_COMBO_RECORD: 'official_combo_record',
            OFFICIAL_LEVEL_COMPLETION: 'official_level_completion',

            // 自定义关卡排行榜
            CUSTOM_GLOBAL_HIGH_SCORE: 'custom_global_high_score',
            CUSTOM_DAILY_HIGH_SCORE: 'custom_daily_high_score',
            CUSTOM_WEEKLY_HIGH_SCORE: 'custom_weekly_high_score',
            CUSTOM_MONTHLY_HIGH_SCORE: 'custom_monthly_high_score',
            CUSTOM_PERFECT_HITS: 'custom_perfect_hits',
            CUSTOM_COMBO_RECORD: 'custom_combo_record',

            // 认证自定义关卡排行榜（通过质量认证的自定义关卡）
            CERTIFIED_CUSTOM_HIGH_SCORE: 'certified_custom_high_score',
            CERTIFIED_CUSTOM_COMBO: 'certified_custom_combo',

            // 兼容旧版本的类型映射
            GLOBAL_HIGH_SCORE: 'official_global_high_score',
            DAILY_HIGH_SCORE: 'official_daily_high_score',
            WEEKLY_HIGH_SCORE: 'official_weekly_high_score',
            MONTHLY_HIGH_SCORE: 'official_monthly_high_score',
            PERFECT_HITS: 'official_perfect_hits',
            COMBO_RECORD: 'official_combo_record',
            LEVEL_COMPLETION: 'official_level_completion'
        };

        // 排行榜配置
        this.config = {
            maxEntries: 100,        // 每个排行榜最大条目数
            dailyResetHour: 0,      // 每日重置时间（小时）
            weeklyResetDay: 1,      // 每周重置日（1=周一）
            monthlyResetDay: 1,     // 每月重置日
            // 自定义关卡分数提交限制
            customLevelRestrictions: {
                minDifficulty: 0.3,         // 最低难度要求
                requireCertification: false, // 是否需要认证
                maxSubmissionsPerDay: 50    // 每日最大提交次数
            }
        };
    }

    /**
     * 初始化排行榜管理器
     */
    async init() {
        try {
            // 加载所有排行榜数据
            await this.loadLeaderboards();
            
            // 检查并重置过期的排行榜
            await this.checkAndResetExpiredLeaderboards();
            
            this.initialized = true;
            console.log('排行榜管理器初始化完成');
            
        } catch (error) {
            console.error('排行榜管理器初始化失败:', error);
            this.initialized = true;
        }
    }

    /**
     * 加载所有排行榜数据
     */
    async loadLeaderboards() {
        try {
            const leaderboardKeys = await storageService.list('leaderboard.');
            
            for (const key of leaderboardKeys) {
                if (key.startsWith('leaderboard.') && key.endsWith('.data')) {
                    const leaderboardData = await storageService.get(key);
                    if (leaderboardData && leaderboardData.type) {
                        this.leaderboards.set(leaderboardData.type, leaderboardData);
                    }
                }
            }
            
            console.log(`加载了 ${this.leaderboards.size} 个排行榜`);
        } catch (error) {
            console.error('加载排行榜数据失败:', error);
        }
    }

    /**
     * 检查并重置过期的排行榜
     */
    async checkAndResetExpiredLeaderboards() {
        const now = new Date();
        
        // 检查每日排行榜
        await this.checkDailyReset(now);
        
        // 检查每周排行榜
        await this.checkWeeklyReset(now);
        
        // 检查每月排行榜
        await this.checkMonthlyReset(now);
    }

    /**
     * 检查每日排行榜重置
     */
    async checkDailyReset(now) {
        const dailyLeaderboard = this.leaderboards.get(this.leaderboardTypes.DAILY_HIGH_SCORE);
        
        if (dailyLeaderboard) {
            const lastReset = new Date(dailyLeaderboard.lastReset);
            const shouldReset = now.getDate() !== lastReset.getDate() || 
                              now.getMonth() !== lastReset.getMonth() || 
                              now.getFullYear() !== lastReset.getFullYear();
            
            if (shouldReset) {
                await this.resetLeaderboard(this.leaderboardTypes.DAILY_HIGH_SCORE);
                console.log('每日排行榜已重置');
            }
        }
    }

    /**
     * 检查每周排行榜重置
     */
    async checkWeeklyReset(now) {
        const weeklyLeaderboard = this.leaderboards.get(this.leaderboardTypes.WEEKLY_HIGH_SCORE);
        
        if (weeklyLeaderboard) {
            const lastReset = new Date(weeklyLeaderboard.lastReset);
            const currentWeek = this.getWeekNumber(now);
            const lastResetWeek = this.getWeekNumber(lastReset);
            
            if (currentWeek !== lastResetWeek || now.getFullYear() !== lastReset.getFullYear()) {
                await this.resetLeaderboard(this.leaderboardTypes.WEEKLY_HIGH_SCORE);
                console.log('每周排行榜已重置');
            }
        }
    }

    /**
     * 检查每月排行榜重置
     */
    async checkMonthlyReset(now) {
        const monthlyLeaderboard = this.leaderboards.get(this.leaderboardTypes.MONTHLY_HIGH_SCORE);
        
        if (monthlyLeaderboard) {
            const lastReset = new Date(monthlyLeaderboard.lastReset);
            
            if (now.getMonth() !== lastReset.getMonth() || now.getFullYear() !== lastReset.getFullYear()) {
                await this.resetLeaderboard(this.leaderboardTypes.MONTHLY_HIGH_SCORE);
                console.log('每月排行榜已重置');
            }
        }
    }

    /**
     * 提交分数到排行榜
     * @param {string} type - 排行榜类型
     * @param {object} scoreData - 分数数据
     * @param {object} levelData - 关卡数据（可选）
     * @param {object} gameData - 游戏数据（可选）
     */
    async submitScore(type, scoreData, levelData = null, gameData = null) {
        try {
            const currentPlayer = playerManager.getCurrentPlayer();
            if (currentPlayer.isGuest) {
                console.log('游客无法提交分数到排行榜');
                return { success: false, reason: 'guest_not_allowed' };
            }

            // 防作弊验证
            if (window.antiCheatSystem && window.antiCheatSystem.initialized) {
                const validation = antiCheatSystem.validateScoreSubmission(scoreData, gameData, levelData);

                if (!validation.isValid) {
                    console.warn('分数验证失败:', validation.errors);
                    return { success: false, reason: 'validation_failed', details: validation };
                }

                if (validation.riskLevel === 'high' || validation.riskLevel === 'critical') {
                    console.warn('分数风险等级过高:', validation.riskLevel);
                    return { success: false, reason: 'high_risk', details: validation };
                }
            }

            // 确定正确的排行榜类型
            const actualType = this.determineLeaderboardType(type, levelData);

            // 检查自定义关卡提交限制
            if (this.isCustomLevelType(actualType)) {
                const canSubmit = await this.checkCustomLevelSubmissionLimits(currentPlayer.id, levelData);
                if (!canSubmit.allowed) {
                    console.warn('自定义关卡分数提交被限制:', canSubmit.reason);
                    return { success: false, reason: 'custom_level_restricted', details: canSubmit };
                }
            }

            let leaderboard = this.leaderboards.get(actualType);

            // 如果排行榜不存在，创建新的
            if (!leaderboard) {
                leaderboard = this.createLeaderboard(actualType);
                this.leaderboards.set(actualType, leaderboard);
            }

            const entry = {
                playerId: currentPlayer.id,
                playerName: currentPlayer.name,
                score: scoreData.score,
                level: scoreData.level || 1,
                perfectHits: scoreData.perfectHits || 0,
                combo: scoreData.combo || 0,
                timestamp: Date.now(),
                gameData: {
                    duration: scoreData.duration || 0,
                    accuracy: scoreData.accuracy || 0,
                    totalHits: scoreData.totalHits || 0
                },
                // 添加关卡信息
                levelInfo: levelData ? {
                    id: levelData.id,
                    name: levelData.name,
                    isCustom: this.isCustomLevel(levelData),
                    difficulty: levelData.difficulty || 'normal'
                } : null
            };

            // 检查是否是玩家的新记录
            const existingEntryIndex = leaderboard.entries.findIndex(e => e.playerId === currentPlayer.id);

            if (existingEntryIndex >= 0) {
                // 如果新分数更高，更新记录
                if (entry.score > leaderboard.entries[existingEntryIndex].score) {
                    leaderboard.entries[existingEntryIndex] = entry;
                } else {
                    return { success: false, reason: 'score_not_improved' };
                }
            } else {
                // 添加新记录
                leaderboard.entries.push(entry);
            }

            // 按分数排序
            leaderboard.entries.sort((a, b) => b.score - a.score);

            // 限制条目数量
            if (leaderboard.entries.length > this.config.maxEntries) {
                leaderboard.entries = leaderboard.entries.slice(0, this.config.maxEntries);
            }

            // 更新排行榜信息
            leaderboard.lastUpdated = Date.now();
            leaderboard.totalEntries = leaderboard.entries.length;

            // 保存排行榜数据
            await this.saveLeaderboard(leaderboard);

            console.log(`✅ 分数已提交到排行榜: ${actualType}, 分数: ${entry.score}`);
            return { success: true, type: actualType, rank: this.getPlayerRank(actualType, currentPlayer.id)?.rank || -1 };

        } catch (error) {
            console.error('❌ 提交分数失败:', error);
            return { success: false, reason: 'submission_error', error: error.message };
        }
    }

    /**
     * 获取排行榜数据
     * @param {string} type - 排行榜类型
     * @param {number} limit - 返回条目数量限制
     * @returns {object|null} 排行榜数据
     */
    getLeaderboard(type, limit = 10) {
        const leaderboard = this.leaderboards.get(type);
        
        if (!leaderboard) {
            return null;
        }

        return {
            type: leaderboard.type,
            title: leaderboard.title,
            lastUpdated: leaderboard.lastUpdated,
            lastReset: leaderboard.lastReset,
            totalEntries: leaderboard.totalEntries,
            entries: leaderboard.entries.slice(0, limit)
        };
    }

    /**
     * 获取玩家在排行榜中的排名
     * @param {string} type - 排行榜类型
     * @param {string} playerId - 玩家ID
     * @returns {object|null} 排名信息
     */
    getPlayerRank(type, playerId) {
        const leaderboard = this.leaderboards.get(type);
        
        if (!leaderboard) {
            return null;
        }

        const entryIndex = leaderboard.entries.findIndex(e => e.playerId === playerId);
        
        if (entryIndex === -1) {
            return null;
        }

        return {
            rank: entryIndex + 1,
            entry: leaderboard.entries[entryIndex],
            totalEntries: leaderboard.totalEntries
        };
    }

    /**
     * 获取所有可用的排行榜类型
     * @returns {Array} 排行榜类型列表
     */
    getAvailableLeaderboards() {
        return Object.values(this.leaderboardTypes).map(type => {
            const leaderboard = this.leaderboards.get(type);
            return {
                type: type,
                title: this.getLeaderboardTitle(type),
                hasData: !!leaderboard && leaderboard.entries.length > 0,
                lastUpdated: leaderboard ? leaderboard.lastUpdated : null
            };
        });
    }

    /**
     * 创建新的排行榜
     * @param {string} type - 排行榜类型
     * @returns {object} 排行榜对象
     */
    createLeaderboard(type) {
        const now = Date.now();
        
        return {
            type: type,
            title: this.getLeaderboardTitle(type),
            entries: [],
            createdAt: now,
            lastUpdated: now,
            lastReset: now,
            totalEntries: 0
        };
    }

    /**
     * 重置排行榜
     * @param {string} type - 排行榜类型
     */
    async resetLeaderboard(type) {
        try {
            const leaderboard = this.leaderboards.get(type);
            
            if (leaderboard) {
                // 备份当前排行榜（可选）
                await this.backupLeaderboard(leaderboard);
                
                // 重置排行榜
                leaderboard.entries = [];
                leaderboard.lastReset = Date.now();
                leaderboard.lastUpdated = Date.now();
                leaderboard.totalEntries = 0;
                
                await this.saveLeaderboard(leaderboard);
            }
        } catch (error) {
            console.error('重置排行榜失败:', error);
        }
    }

    /**
     * 备份排行榜数据
     * @param {object} leaderboard - 排行榜对象
     */
    async backupLeaderboard(leaderboard) {
        try {
            const backupKey = `leaderboard.backup.${leaderboard.type}.${Date.now()}`;
            await storageService.put(backupKey, leaderboard);
        } catch (error) {
            console.error('备份排行榜失败:', error);
        }
    }

    /**
     * 保存排行榜数据
     * @param {object} leaderboard - 排行榜对象
     */
    async saveLeaderboard(leaderboard) {
        const key = `leaderboard.${leaderboard.type}.data`;
        await storageService.put(key, leaderboard);
    }

    /**
     * 获取排行榜标题
     * @param {string} type - 排行榜类型
     * @returns {string} 标题
     */
    getLeaderboardTitle(type) {
        const titleMap = {
            [this.leaderboardTypes.GLOBAL_HIGH_SCORE]: i18nService.t('leaderboard.global'),
            [this.leaderboardTypes.DAILY_HIGH_SCORE]: i18nService.t('leaderboard.daily'),
            [this.leaderboardTypes.WEEKLY_HIGH_SCORE]: i18nService.t('leaderboard.weekly'),
            [this.leaderboardTypes.MONTHLY_HIGH_SCORE]: i18nService.t('leaderboard.monthly'),
            [this.leaderboardTypes.PERFECT_HITS]: i18nService.t('leaderboard.perfectHits'),
            [this.leaderboardTypes.COMBO_RECORD]: i18nService.t('leaderboard.combo'),
            [this.leaderboardTypes.LEVEL_COMPLETION]: i18nService.t('leaderboard.level')
        };
        
        return titleMap[type] || type;
    }

    /**
     * 获取周数
     * @param {Date} date - 日期
     * @returns {number} 周数
     */
    getWeekNumber(date) {
        const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
        const dayNum = d.getUTCDay() || 7;
        d.setUTCDate(d.getUTCDate() + 4 - dayNum);
        const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
        return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
    }

    /**
     * 格式化分数显示
     * @param {number} score - 分数
     * @returns {string} 格式化后的分数
     */
    formatScore(score) {
        if (score >= 1000000) {
            return (score / 1000000).toFixed(1) + 'M';
        } else if (score >= 1000) {
            return (score / 1000).toFixed(1) + 'K';
        } else {
            return score.toString();
        }
    }

    /**
     * 格式化时间显示
     * @param {number} timestamp - 时间戳
     * @returns {string} 格式化后的时间
     */
    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) { // 1分钟内
            return i18nService.t('time.justNow');
        } else if (diff < 3600000) { // 1小时内
            const minutes = Math.floor(diff / 60000);
            return i18nService.t('time.minutesAgo', { minutes });
        } else if (diff < 86400000) { // 1天内
            const hours = Math.floor(diff / 3600000);
            return i18nService.t('time.hoursAgo', { hours });
        } else {
            return date.toLocaleDateString();
        }
    }

    /**
     * 清理过期的备份数据
     */
    async cleanupOldBackups() {
        try {
            const backupKeys = await storageService.list('leaderboard.backup.');
            const oneMonthAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);

            for (const key of backupKeys) {
                const parts = key.split('.');
                if (parts.length >= 4) {
                    const timestamp = parseInt(parts[3]);
                    if (timestamp < oneMonthAgo) {
                        await storageService.delete(key);
                    }
                }
            }
        } catch (error) {
            console.error('清理备份数据失败:', error);
        }
    }

    /**
     * 确定正确的排行榜类型
     * @param {string} requestedType - 请求的排行榜类型
     * @param {object} levelData - 关卡数据
     * @returns {string} 实际的排行榜类型
     */
    determineLeaderboardType(requestedType, levelData) {
        // 如果没有关卡数据，使用官方排行榜
        if (!levelData) {
            return this.mapToOfficialType(requestedType);
        }

        // 检查是否是自定义关卡
        if (this.isCustomLevel(levelData)) {
            // 检查是否是认证关卡
            if (levelData.certified) {
                return this.mapToCertifiedType(requestedType);
            } else {
                return this.mapToCustomType(requestedType);
            }
        } else {
            return this.mapToOfficialType(requestedType);
        }
    }

    /**
     * 检查是否是自定义关卡
     * @param {object} levelData - 关卡数据
     * @returns {boolean} 是否是自定义关卡
     */
    isCustomLevel(levelData) {
        if (!levelData) return false;

        // 检查关卡ID前缀
        if (levelData.id && levelData.id.startsWith('level_')) {
            return true;
        }

        // 检查是否有作者信息
        if (levelData.author || levelData.authorId) {
            return true;
        }

        // 检查是否标记为自定义
        if (levelData.isCustom === true) {
            return true;
        }

        return false;
    }

    /**
     * 检查排行榜类型是否为自定义关卡类型
     * @param {string} type - 排行榜类型
     * @returns {boolean} 是否是自定义关卡类型
     */
    isCustomLevelType(type) {
        return type.startsWith('custom_') || type.startsWith('certified_custom_');
    }

    /**
     * 映射到官方排行榜类型
     * @param {string} type - 原始类型
     * @returns {string} 官方排行榜类型
     */
    mapToOfficialType(type) {
        const mapping = {
            'global_high_score': this.leaderboardTypes.OFFICIAL_GLOBAL_HIGH_SCORE,
            'daily_high_score': this.leaderboardTypes.OFFICIAL_DAILY_HIGH_SCORE,
            'weekly_high_score': this.leaderboardTypes.OFFICIAL_WEEKLY_HIGH_SCORE,
            'monthly_high_score': this.leaderboardTypes.OFFICIAL_MONTHLY_HIGH_SCORE,
            'perfect_hits': this.leaderboardTypes.OFFICIAL_PERFECT_HITS,
            'combo_record': this.leaderboardTypes.OFFICIAL_COMBO_RECORD,
            'level_completion': this.leaderboardTypes.OFFICIAL_LEVEL_COMPLETION
        };

        return mapping[type] || this.leaderboardTypes.OFFICIAL_GLOBAL_HIGH_SCORE;
    }

    /**
     * 映射到自定义关卡排行榜类型
     * @param {string} type - 原始类型
     * @returns {string} 自定义关卡排行榜类型
     */
    mapToCustomType(type) {
        const mapping = {
            'global_high_score': this.leaderboardTypes.CUSTOM_GLOBAL_HIGH_SCORE,
            'daily_high_score': this.leaderboardTypes.CUSTOM_DAILY_HIGH_SCORE,
            'weekly_high_score': this.leaderboardTypes.CUSTOM_WEEKLY_HIGH_SCORE,
            'monthly_high_score': this.leaderboardTypes.CUSTOM_MONTHLY_HIGH_SCORE,
            'perfect_hits': this.leaderboardTypes.CUSTOM_PERFECT_HITS,
            'combo_record': this.leaderboardTypes.CUSTOM_COMBO_RECORD
        };

        return mapping[type] || this.leaderboardTypes.CUSTOM_GLOBAL_HIGH_SCORE;
    }

    /**
     * 映射到认证自定义关卡排行榜类型
     * @param {string} type - 原始类型
     * @returns {string} 认证自定义关卡排行榜类型
     */
    mapToCertifiedType(type) {
        const mapping = {
            'global_high_score': this.leaderboardTypes.CERTIFIED_CUSTOM_HIGH_SCORE,
            'combo_record': this.leaderboardTypes.CERTIFIED_CUSTOM_COMBO
        };

        return mapping[type] || this.leaderboardTypes.CERTIFIED_CUSTOM_HIGH_SCORE;
    }

    /**
     * 检查自定义关卡分数提交限制
     * @param {string} playerId - 玩家ID
     * @param {object} levelData - 关卡数据
     * @returns {object} 检查结果
     */
    async checkCustomLevelSubmissionLimits(playerId, levelData) {
        try {
            const restrictions = this.config.customLevelRestrictions;

            // 检查关卡难度
            if (window.antiCheatSystem && levelData) {
                const difficulty = antiCheatSystem.calculateLevelDifficulty(levelData);
                if (difficulty < restrictions.minDifficulty) {
                    return {
                        allowed: false,
                        reason: 'level_too_easy',
                        details: { difficulty, minRequired: restrictions.minDifficulty }
                    };
                }
            }

            // 检查是否需要认证
            if (restrictions.requireCertification && !levelData.certified) {
                return {
                    allowed: false,
                    reason: 'level_not_certified',
                    details: { requireCertification: true }
                };
            }

            // 检查每日提交次数限制
            const today = new Date().toDateString();
            const submissionKey = `custom_submissions_${playerId}_${today}`;
            const todaySubmissions = await storageService.get(submissionKey) || 0;

            if (todaySubmissions >= restrictions.maxSubmissionsPerDay) {
                return {
                    allowed: false,
                    reason: 'daily_limit_exceeded',
                    details: {
                        submissions: todaySubmissions,
                        limit: restrictions.maxSubmissionsPerDay
                    }
                };
            }

            // 更新提交计数
            await storageService.put(submissionKey, todaySubmissions + 1);

            return { allowed: true };

        } catch (error) {
            console.error('检查自定义关卡提交限制失败:', error);
            return { allowed: true }; // 出错时允许提交
        }
    }
}

// 创建全局排行榜管理器实例
window.leaderboardManager = new LeaderboardManager();
