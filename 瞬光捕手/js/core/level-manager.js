/**
 * 瞬光捕手 - 关卡管理器
 * 负责关卡数据的管理、自定义关卡的创建和分享
 */

class LevelManager {
    constructor() {
        this.currentLevel = null;
        this.customLevels = new Map();
        this.levelRatings = new Map();
        this.initialized = false;
    }

    /**
     * 初始化关卡管理器
     */
    async init() {
        try {
            // 加载自定义关卡
            await this.loadCustomLevels();
            
            // 加载关卡评分数据
            await this.loadLevelRatings();
            
            this.initialized = true;
            console.log('关卡管理器初始化完成');
            
        } catch (error) {
            console.error('关卡管理器初始化失败:', error);
            this.initialized = true;
        }
    }

    /**
     * 加载自定义关卡
     */
    async loadCustomLevels() {
        try {
            const levelKeys = await storageService.list('level.');
            
            for (const key of levelKeys) {
                if (key.startsWith('level.custom.') && key.endsWith('.data')) {
                    const levelData = await storageService.get(key);
                    if (levelData && levelData.id) {
                        this.customLevels.set(levelData.id, levelData);
                    }
                }
            }
            
            console.log(`加载了 ${this.customLevels.size} 个自定义关卡`);
        } catch (error) {
            console.error('加载自定义关卡失败:', error);
        }
    }

    /**
     * 加载关卡评分数据
     */
    async loadLevelRatings() {
        try {
            const ratingKeys = await storageService.list('rating.');
            
            for (const key of ratingKeys) {
                if (key.startsWith('rating.level.')) {
                    const ratingData = await storageService.get(key);
                    if (ratingData) {
                        const levelId = key.replace('rating.level.', '');
                        this.levelRatings.set(levelId, ratingData);
                    }
                }
            }
            
            console.log(`加载了 ${this.levelRatings.size} 个关卡评分`);
        } catch (error) {
            console.error('加载关卡评分失败:', error);
        }
    }

    /**
     * 创建新的自定义关卡
     * @param {object} levelData - 关卡数据
     * @returns {Promise<string|null>} 关卡ID或null（如果失败）
     */
    async createCustomLevel(levelData) {
        try {
            const levelId = this.generateLevelId();
            const currentPlayer = playerManager.getCurrentPlayer();
            
            const level = {
                id: levelId,
                name: levelData.name || '未命名关卡',
                description: levelData.description || '',
                author: currentPlayer.name,
                authorId: currentPlayer.id,
                difficulty: levelData.difficulty || 'normal',
                createdAt: Date.now(),
                updatedAt: Date.now(),
                published: false,
                playCount: 0,
                rating: {
                    likes: 0,
                    dislikes: 0,
                    totalRating: 0
                },
                data: {
                    sparks: levelData.sparks || [],
                    duration: levelData.duration || 30000,
                    targetScore: levelData.targetScore || 1000,
                    specialEffects: levelData.specialEffects || []
                }
            };

            // 保存关卡数据
            await this.saveLevelData(level);
            
            // 添加到内存中
            this.customLevels.set(levelId, level);
            
            // 更新玩家统计
            if (!currentPlayer.isGuest) {
                await playerManager.updatePlayerStats({
                    customLevelsCreated: (currentPlayer.stats.customLevelsCreated || 0) + 1
                });
            }
            
            console.log(`创建自定义关卡: ${level.name} (ID: ${levelId})`);
            return levelId;
            
        } catch (error) {
            console.error('创建自定义关卡失败:', error);
            throw new Error(i18nService.t('error.levelSaveFailed'));
        }
    }

    /**
     * 更新自定义关卡
     * @param {string} levelId - 关卡ID
     * @param {object} updateData - 更新数据
     * @returns {Promise<boolean>} 是否成功
     */
    async updateCustomLevel(levelId, updateData) {
        try {
            const level = this.customLevels.get(levelId);
            if (!level) {
                throw new Error(i18nService.t('error.levelNotFound'));
            }

            const currentPlayer = playerManager.getCurrentPlayer();
            
            // 检查权限（只有作者可以编辑）
            if (level.authorId !== currentPlayer.id && !currentPlayer.isGuest) {
                throw new Error('没有权限编辑此关卡');
            }

            // 更新数据
            Object.assign(level, updateData);
            level.updatedAt = Date.now();

            // 保存数据
            await this.saveLevelData(level);
            
            console.log(`更新自定义关卡: ${level.name}`);
            return true;
            
        } catch (error) {
            console.error('更新自定义关卡失败:', error);
            return false;
        }
    }

    /**
     * 删除自定义关卡
     * @param {string} levelId - 关卡ID
     * @returns {Promise<boolean>} 是否成功
     */
    async deleteCustomLevel(levelId) {
        try {
            const level = this.customLevels.get(levelId);
            if (!level) {
                return false;
            }

            const currentPlayer = playerManager.getCurrentPlayer();
            
            // 检查权限（只有作者可以删除）
            if (level.authorId !== currentPlayer.id && !currentPlayer.isGuest) {
                return false;
            }

            // 删除关卡数据
            await storageService.delete(`level.custom.${levelId}.data`);
            
            // 删除评分数据
            await storageService.delete(`rating.level.${levelId}`);
            
            // 从内存中移除
            this.customLevels.delete(levelId);
            this.levelRatings.delete(levelId);
            
            console.log(`删除自定义关卡: ${level.name}`);
            return true;
            
        } catch (error) {
            console.error('删除自定义关卡失败:', error);
            return false;
        }
    }

    /**
     * 发布关卡
     * @param {string} levelId - 关卡ID
     * @returns {Promise<boolean>} 是否成功
     */
    async publishLevel(levelId) {
        try {
            const level = this.customLevels.get(levelId);
            if (!level) {
                return false;
            }

            level.published = true;
            level.updatedAt = Date.now();
            
            await this.saveLevelData(level);
            
            console.log(`发布关卡: ${level.name}`);
            return true;
            
        } catch (error) {
            console.error('发布关卡失败:', error);
            return false;
        }
    }

    /**
     * 对关卡进行评分
     * @param {string} levelId - 关卡ID
     * @param {boolean} isLike - 是否点赞（true为点赞，false为踩）
     * @returns {Promise<boolean>} 是否成功
     */
    async rateLevelLevel(levelId, isLike) {
        try {
            const level = this.customLevels.get(levelId);
            if (!level) {
                return false;
            }

            const currentPlayer = playerManager.getCurrentPlayer();
            if (currentPlayer.isGuest) {
                return false; // 游客不能评分
            }

            const ratingKey = `rating.level.${levelId}`;
            let ratingData = this.levelRatings.get(levelId) || {
                levelId: levelId,
                ratings: {},
                totalLikes: 0,
                totalDislikes: 0
            };

            const playerId = currentPlayer.id;
            const previousRating = ratingData.ratings[playerId];

            // 移除之前的评分
            if (previousRating === true) {
                ratingData.totalLikes--;
            } else if (previousRating === false) {
                ratingData.totalDislikes--;
            }

            // 添加新评分
            if (previousRating === isLike) {
                // 如果点击相同的按钮，则取消评分
                delete ratingData.ratings[playerId];
            } else {
                // 添加新评分
                ratingData.ratings[playerId] = isLike;
                if (isLike) {
                    ratingData.totalLikes++;
                } else {
                    ratingData.totalDislikes++;
                }
            }

            // 更新关卡评分
            level.rating.likes = ratingData.totalLikes;
            level.rating.dislikes = ratingData.totalDislikes;
            level.rating.totalRating = ratingData.totalLikes - ratingData.totalDislikes;

            // 保存数据
            await storageService.put(ratingKey, ratingData);
            await this.saveLevelData(level);
            
            // 更新内存
            this.levelRatings.set(levelId, ratingData);
            
            return true;
            
        } catch (error) {
            console.error('关卡评分失败:', error);
            return false;
        }
    }

    /**
     * 增加关卡游玩次数
     * @param {string} levelId - 关卡ID
     */
    async incrementPlayCount(levelId) {
        try {
            const level = this.customLevels.get(levelId);
            if (level) {
                level.playCount++;
                await this.saveLevelData(level);
            }
        } catch (error) {
            console.error('更新游玩次数失败:', error);
        }
    }

    /**
     * 获取自定义关卡列表
     * @param {string} sortBy - 排序方式 ('rating', 'playCount', 'date', 'difficulty')
     * @param {string} filterBy - 过滤条件 ('all', 'my', 'published')
     * @returns {Array} 关卡列表
     */
    getCustomLevels(sortBy = 'rating', filterBy = 'published') {
        let levels = Array.from(this.customLevels.values());
        
        // 过滤
        const currentPlayer = playerManager.getCurrentPlayer();
        switch (filterBy) {
            case 'my':
                levels = levels.filter(level => level.authorId === currentPlayer.id);
                break;
            case 'published':
                levels = levels.filter(level => level.published);
                break;
            case 'all':
            default:
                // 不过滤
                break;
        }
        
        // 排序
        switch (sortBy) {
            case 'rating':
                levels.sort((a, b) => {
                    // 首先按评分排序
                    const ratingDiff = b.rating.totalRating - a.rating.totalRating;
                    if (ratingDiff !== 0) return ratingDiff;
                    
                    // 评分相同时按难度排序
                    const difficultyOrder = { 'easy': 1, 'normal': 2, 'hard': 3, 'expert': 4 };
                    return difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty];
                });
                break;
            case 'playCount':
                levels.sort((a, b) => b.playCount - a.playCount);
                break;
            case 'date':
                levels.sort((a, b) => b.createdAt - a.createdAt);
                break;
            case 'difficulty':
                const difficultyOrder = { 'easy': 1, 'normal': 2, 'hard': 3, 'expert': 4 };
                levels.sort((a, b) => difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty]);
                break;
        }
        
        return levels;
    }

    /**
     * 获取关卡详情
     * @param {string} levelId - 关卡ID
     * @returns {object|null} 关卡数据
     */
    getLevel(levelId) {
        return this.customLevels.get(levelId) || null;
    }

    /**
     * 获取玩家对关卡的评分
     * @param {string} levelId - 关卡ID
     * @returns {boolean|null} 评分（true为点赞，false为踩，null为未评分）
     */
    getPlayerRating(levelId) {
        const currentPlayer = playerManager.getCurrentPlayer();
        if (currentPlayer.isGuest) {
            return null;
        }

        const ratingData = this.levelRatings.get(levelId);
        if (!ratingData) {
            return null;
        }

        return ratingData.ratings[currentPlayer.id] || null;
    }

    /**
     * 保存关卡数据
     * @param {object} level - 关卡对象
     */
    async saveLevelData(level) {
        const key = `level.custom.${level.id}.data`;
        await storageService.put(key, level);
    }

    /**
     * 生成唯一的关卡ID
     * @returns {string} 关卡ID
     */
    generateLevelId() {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substr(2, 5);
        return `level_${timestamp}_${random}`;
    }

    /**
     * 验证关卡数据
     * @param {object} levelData - 关卡数据
     * @returns {boolean} 是否有效
     */
    validateLevelData(levelData) {
        if (!levelData.name || levelData.name.trim().length === 0) {
            return false;
        }
        
        if (!levelData.sparks || !Array.isArray(levelData.sparks)) {
            return false;
        }
        
        if (!levelData.duration || levelData.duration < 1000) {
            return false;
        }
        
        return true;
    }

    /**
     * 获取难度显示文本
     * @param {string} difficulty - 难度级别
     * @returns {string} 显示文本
     */
    getDifficultyText(difficulty) {
        const difficultyMap = {
            'easy': i18nService.t('editor.difficulty.easy'),
            'normal': i18nService.t('editor.difficulty.normal'),
            'hard': i18nService.t('editor.difficulty.hard'),
            'expert': i18nService.t('editor.difficulty.expert')
        };
        
        return difficultyMap[difficulty] || difficultyMap['normal'];
    }

    /**
     * 获取难度颜色
     * @param {string} difficulty - 难度级别
     * @returns {string} 颜色代码
     */
    getDifficultyColor(difficulty) {
        const colorMap = {
            'easy': '#4CAF50',
            'normal': '#2196F3',
            'hard': '#FF9800',
            'expert': '#F44336'
        };

        return colorMap[difficulty] || colorMap['normal'];
    }

    /**
     * 获取所有关卡（包括自定义关卡）
     * @returns {Array} 关卡列表
     */
    getAllLevels() {
        return Array.from(this.customLevels.values());
    }

    /**
     * 加载关卡数据到编辑器
     * @param {object} levelData - 关卡数据
     * @returns {Promise<boolean>} 是否成功
     */
    async loadLevelData(levelData) {
        try {
            if (!levelEditor.initialized) {
                await levelEditor.init();
            }

            // 将关卡数据转换为编辑器格式
            const editorLevel = {
                id: levelData.id,
                name: levelData.name,
                description: levelData.description,
                difficulty: levelData.difficulty,
                author: levelData.author,
                createdAt: levelData.createdAt,
                modifiedAt: levelData.updatedAt || levelData.modifiedAt || Date.now(),
                version: '1.0',
                objects: levelData.objects || [],
                settings: {
                    timeLimit: levelData.data?.duration ? Math.floor(levelData.data.duration / 1000) : 60,
                    targetScore: levelData.data?.targetScore || 1000,
                    backgroundColor: levelData.data?.backgroundColor || '#1a1a2e',
                    music: 'default'
                }
            };

            // 如果有旧格式的光点数据，转换为新格式
            if (levelData.data && levelData.data.sparks && levelData.data.sparks.length > 0) {
                levelData.data.sparks.forEach((spark, index) => {
                    editorLevel.objects.push({
                        id: `spark_${index}`,
                        type: 'spark',
                        subType: spark.type || 'normal',
                        x: spark.x || 0,
                        y: spark.y || 0,
                        properties: {
                            points: spark.points || 10,
                            lifetime: spark.lifetime || 3000
                        }
                    });
                });
            }

            // 加载到编辑器
            levelEditor.currentLevel = editorLevel;
            levelEditor.clearHistory();
            levelEditor.saveToHistory();
            levelEditor.updateLevelSettingsUI();
            levelEditor.render();

            console.log('关卡数据加载到编辑器成功:', editorLevel.name);
            return true;

        } catch (error) {
            console.error('加载关卡数据到编辑器失败:', error);
            return false;
        }
    }

    /**
     * 从编辑器数据创建关卡
     * @param {object} editorLevel - 编辑器关卡数据
     * @returns {object} 关卡数据
     */
    createLevelFromEditor(editorLevel) {
        const currentPlayer = playerManager.getCurrentPlayer();

        return {
            id: editorLevel.id,
            name: editorLevel.name,
            description: editorLevel.description,
            author: currentPlayer.name,
            authorId: currentPlayer.id,
            difficulty: editorLevel.difficulty,
            createdAt: editorLevel.createdAt || Date.now(),
            updatedAt: Date.now(),
            published: false,
            playCount: 0,
            rating: {
                likes: 0,
                dislikes: 0,
                totalRating: 0
            },
            objects: editorLevel.objects || [],
            data: {
                duration: (editorLevel.settings?.timeLimit || 60) * 1000,
                targetScore: editorLevel.settings?.targetScore || 1000,
                backgroundColor: editorLevel.settings?.backgroundColor || '#1a1a2e',
                sparks: this.convertObjectsToSparks(editorLevel.objects || []),
                specialEffects: []
            }
        };
    }

    /**
     * 将对象数组转换为光点数组（兼容旧格式）
     * @param {Array} objects - 对象数组
     * @returns {Array} 光点数组
     */
    convertObjectsToSparks(objects) {
        return objects
            .filter(obj => obj.type === 'spark')
            .map(obj => ({
                x: obj.x,
                y: obj.y,
                type: obj.subType || 'normal',
                points: obj.properties?.points || 10,
                lifetime: obj.properties?.lifetime || 3000
            }));
    }

    /**
     * 检查关卡是否有效
     * @param {object} level - 关卡数据
     * @returns {object} 验证结果
     */
    validateLevel(level) {
        const errors = [];
        const warnings = [];

        // 检查基本信息
        if (!level.name || level.name.trim().length === 0) {
            errors.push('关卡名称不能为空');
        }

        if (level.name && level.name.length > 50) {
            warnings.push('关卡名称过长，建议不超过50个字符');
        }

        if (level.description && level.description.length > 200) {
            warnings.push('关卡描述过长，建议不超过200个字符');
        }

        // 检查对象数量
        const objects = level.objects || [];
        const sparkCount = objects.filter(obj => obj.type === 'spark').length;

        if (sparkCount === 0) {
            errors.push('关卡必须至少包含一个光点');
        }

        if (sparkCount > 100) {
            warnings.push('光点数量过多，可能影响游戏性能');
        }

        // 检查时间限制
        const timeLimit = level.data?.duration || 60000;
        if (timeLimit < 5000) {
            warnings.push('时间限制过短，可能导致关卡过于困难');
        }

        if (timeLimit > 300000) {
            warnings.push('时间限制过长，可能影响游戏体验');
        }

        // 检查目标分数
        const targetScore = level.data?.targetScore || 1000;
        if (targetScore < 100) {
            warnings.push('目标分数过低');
        }

        if (targetScore > sparkCount * 100) {
            warnings.push('目标分数可能过高，难以达成');
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
}

// 创建全局关卡管理器实例
window.levelManager = new LevelManager();
