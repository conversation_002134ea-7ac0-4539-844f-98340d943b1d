/**
 * 防作弊系统初始化脚本
 * 负责初始化和配置所有防作弊相关组件
 */

(function() {
    'use strict';

    /**
     * 防作弊系统初始化管理器
     */
    class AntiCheatInitializer {
        constructor() {
            this.initialized = false;
            this.initializationPromise = null;
            this.components = {
                antiCheatSystem: false,
                levelCertificationSystem: false,
                leaderboardManager: false
            };
            
            console.log('🛡️ 防作弊系统初始化器已创建');
        }

        /**
         * 初始化所有防作弊组件
         */
        async initialize() {
            if (this.initialized) {
                return true;
            }

            if (this.initializationPromise) {
                return this.initializationPromise;
            }

            this.initializationPromise = this._performInitialization();
            return this.initializationPromise;
        }

        /**
         * 执行初始化过程
         */
        async _performInitialization() {
            try {
                console.log('🛡️ 开始初始化防作弊系统...');

                // 1. 等待必要的依赖加载
                await this.waitForDependencies();

                // 2. 初始化防作弊核心系统
                await this.initializeAntiCheatSystem();

                // 3. 初始化关卡认证系统
                await this.initializeLevelCertificationSystem();

                // 4. 确保排行榜管理器已初始化
                await this.ensureLeaderboardManagerInitialized();

                // 5. 设置系统集成
                this.setupSystemIntegration();

                // 6. 验证系统完整性
                await this.validateSystemIntegrity();

                this.initialized = true;
                console.log('✅ 防作弊系统初始化完成');

                // 触发初始化完成事件
                this.dispatchInitializationEvent();

                return true;

            } catch (error) {
                console.error('❌ 防作弊系统初始化失败:', error);
                this.initialized = false;
                throw error;
            }
        }

        /**
         * 等待必要的依赖加载
         */
        async waitForDependencies() {
            const dependencies = [
                'storageService',
                'playerManager'
            ];

            const maxWaitTime = 10000; // 最大等待10秒
            const checkInterval = 100; // 每100ms检查一次
            let waitTime = 0;

            while (waitTime < maxWaitTime) {
                const allLoaded = dependencies.every(dep => window[dep] !== undefined);
                
                if (allLoaded) {
                    console.log('📦 所有依赖已加载');
                    return;
                }

                await new Promise(resolve => setTimeout(resolve, checkInterval));
                waitTime += checkInterval;
            }

            throw new Error('等待依赖超时，某些必要组件未加载');
        }

        /**
         * 初始化防作弊核心系统
         */
        async initializeAntiCheatSystem() {
            try {
                if (!window.antiCheatSystem) {
                    throw new Error('AntiCheatSystem 类未加载');
                }

                await window.antiCheatSystem.initialize();
                this.components.antiCheatSystem = true;
                console.log('✅ 防作弊核心系统初始化完成');

            } catch (error) {
                console.error('❌ 防作弊核心系统初始化失败:', error);
                throw error;
            }
        }

        /**
         * 初始化关卡认证系统
         */
        async initializeLevelCertificationSystem() {
            try {
                if (!window.levelCertificationSystem) {
                    throw new Error('LevelCertificationSystem 类未加载');
                }

                await window.levelCertificationSystem.initialize();
                this.components.levelCertificationSystem = true;
                console.log('✅ 关卡认证系统初始化完成');

            } catch (error) {
                console.error('❌ 关卡认证系统初始化失败:', error);
                throw error;
            }
        }

        /**
         * 确保排行榜管理器已初始化
         */
        async ensureLeaderboardManagerInitialized() {
            try {
                if (!window.leaderboardManager) {
                    throw new Error('LeaderboardManager 类未加载');
                }

                if (!window.leaderboardManager.initialized) {
                    await window.leaderboardManager.init();
                }

                this.components.leaderboardManager = true;
                console.log('✅ 排行榜管理器确认已初始化');

            } catch (error) {
                console.error('❌ 排行榜管理器初始化失败:', error);
                throw error;
            }
        }

        /**
         * 设置系统集成
         */
        setupSystemIntegration() {
            try {
                // 设置防作弊系统与排行榜管理器的集成
                if (window.antiCheatSystem && window.leaderboardManager) {
                    // 排行榜管理器已经在submitScore方法中集成了防作弊验证
                    console.log('🔗 防作弊系统与排行榜管理器集成完成');
                }

                // 设置关卡认证系统与关卡管理器的集成
                if (window.levelCertificationSystem && window.levelManager) {
                    // 为关卡管理器添加认证检查方法
                    this.integrateWithLevelManager();
                    console.log('🔗 关卡认证系统与关卡管理器集成完成');
                }

                // 设置游戏引擎集成
                if (window.gameEngine) {
                    this.integrateWithGameEngine();
                    console.log('🔗 防作弊系统与游戏引擎集成完成');
                }

            } catch (error) {
                console.error('❌ 系统集成设置失败:', error);
                throw error;
            }
        }

        /**
         * 与关卡管理器集成
         */
        integrateWithLevelManager() {
            if (!window.levelManager || !window.levelCertificationSystem) return;

            // 添加关卡认证检查方法
            window.levelManager.checkLevelCertification = async function(levelId) {
                const certification = levelCertificationSystem.certificationRecords.get(levelId);
                return certification && certification.status === 'certified';
            };

            // 添加关卡质量评估方法
            window.levelManager.evaluateLevelQuality = async function(levelData) {
                return await levelCertificationSystem.evaluateLevel(levelData);
            };
        }

        /**
         * 与游戏引擎集成
         */
        integrateWithGameEngine() {
            if (!window.gameEngine) return;

            // 为游戏引擎添加事件记录功能（用于防作弊分析）
            const originalEngine = window.gameEngine;
            
            // 记录点击事件
            if (originalEngine.handleClick) {
                const originalHandleClick = originalEngine.handleClick.bind(originalEngine);
                originalEngine.handleClick = function(x, y) {
                    // 记录点击事件
                    if (!this.clickEvents) this.clickEvents = [];
                    this.clickEvents.push({
                        x: x,
                        y: y,
                        timestamp: Date.now(),
                        gameTime: Date.now() - this.gameStartTime
                    });

                    return originalHandleClick(x, y);
                };
            }

            // 记录光点事件
            if (originalEngine.spawnSpark) {
                const originalSpawnSpark = originalEngine.spawnSpark.bind(originalEngine);
                originalEngine.spawnSpark = function(spark) {
                    // 记录光点生成事件
                    if (!this.sparkEvents) this.sparkEvents = [];
                    this.sparkEvents.push({
                        type: 'spawn',
                        spark: { x: spark.x, y: spark.y, size: spark.size },
                        timestamp: Date.now(),
                        gameTime: Date.now() - this.gameStartTime
                    });

                    return originalSpawnSpark(spark);
                };
            }
        }

        /**
         * 验证系统完整性
         */
        async validateSystemIntegrity() {
            const issues = [];

            // 检查核心组件
            if (!this.components.antiCheatSystem) {
                issues.push('防作弊核心系统未正确初始化');
            }

            if (!this.components.levelCertificationSystem) {
                issues.push('关卡认证系统未正确初始化');
            }

            if (!this.components.leaderboardManager) {
                issues.push('排行榜管理器未正确初始化');
            }

            // 检查系统配置
            if (window.antiCheatSystem && !window.antiCheatSystem.initialized) {
                issues.push('防作弊系统配置不完整');
            }

            if (issues.length > 0) {
                throw new Error('系统完整性验证失败: ' + issues.join(', '));
            }

            console.log('✅ 系统完整性验证通过');
        }

        /**
         * 触发初始化完成事件
         */
        dispatchInitializationEvent() {
            const event = new CustomEvent('antiCheatSystemReady', {
                detail: {
                    components: this.components,
                    timestamp: Date.now()
                }
            });

            window.dispatchEvent(event);
            console.log('📡 防作弊系统就绪事件已触发');
        }

        /**
         * 获取系统状态
         */
        getSystemStatus() {
            return {
                initialized: this.initialized,
                components: { ...this.components },
                timestamp: Date.now()
            };
        }

        /**
         * 重新初始化系统
         */
        async reinitialize() {
            this.initialized = false;
            this.initializationPromise = null;
            this.components = {
                antiCheatSystem: false,
                levelCertificationSystem: false,
                leaderboardManager: false
            };

            return await this.initialize();
        }
    }

    // 创建全局初始化器实例
    window.antiCheatInitializer = new AntiCheatInitializer();

    // 自动初始化（在DOM加载完成后）
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                window.antiCheatInitializer.initialize().catch(error => {
                    console.error('自动初始化防作弊系统失败:', error);
                });
            }, 1000); // 延迟1秒确保其他系统已加载
        });
    } else {
        // DOM已经加载完成
        setTimeout(() => {
            window.antiCheatInitializer.initialize().catch(error => {
                console.error('自动初始化防作弊系统失败:', error);
            });
        }, 1000);
    }

    console.log('🛡️ 防作弊系统初始化脚本已加载');

})();
