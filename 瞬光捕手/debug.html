<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面 - 瞬光捕手</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .log-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .log-entry {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
            font-family: monospace;
            font-size: 12px;
        }
        .log-error { color: #d32f2f; }
        .log-warn { color: #f57c00; }
        .log-info { color: #1976d2; }
        .log-success { color: #388e3c; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🐛 调试页面</h1>
        
        <div class="log-section">
            <h3>📋 系统状态</h3>
            <div id="system-status"></div>
            <button onclick="checkSystemStatus()">检查系统状态</button>
        </div>

        <div class="log-section">
            <h3>📦 模块测试</h3>
            <div id="module-status"></div>
            <button onclick="testModules()">测试模块</button>
        </div>

        <div class="log-section">
            <h3>🔧 控制台日志</h3>
            <div id="console-logs"></div>
            <button onclick="clearLogs()">清除日志</button>
        </div>
    </div>

    <!-- 引入核心脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/core/level-manager.js"></script>
    <script src="js/core/level-editor.js"></script>

    <script>
        // 日志收集
        const logs = [];
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };

        // 重写console方法来收集日志
        console.log = function(...args) {
            logs.push({ type: 'info', message: args.join(' '), time: new Date().toLocaleTimeString() });
            originalConsole.log.apply(console, args);
            updateConsoleLogs();
        };

        console.error = function(...args) {
            logs.push({ type: 'error', message: args.join(' '), time: new Date().toLocaleTimeString() });
            originalConsole.error.apply(console, args);
            updateConsoleLogs();
        };

        console.warn = function(...args) {
            logs.push({ type: 'warn', message: args.join(' '), time: new Date().toLocaleTimeString() });
            originalConsole.warn.apply(console, args);
            updateConsoleLogs();
        };

        console.info = function(...args) {
            logs.push({ type: 'info', message: args.join(' '), time: new Date().toLocaleTimeString() });
            originalConsole.info.apply(console, args);
            updateConsoleLogs();
        };

        function updateConsoleLogs() {
            const container = document.getElementById('console-logs');
            const recentLogs = logs.slice(-20); // 只显示最近20条日志
            
            container.innerHTML = recentLogs.map(log => 
                `<div class="log-entry log-${log.type}">[${log.time}] ${log.message}</div>`
            ).join('');
            
            // 自动滚动到底部
            container.scrollTop = container.scrollHeight;
        }

        function clearLogs() {
            logs.length = 0;
            updateConsoleLogs();
        }

        function addStatus(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = message;
            container.appendChild(entry);
        }

        function clearStatus(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        async function checkSystemStatus() {
            clearStatus('system-status');
            
            try {
                // 检查浏览器支持
                addStatus('system-status', '✅ 浏览器支持检查:', 'info');
                addStatus('system-status', `  - IndexedDB: ${!!window.indexedDB}`, 'info');
                addStatus('system-status', `  - LocalStorage: ${!!window.localStorage}`, 'info');
                addStatus('system-status', `  - Canvas: ${!!document.createElement('canvas').getContext}`, 'info');
                addStatus('system-status', `  - WebGL: ${checkWebGLSupport()}`, 'info');
                
                // 检查全局对象
                addStatus('system-status', '✅ 全局对象检查:', 'info');
                addStatus('system-status', `  - StorageService: ${typeof StorageService !== 'undefined'}`, 'info');
                addStatus('system-status', `  - I18nService: ${typeof I18nService !== 'undefined'}`, 'info');
                addStatus('system-status', `  - LevelManager: ${typeof LevelManager !== 'undefined'}`, 'info');
                addStatus('system-status', `  - LevelEditor: ${typeof LevelEditor !== 'undefined'}`, 'info');
                
            } catch (error) {
                addStatus('system-status', `❌ 系统检查失败: ${error.message}`, 'error');
            }
        }

        async function testModules() {
            clearStatus('module-status');
            
            try {
                // 测试存储服务
                addStatus('module-status', '🧪 测试存储服务...', 'info');
                const storage = new StorageService();
                await storage.init();
                addStatus('module-status', '✅ 存储服务初始化成功', 'success');
                
                // 测试存储操作
                await storage.put('test.key', 'test value');
                const value = await storage.get('test.key');
                if (value === 'test value') {
                    addStatus('module-status', '✅ 存储读写测试成功', 'success');
                } else {
                    addStatus('module-status', '❌ 存储读写测试失败', 'error');
                }
                
                // 测试国际化服务
                addStatus('module-status', '🧪 测试国际化服务...', 'info');
                const i18n = new I18nService();
                await i18n.init();
                addStatus('module-status', '✅ 国际化服务初始化成功', 'success');
                
                const text = i18n.t('menu.startGame');
                if (text && text !== 'menu.startGame') {
                    addStatus('module-status', `✅ 翻译测试成功: ${text}`, 'success');
                } else {
                    addStatus('module-status', '❌ 翻译测试失败', 'error');
                }
                
                // 测试关卡管理器
                addStatus('module-status', '🧪 测试关卡管理器...', 'info');
                const levelManager = new LevelManager();
                await levelManager.init();
                addStatus('module-status', '✅ 关卡管理器初始化成功', 'success');
                
                // 测试关卡编辑器
                addStatus('module-status', '🧪 测试关卡编辑器...', 'info');
                const editor = new LevelEditor();
                // 创建一个临时canvas用于测试
                const canvas = document.createElement('canvas');
                canvas.width = 400;
                canvas.height = 300;
                await editor.init(canvas);
                addStatus('module-status', '✅ 关卡编辑器初始化成功', 'success');
                
                addStatus('module-status', '🎉 所有模块测试完成！', 'success');
                
            } catch (error) {
                addStatus('module-status', `❌ 模块测试失败: ${error.message}`, 'error');
                console.error('模块测试详细错误:', error);
            }
        }

        function checkWebGLSupport() {
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                return !!gl;
            } catch (e) {
                return false;
            }
        }

        // 页面加载完成后自动检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkSystemStatus();
                console.log('🐛 调试页面加载完成');
            }, 1000);
        });

        // 捕获未处理的错误
        window.addEventListener('error', (event) => {
            console.error('全局错误:', event.error);
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.error('未处理的Promise拒绝:', event.reason);
        });
    </script>
</body>
</html>
