# 关卡编辑器系统说明

## 概述

关卡编辑器是瞬光捕手游戏的核心功能之一，允许玩家创建、编辑和分享自定义关卡。编辑器提供了直观的可视化界面，支持多种游戏对象类型和丰富的编辑工具。

## 系统架构

### 核心组件

1. **LevelEditor 类** (`js/core/level-editor.js`)
   - 主要的关卡编辑器控制器
   - 管理编辑状态、工具选择和对象操作
   - 处理画布渲染和用户交互

2. **编辑器界面** (`index.html` 中的 level-editor-screen)
   - 工具栏：选择、光点、障碍、道具、触发器、橡皮擦工具
   - 操作面板：新建、加载、保存、测试、发布功能
   - 属性面板：关卡设置和对象属性配置
   - 统计面板：显示关卡对象统计信息

3. **屏幕管理器集成** (`js/ui/screen-manager.js`)
   - 处理编辑器界面的显示和隐藏
   - 管理编辑器相关的用户交互事件
   - 提供编辑器功能的入口点

## 主要功能

### 1. 编辑工具

- **选择工具**：选择和移动关卡中的对象
- **光点工具**：添加各种类型的光点（普通、快速、缓慢、奖励、完美）
- **障碍工具**：添加障碍物（墙壁、移动墙壁、尖刺）
- **道具工具**：添加道具（时间减缓、双倍分数、护盾、多重击中）
- **触发器工具**：添加触发区域
- **橡皮擦工具**：删除选中的对象

### 2. 视图控制

- **网格显示**：显示/隐藏编辑网格
- **网格对齐**：对象自动对齐到网格
- **缩放控制**：放大/缩小编辑视图（0.1x - 5.0x）
- **画布拖拽**：支持鼠标和触摸拖拽移动视图

### 3. 关卡管理

- **新建关卡**：创建空白关卡
- **加载关卡**：从存储中加载已保存的关卡
- **保存关卡**：将当前关卡保存到本地存储
- **测试关卡**：在游戏中测试当前关卡
- **发布关卡**：将关卡发布到共享系统

### 4. 关卡设置

- **基本信息**：关卡名称、描述
- **难度设置**：简单、普通、困难、专家
- **游戏参数**：时间限制、目标分数
- **对象统计**：实时显示各类对象数量

## 技术实现

### 渲染系统

```javascript
// 画布渲染循环
render() {
    this.clearCanvas();
    
    if (this.editorState.showGrid) {
        this.renderGrid();
    }
    
    this.renderObjects();
    this.renderSelection();
    this.renderUI();
}
```

### 输入处理

- **鼠标事件**：点击、拖拽、滚轮缩放
- **触摸事件**：单点触摸、双指缩放、拖拽
- **键盘事件**：快捷键支持（删除、撤销、重做）

### 状态管理

```javascript
// 编辑器状态结构
editorState = {
    currentTool: 'select',
    selectedObjects: [],
    showGrid: true,
    snapToGrid: true,
    zoom: 1.0,
    panOffset: { x: 0, y: 0 },
    isDragging: false,
    isModified: false
}
```

### 撤销/重做系统

- 基于命令模式实现
- 支持所有编辑操作的撤销和重做
- 历史记录限制（默认50步）

## 对象类型

### 光点 (Spark)
- **普通光点**：基础得分对象
- **快速光点**：移动速度快，分数高
- **缓慢光点**：移动速度慢，容易击中
- **奖励光点**：提供额外分数奖励
- **完美光点**：需要完美时机击中

### 障碍物 (Obstacle)
- **墙壁**：静态阻挡物
- **移动墙壁**：按路径移动的障碍
- **尖刺**：接触即失败的危险区域

### 道具 (Powerup)
- **时间减缓**：暂时减慢游戏速度
- **双倍分数**：一段时间内分数翻倍
- **护盾**：保护玩家免受一次伤害
- **多重击中**：允许连续击中多个目标

## 关卡验证

编辑器在保存和发布关卡前会进行验证：

1. **基本验证**
   - 关卡名称不能为空
   - 必须包含至少一个光点
   - 所有对象必须在画布边界内

2. **游戏性验证**
   - 检查关卡是否可以完成
   - 验证目标分数的合理性
   - 确保时间限制适当

## 文件格式

关卡数据以JSON格式存储：

```json
{
  "id": "level_uuid",
  "name": "关卡名称",
  "description": "关卡描述",
  "difficulty": "normal",
  "settings": {
    "timeLimit": 60,
    "targetScore": 1000
  },
  "objects": [
    {
      "id": "object_uuid",
      "type": "spark",
      "subType": "normal",
      "x": 100,
      "y": 200,
      "properties": {}
    }
  ],
  "metadata": {
    "version": "1.0",
    "createdAt": 1234567890,
    "modifiedAt": 1234567890,
    "author": "player_name"
  }
}
```

## 性能优化

1. **渲染优化**
   - 只渲染可见区域内的对象
   - 使用对象池减少内存分配
   - 批量渲染相同类型的对象

2. **交互优化**
   - 使用空间索引加速碰撞检测
   - 防抖处理频繁的用户输入
   - 延迟保存避免频繁IO操作

## 国际化支持

编辑器界面支持中英文切换，所有文本都通过i18n系统管理：

- 工具提示和按钮文本
- 错误消息和确认对话框
- 对象类型和属性名称
- 帮助文档和说明文本

## 扩展性

编辑器设计为可扩展的架构：

1. **新对象类型**：通过注册系统添加新的游戏对象
2. **自定义工具**：可以添加新的编辑工具
3. **插件系统**：支持第三方功能扩展
4. **主题系统**：可自定义编辑器外观

## 使用指南

### 基本操作

1. 点击"关卡编辑器"进入编辑模式
2. 选择工具栏中的工具
3. 在画布上点击放置对象
4. 使用属性面板调整对象设置
5. 保存或测试关卡

### 快捷键

- `Delete`：删除选中对象
- `Ctrl+Z`：撤销操作
- `Ctrl+Y`：重做操作
- `Ctrl+S`：保存关卡
- `Space`：切换网格显示

### 最佳实践

1. **关卡设计原则**
   - 从简单到复杂的难度递进
   - 合理分布光点和障碍物
   - 确保关卡有明确的目标

2. **性能考虑**
   - 避免过多的移动对象
   - 合理设置关卡边界
   - 测试不同设备上的表现

3. **用户体验**
   - 提供清晰的视觉反馈
   - 设置合理的时间限制
   - 包含适当的挑战性

## 故障排除

### 常见问题

1. **编辑器无法启动**
   - 检查浏览器控制台错误
   - 确认所有依赖文件已加载
   - 清除浏览器缓存重试

2. **关卡保存失败**
   - 检查存储空间是否充足
   - 验证关卡数据格式
   - 确认用户权限设置

3. **性能问题**
   - 减少关卡中的对象数量
   - 关闭不必要的视觉效果
   - 使用较低的缩放级别

### 调试工具

编辑器提供了内置的调试功能：

- 控制台日志记录
- 性能监控面板
- 对象检查器
- 状态查看器

## 更新日志

### v1.0.0
- 初始版本发布
- 基础编辑功能实现
- 多种对象类型支持
- 关卡保存和加载
- 国际化支持

## 技术支持

如需技术支持或报告问题，请：

1. 查看浏览器控制台错误信息
2. 记录重现问题的步骤
3. 提供系统环境信息
4. 联系开发团队获取帮助
