# 排行榜系统说明

## 概述

排行榜系统是瞬光捕手游戏的核心功能之一，为玩家提供竞技和成就展示平台。系统支持多种排行榜类型，自动管理数据生命周期，并提供丰富的统计信息。

## 功能特性

### 1. 多类型排行榜
- **全球排行榜** - 记录所有玩家的最高分数
- **每日排行榜** - 每日重置，展示当日最佳成绩
- **每周排行榜** - 每周重置，展示本周最佳成绩
- **每月排行榜** - 每月重置，展示本月最佳成绩
- **完美击中排行榜** - 按完美击中次数排序
- **连击记录排行榜** - 按最高连击数排序
- **关卡通关排行榜** - 按通关关卡数排序

### 2. 自动数据管理
- **定时重置** - 每日、每周、每月排行榜自动重置
- **数据备份** - 重置前自动备份历史数据
- **存储优化** - 限制排行榜条目数量，自动清理过期备份
- **容错处理** - 完善的错误处理和恢复机制

### 3. 丰富的统计信息
- **玩家排名** - 显示玩家在各排行榜中的位置
- **详细数据** - 包含分数、关卡、连击、准确率等信息
- **时间记录** - 记录成绩获得时间，支持相对时间显示
- **成就追踪** - 跟踪玩家的各项游戏成就

## 技术架构

### 核心类：LeaderboardManager

```javascript
class LeaderboardManager {
    constructor() {
        this.leaderboards = new Map();
        this.initialized = false;
        this.leaderboardTypes = { ... };
        this.config = { ... };
    }
}
```

### 主要方法

#### 初始化和数据管理
- `init()` - 初始化排行榜管理器
- `loadLeaderboards()` - 加载所有排行榜数据
- `checkAndResetExpiredLeaderboards()` - 检查并重置过期排行榜

#### 分数提交和查询
- `submitScore(type, scoreData)` - 提交分数到指定排行榜
- `getLeaderboard(type, limit)` - 获取排行榜数据
- `getPlayerRank(type, playerId)` - 获取玩家排名信息

#### 数据维护
- `resetLeaderboard(type)` - 重置指定排行榜
- `backupLeaderboard(leaderboard)` - 备份排行榜数据
- `cleanupOldBackups()` - 清理过期备份

### 数据结构

#### 排行榜条目
```javascript
{
    playerId: "player_id",
    playerName: "玩家名称",
    score: 12345,
    level: 5,
    perfectHits: 100,
    combo: 25,
    timestamp: 1640995200000,
    gameData: {
        duration: 180000,
        accuracy: 95.5,
        totalHits: 200
    }
}
```

#### 排行榜对象
```javascript
{
    type: "global_high_score",
    title: "全球排行",
    entries: [...],
    createdAt: 1640995200000,
    lastUpdated: 1640995200000,
    lastReset: 1640995200000,
    totalEntries: 50
}
```

## 存储策略

### 数据存储
- **主数据** - `leaderboard.{type}.data`
- **备份数据** - `leaderboard.backup.{type}.{timestamp}`
- **配置数据** - 存储在管理器配置中

### 存储优化
- 每个排行榜最多存储100条记录
- 备份数据保留30天后自动清理
- 使用KV存储服务的多层适配器

## 界面集成

### 排行榜界面
- **标签页切换** - 支持不同排行榜类型切换
- **实时刷新** - 支持手动刷新排行榜数据
- **玩家高亮** - 当前玩家记录特殊显示
- **排名展示** - 前三名特殊图标显示

### 游戏集成
- **自动提交** - 游戏结束时自动提交分数
- **多榜提交** - 同时提交到多个相关排行榜
- **条件过滤** - 游客账号不参与排行榜

## 国际化支持

### 中文翻译
```javascript
'leaderboard.title': '排行榜',
'leaderboard.global': '全球排行',
'leaderboard.daily': '今日排行',
// ... 更多翻译
```

### 英文翻译
```javascript
'leaderboard.title': 'Leaderboard',
'leaderboard.global': 'Global',
'leaderboard.daily': 'Daily',
// ... 更多翻译
```

## 性能优化

### 数据加载
- **延迟加载** - 仅在需要时加载排行榜数据
- **缓存机制** - 内存中缓存排行榜数据
- **批量操作** - 批量处理数据更新

### 界面渲染
- **虚拟滚动** - 大量数据时使用虚拟滚动
- **增量更新** - 仅更新变化的排行榜条目
- **防抖处理** - 防止频繁的界面更新

## 错误处理

### 数据完整性
- **数据验证** - 提交前验证分数数据格式
- **备份恢复** - 数据损坏时从备份恢复
- **降级处理** - 存储失败时的降级策略

### 用户体验
- **加载状态** - 显示数据加载进度
- **错误提示** - 友好的错误信息显示
- **重试机制** - 失败操作的自动重试

## 扩展性设计

### 新排行榜类型
- 通过配置轻松添加新的排行榜类型
- 支持自定义排序规则和重置周期
- 灵活的数据结构适应不同需求

### 统计维度
- 支持多维度的成绩统计
- 可扩展的游戏数据记录
- 灵活的排名计算逻辑

## 使用示例

### 提交分数
```javascript
const gameStats = {
    score: 12345,
    level: 5,
    perfectHits: 100,
    combo: 25,
    duration: 180000,
    accuracy: 95.5,
    totalHits: 200
};

await leaderboardManager.submitScore(
    leaderboardManager.leaderboardTypes.GLOBAL_HIGH_SCORE,
    gameStats
);
```

### 获取排行榜
```javascript
const leaderboard = leaderboardManager.getLeaderboard('global_high_score', 10);
console.log('前10名:', leaderboard.entries);
```

### 查询玩家排名
```javascript
const playerRank = leaderboardManager.getPlayerRank('global_high_score', 'player_id');
console.log('玩家排名:', playerRank.rank);
```

## 维护指南

### 定期维护
- 监控排行榜数据大小
- 清理过期的备份数据
- 检查数据完整性

### 故障排除
- 检查存储服务状态
- 验证排行榜数据格式
- 查看控制台错误日志

### 性能监控
- 监控数据加载时间
- 跟踪内存使用情况
- 分析用户操作模式

## 总结

排行榜系统为瞬光捕手游戏提供了完整的竞技功能，通过多类型排行榜、自动数据管理和丰富的统计信息，为玩家创造了良好的竞技体验。系统设计注重性能、可扩展性和用户体验，为游戏的长期发展奠定了坚实基础。
