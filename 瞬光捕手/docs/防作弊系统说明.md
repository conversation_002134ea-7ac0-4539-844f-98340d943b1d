# 瞬光捕手防作弊系统说明

## 系统概述

瞬光捕手防作弊系统是一个综合性的反作弊解决方案，旨在防止玩家通过自定义关卡刷分来操纵排行榜。系统采用多层防护策略，包括分离式排行榜、关卡难度评估、分数验证和关卡认证等功能。

## 核心组件

### 1. 防作弊核心系统 (AntiCheatSystem)
**文件位置**: `js/core/anti-cheat-system.js`

#### 主要功能
- **分数验证**: 检查分数的合理性和一致性
- **关卡难度评估**: 基于多维度算法评估关卡难度
- **异常模式检测**: 识别可疑的游戏行为
- **玩家行为分析**: 跟踪和分析玩家历史行为

#### 核心方法
```javascript
// 验证分数提交
validateScoreSubmission(scoreData, gameData, levelData)

// 计算关卡难度
calculateLevelDifficulty(levelData)

// 分析玩家行为
analyzePlayerBehavior(playerId, scoreData)
```

#### 难度评估算法
系统使用多维度算法评估关卡难度：
- **光点数量** (权重: 0.2): 光点越多难度越高
- **持续时间** (权重: 0.15): 时间越长难度越高
- **目标分数** (权重: 0.15): 目标分数越高难度越高
- **光点速度** (权重: 0.1): 速度越快难度越高
- **光点大小** (权重: 0.1): 光点越小难度越高
- **高级因子** (权重: 0.3): 包括密度、分布、时间压力等

### 2. 分离式排行榜系统 (LeaderboardManager)
**文件位置**: `js/core/leaderboard-manager.js`

#### 排行榜分类
- **官方关卡排行榜**: 内置关卡的排行榜
- **自定义关卡排行榜**: 玩家创建关卡的排行榜
- **认证关卡排行榜**: 通过质量认证的自定义关卡排行榜

#### 排行榜类型
```javascript
// 官方关卡
OFFICIAL_GLOBAL_HIGH_SCORE    // 官方全球最高分
OFFICIAL_DAILY_HIGH_SCORE     // 官方每日最高分
OFFICIAL_WEEKLY_HIGH_SCORE    // 官方每周最高分
OFFICIAL_MONTHLY_HIGH_SCORE   // 官方每月最高分

// 自定义关卡
CUSTOM_GLOBAL_HIGH_SCORE      // 自定义全球最高分
CUSTOM_DAILY_HIGH_SCORE       // 自定义每日最高分

// 认证关卡
CERTIFIED_CUSTOM_HIGH_SCORE   // 认证关卡最高分
```

#### 提交限制
- **最低难度要求**: 自定义关卡难度必须 ≥ 0.3
- **每日提交限制**: 每个玩家每天最多提交50次自定义关卡分数
- **认证要求**: 高质量排行榜需要关卡通过认证

### 3. 关卡认证系统 (LevelCertificationSystem)
**文件位置**: `js/core/level-certification-system.js`

#### 认证标准
系统基于以下维度评估关卡质量：

1. **基础要求检查**
   - 关卡名称长度 ≥ 3个字符
   - 关卡时长 ≥ 15秒
   - 光点数量: 8-80个
   - 目标分数: 500-20000分
   - 最低难度 ≥ 0.4

2. **质量评估维度**
   - **难度合理性** (25%): 难度是否在合理范围内
   - **游戏平衡性** (20%): 时间、分数、光点分布的平衡
   - **创意性** (20%): 特殊效果、独特排列等创新元素
   - **可玩性** (15%): 游戏体验的流畅性和挑战性
   - **技术质量** (10%): 数据完整性和配置合理性
   - **美观性** (10%): 视觉效果和光点分布美观度

#### 认证等级
- **铜牌认证** (≥60分): 可参与排行榜
- **银牌认证** (≥75分): 可参与排行榜 + 推荐展示
- **金牌认证** (≥90分): 可参与排行榜 + 推荐展示 + 精选关卡

### 4. 系统初始化器 (AntiCheatInitializer)
**文件位置**: `js/core/anti-cheat-init.js`

#### 功能
- 自动初始化所有防作弊组件
- 确保组件间正确集成
- 验证系统完整性
- 提供系统状态监控

## 工作流程

### 分数提交流程
1. **游戏结束**: 游戏引擎计算游戏统计数据
2. **数据准备**: 收集游戏数据、关卡数据用于验证
3. **防作弊验证**: 
   - 分数合理性检查
   - 时间一致性验证
   - 异常模式检测
   - 玩家行为分析
4. **排行榜类型确定**: 根据关卡类型确定目标排行榜
5. **提交限制检查**: 检查自定义关卡的提交限制
6. **分数提交**: 提交到相应的排行榜
7. **结果反馈**: 向玩家显示提交结果

### 关卡认证流程
1. **关卡提交**: 玩家提交自定义关卡申请认证
2. **基础检查**: 验证关卡是否满足基本要求
3. **质量评估**: 多维度评估关卡质量
4. **自动认证**: 高分关卡自动通过认证
5. **人工审核**: 中等分数关卡进入人工审核队列
6. **认证结果**: 颁发相应等级的认证证书

## 配置参数

### 防作弊系统配置
```javascript
config: {
    scoreValidation: {
        maxScorePerSecond: 1000,      // 每秒最大分数
        maxPerfectHitRatio: 0.95,     // 最大完美击中比例
        minGameDuration: 3000         // 最短游戏时长
    },
    levelDifficulty: {
        minDifficulty: 0.1,           // 最低难度
        maxDifficulty: 1.0,           // 最高难度
        difficultyWeights: { ... }    // 难度计算权重
    },
    anomalyDetection: {
        maxConsecutivePerfects: 50,   // 最大连续完美击中
        suspiciousComboLength: 100    // 可疑连击长度
    }
}
```

### 排行榜配置
```javascript
config: {
    maxEntries: 100,                  // 每个排行榜最大条目数
    customLevelRestrictions: {
        minDifficulty: 0.3,           // 自定义关卡最低难度
        maxSubmissionsPerDay: 50      // 每日最大提交次数
    }
}
```

## 安全特性

### 多层验证
1. **客户端验证**: 实时检查游戏数据合理性
2. **行为分析**: 分析玩家历史行为模式
3. **统计验证**: 验证游戏统计数据一致性
4. **关卡质量**: 评估关卡设计质量

### 风险等级
- **低风险**: 正常游戏行为，允许提交
- **中风险**: 轻微异常，记录警告但允许提交
- **高风险**: 明显异常，拒绝提交并记录
- **严重风险**: 严重作弊行为，拒绝提交并可能封禁

### 数据保护
- **加密存储**: 敏感数据加密存储
- **备份机制**: 定期备份排行榜数据
- **完整性检查**: 定期验证数据完整性

## 使用方法

### 初始化系统
```javascript
// 系统会自动初始化，也可以手动初始化
await antiCheatInitializer.initialize();
```

### 提交分数
```javascript
// 游戏引擎会自动调用，包含防作弊验证
const result = await leaderboardManager.submitScore(
    type, scoreData, levelData, gameData
);
```

### 关卡认证
```javascript
// 申请关卡认证
const result = await levelCertificationSystem.applyCertification(
    levelId, levelData
);
```

### 检查系统状态
```javascript
// 获取系统状态
const status = antiCheatInitializer.getSystemStatus();
console.log('防作弊系统状态:', status);
```

## 监控和日志

### 日志记录
- **分数验证日志**: 记录所有分数验证结果
- **异常行为日志**: 记录检测到的异常行为
- **认证申请日志**: 记录关卡认证申请和结果
- **系统运行日志**: 记录系统运行状态

### 性能监控
- **验证耗时**: 监控分数验证处理时间
- **内存使用**: 监控系统内存占用
- **缓存命中率**: 监控缓存效率

## 扩展性

### 添加新的验证规则
```javascript
// 在AntiCheatSystem中添加新的验证方法
validateCustomRule(scoreData, gameData) {
    // 自定义验证逻辑
}
```

### 添加新的排行榜类型
```javascript
// 在LeaderboardManager中添加新类型
this.leaderboardTypes.NEW_TYPE = 'new_type';
```

### 添加新的认证维度
```javascript
// 在LevelCertificationSystem中添加新的评估维度
evaluateNewDimension(levelData) {
    // 新的评估逻辑
}
```

## 故障排除

### 常见问题
1. **分数提交失败**: 检查防作弊验证日志
2. **关卡认证失败**: 查看质量评估报告
3. **系统初始化失败**: 检查依赖组件是否正确加载

### 调试工具
```javascript
// 查看防作弊系统状态
console.log(antiCheatSystem.getSystemStatus());

// 查看关卡难度评估
console.log(antiCheatSystem.calculateLevelDifficulty(levelData));

// 查看认证评估结果
console.log(await levelCertificationSystem.evaluateLevel(levelData));
```

## 更新日志

### v1.0.0 (当前版本)
- 实现分离式排行榜系统
- 添加关卡难度评估算法
- 创建关卡认证系统
- 集成防作弊验证机制
- 添加系统初始化和监控功能

---

**注意**: 本系统仍在持续改进中，建议定期更新以获得最新的安全特性和性能优化。
