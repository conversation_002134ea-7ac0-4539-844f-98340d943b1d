# 🎮 瞬光捕手 - 自定义关卡功能说明

## 📋 功能概述

自定义关卡系统是瞬光捕手游戏的核心功能之一，允许玩家创建、分享和游玩用户生成的内容。该系统提供了完整的关卡生命周期管理，包括创建、编辑、发布、评分和删除等功能。

## ✨ 主要特性

### 🎯 关卡管理
- **创建关卡**: 使用内置关卡编辑器创建自定义关卡
- **保存/加载**: 本地存储关卡数据，支持离线使用
- **发布系统**: 将关卡发布供其他玩家游玩
- **版本控制**: 支持关卡更新和版本管理

### 👥 社交功能
- **评分系统**: 点赞/踩功能，每个玩家每个关卡只能评分一次
- **作者标识**: 显示关卡作者信息
- **游玩统计**: 记录关卡游玩次数和受欢迎程度

### 🔍 浏览和搜索
- **关卡列表**: 网格布局展示所有可用关卡
- **筛选功能**: 按作者、难度、评分等条件筛选
- **排序选项**: 支持按评分、游玩次数、创建时间、难度排序
- **搜索功能**: 按关卡名称和描述搜索

### 📱 响应式设计
- **跨平台支持**: PC和移动端都有优化的用户界面
- **触摸友好**: 移动端优化的交互体验
- **自适应布局**: 根据屏幕尺寸自动调整界面

## 🏗️ 技术架构

### 核心组件

#### 1. LevelManager (关卡管理器)
- **文件**: `js/core/level-manager.js`
- **职责**: 关卡数据管理、CRUD操作、评分系统
- **主要方法**:
  - `createCustomLevel()`: 创建新关卡
  - `getAllLevels()`: 获取所有关卡
  - `rateLevelLevel()`: 关卡评分
  - `deleteCustomLevel()`: 删除关卡
  - `validateLevel()`: 关卡验证

#### 2. ScreenManager (屏幕管理器)
- **文件**: `js/ui/screen-manager.js`
- **职责**: 界面管理、用户交互、事件处理
- **主要方法**:
  - `showCustomLevels()`: 显示自定义关卡界面
  - `showLevelDetail()`: 显示关卡详情
  - `playLevel()`: 开始游玩关卡
  - `editLevel()`: 编辑关卡

#### 3. StorageService (存储服务)
- **文件**: `js/utils/storage.js`
- **职责**: 数据持久化、本地存储管理
- **支持**: localStorage 和 IndexedDB 适配器

#### 4. I18nService (国际化服务)
- **文件**: `js/utils/i18n.js`
- **职责**: 多语言支持、文本翻译
- **支持语言**: 中文、英文

### 数据结构

#### 关卡数据格式
```javascript
{
  id: "unique-level-id",
  name: "关卡名称",
  description: "关卡描述",
  author: "作者名称",
  authorId: "作者ID",
  difficulty: "easy|normal|hard|expert",
  createdAt: 1234567890000,
  updatedAt: 1234567890000,
  published: true,
  playCount: 0,
  rating: {
    likes: 0,
    dislikes: 0,
    totalRating: 0
  },
  objects: [
    {
      id: "object-id",
      type: "spark|obstacle|powerup|trigger",
      subType: "specific-type",
      x: 100,
      y: 200,
      properties: {}
    }
  ],
  data: {
    duration: 60000,
    targetScore: 1000,
    backgroundColor: "#1a1a2e",
    sparks: [], // 兼容旧格式
    specialEffects: []
  }
}
```

## 🎨 用户界面

### 主界面组件

#### 1. 关卡列表界面
- **位置**: `#custom-levels-screen`
- **功能**: 展示所有可用关卡的网格布局
- **特性**: 
  - 关卡卡片显示基本信息
  - 悬停效果和动画
  - 响应式网格布局

#### 2. 控制面板
- **筛选控制**: 按发布状态、作者筛选
- **排序控制**: 多种排序选项
- **搜索功能**: 实时搜索关卡

#### 3. 关卡详情对话框
- **位置**: `#level-detail-dialog`
- **功能**: 显示关卡详细信息和预览
- **特性**:
  - 关卡元数据展示
  - 实时关卡预览画布
  - 评分和操作按钮

### 样式系统

#### CSS 文件结构
- **主样式**: `styles/main.css` - 包含自定义关卡的所有样式
- **响应式**: `styles/responsive.css` - 移动端和平板适配

#### 设计特点
- **深色主题**: 符合游戏整体风格
- **渐变效果**: 现代化的视觉效果
- **毛玻璃效果**: backdrop-filter 实现的模糊背景
- **动画过渡**: 流畅的用户体验

## 🔧 开发指南

### 添加新功能

#### 1. 扩展关卡对象类型
```javascript
// 在 level-manager.js 中添加新的对象类型处理
case 'newObjectType':
    // 处理新对象类型的逻辑
    break;
```

#### 2. 添加新的筛选条件
```javascript
// 在 screen-manager.js 的 loadCustomLevelsList 方法中
if (filter === 'newFilter') {
    filteredLevels = filteredLevels.filter(level => {
        // 新的筛选逻辑
    });
}
```

#### 3. 国际化支持
```javascript
// 在 i18n.js 中添加新的翻译键
'customLevels.newFeature': '新功能',
'customLevels.newFeature.description': '新功能描述',
```

### 性能优化建议

#### 1. 关卡列表虚拟化
对于大量关卡，考虑实现虚拟滚动：
```javascript
// 只渲染可见区域的关卡卡片
const visibleLevels = levels.slice(startIndex, endIndex);
```

#### 2. 图片懒加载
关卡预览图片使用懒加载：
```javascript
// 使用 Intersection Observer API
const observer = new IntersectionObserver(callback);
observer.observe(imageElement);
```

#### 3. 数据缓存
实现关卡数据缓存机制：
```javascript
// 缓存经常访问的关卡数据
const levelCache = new Map();
```

## 🧪 测试

### 测试文件
- **测试页面**: `test-custom-levels.html`
- **功能**: 提供完整的功能测试界面
- **覆盖范围**: 基础功能、界面、响应式、性能测试

### 测试类型

#### 1. 单元测试
- 关卡管理器方法测试
- 数据验证测试
- 存储服务测试

#### 2. 集成测试
- 界面与后端交互测试
- 跨组件通信测试
- 数据流测试

#### 3. 用户体验测试
- 响应式设计测试
- 移动端交互测试
- 性能基准测试

### 运行测试
1. 在浏览器中打开 `test-custom-levels.html`
2. 点击各个测试按钮
3. 查看测试结果
4. 生成测试报告

## 🚀 部署和维护

### 部署检查清单
- [ ] 所有JavaScript文件正确加载
- [ ] CSS样式文件包含在HTML中
- [ ] 国际化文件配置正确
- [ ] 存储服务初始化成功
- [ ] 响应式设计在各设备上正常

### 维护建议
1. **定期备份**: 定期备份用户创建的关卡数据
2. **性能监控**: 监控关卡加载和渲染性能
3. **用户反馈**: 收集用户对自定义关卡功能的反馈
4. **版本更新**: 保持关卡数据格式的向后兼容性

## 📞 技术支持

如果在使用或开发过程中遇到问题，请检查：

1. **浏览器控制台**: 查看JavaScript错误信息
2. **网络面板**: 检查资源加载情况
3. **存储面板**: 验证数据是否正确保存
4. **测试页面**: 使用测试页面诊断问题

## 🎯 未来规划

### 计划中的功能
- [ ] 关卡分享和导入/导出
- [ ] 关卡评论系统
- [ ] 关卡标签和分类
- [ ] 关卡排行榜
- [ ] 协作编辑功能
- [ ] 关卡模板系统

### 技术改进
- [ ] WebGL 渲染优化
- [ ] 离线同步机制
- [ ] 云端存储集成
- [ ] 实时多人游戏支持

---

**版本**: 1.0.0  
**最后更新**: 2025-07-30  
**维护者**: 瞬光捕手开发团队
