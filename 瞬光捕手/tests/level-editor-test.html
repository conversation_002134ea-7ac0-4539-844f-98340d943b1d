<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关卡编辑器测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #test-canvas {
            border: 1px solid #ccc;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎮 关卡编辑器功能测试</h1>
        
        <div class="test-section">
            <h3>📋 测试概述</h3>
            <p>本页面用于测试关卡编辑器的核心功能，包括初始化、工具切换、对象创建等。</p>
            <button onclick="runAllTests()">运行所有测试</button>
            <button onclick="clearResults()">清除结果</button>
        </div>

        <div class="test-section">
            <h3>🔧 环境检查</h3>
            <div id="env-results"></div>
            <button onclick="checkEnvironment()">检查环境</button>
        </div>

        <div class="test-section">
            <h3>🎨 编辑器初始化测试</h3>
            <div id="init-results"></div>
            <canvas id="test-canvas" width="400" height="300"></canvas>
            <button onclick="testEditorInit()">测试初始化</button>
        </div>

        <div class="test-section">
            <h3>🛠️ 工具功能测试</h3>
            <div id="tools-results"></div>
            <button onclick="testTools()">测试工具</button>
        </div>

        <div class="test-section">
            <h3>📦 对象操作测试</h3>
            <div id="objects-results"></div>
            <button onclick="testObjects()">测试对象操作</button>
        </div>

        <div class="test-section">
            <h3>💾 保存加载测试</h3>
            <div id="storage-results"></div>
            <button onclick="testStorage()">测试保存加载</button>
        </div>

        <div class="test-section">
            <h3>🌐 国际化测试</h3>
            <div id="i18n-results"></div>
            <button onclick="testI18n()">测试国际化</button>
        </div>
    </div>

    <!-- 引入必要的脚本文件 -->
    <script src="../js/utils/storage-service.js"></script>
    <script src="../js/utils/i18n.js"></script>
    <script src="../js/core/level-manager.js"></script>
    <script src="../js/core/level-editor.js"></script>

    <script>
        // 测试工具函数
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result test-${type}`;
            result.innerHTML = message;
            container.appendChild(result);
        }

        function clearResults() {
            const containers = ['env-results', 'init-results', 'tools-results', 'objects-results', 'storage-results', 'i18n-results'];
            containers.forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
        }

        // 环境检查
        async function checkEnvironment() {
            const container = 'env-results';
            
            try {
                // 检查必要的全局对象
                const checks = [
                    { name: 'StorageService', obj: typeof StorageService !== 'undefined' },
                    { name: 'I18nService', obj: typeof I18nService !== 'undefined' },
                    { name: 'LevelManager', obj: typeof LevelManager !== 'undefined' },
                    { name: 'LevelEditor', obj: typeof LevelEditor !== 'undefined' },
                    { name: 'Canvas支持', obj: !!document.createElement('canvas').getContext },
                    { name: 'LocalStorage支持', obj: typeof Storage !== 'undefined' }
                ];

                checks.forEach(check => {
                    const type = check.obj ? 'pass' : 'fail';
                    const status = check.obj ? '✅' : '❌';
                    addResult(container, `${status} ${check.name}: ${check.obj ? '支持' : '不支持'}`, type);
                });

                // 初始化服务
                if (typeof storageService === 'undefined') {
                    window.storageService = new StorageService();
                    await storageService.init();
                    addResult(container, '✅ StorageService 初始化完成', 'pass');
                }

                if (typeof i18nService === 'undefined') {
                    window.i18nService = new I18nService();
                    await i18nService.init();
                    addResult(container, '✅ I18nService 初始化完成', 'pass');
                }

                if (typeof levelManager === 'undefined') {
                    window.levelManager = new LevelManager();
                    await levelManager.init();
                    addResult(container, '✅ LevelManager 初始化完成', 'pass');
                }

            } catch (error) {
                addResult(container, `❌ 环境检查失败: ${error.message}`, 'fail');
            }
        }

        // 编辑器初始化测试
        async function testEditorInit() {
            const container = 'init-results';
            
            try {
                // 创建编辑器实例
                const canvas = document.getElementById('test-canvas');
                window.testEditor = new LevelEditor();
                
                // 初始化编辑器
                await testEditor.init(canvas);
                addResult(container, '✅ 编辑器初始化成功', 'pass');
                
                // 检查初始状态
                if (testEditor.initialized) {
                    addResult(container, '✅ 编辑器状态正确', 'pass');
                } else {
                    addResult(container, '❌ 编辑器状态异常', 'fail');
                }
                
                // 检查画布
                if (testEditor.canvas && testEditor.ctx) {
                    addResult(container, '✅ 画布初始化成功', 'pass');
                } else {
                    addResult(container, '❌ 画布初始化失败', 'fail');
                }
                
                // 测试渲染
                testEditor.render();
                addResult(container, '✅ 渲染测试通过', 'pass');
                
            } catch (error) {
                addResult(container, `❌ 初始化测试失败: ${error.message}`, 'fail');
            }
        }

        // 工具功能测试
        function testTools() {
            const container = 'tools-results';
            
            try {
                if (!window.testEditor || !testEditor.initialized) {
                    addResult(container, '❌ 请先运行编辑器初始化测试', 'fail');
                    return;
                }
                
                // 测试工具切换
                const tools = ['select', 'spark', 'obstacle', 'powerup', 'trigger', 'eraser'];
                
                tools.forEach(tool => {
                    testEditor.setCurrentTool(tool);
                    if (testEditor.editorState.currentTool === tool) {
                        addResult(container, `✅ 工具切换成功: ${tool}`, 'pass');
                    } else {
                        addResult(container, `❌ 工具切换失败: ${tool}`, 'fail');
                    }
                });
                
                // 测试工具状态
                testEditor.setCurrentTool('select');
                if (testEditor.getCurrentTool() === 'select') {
                    addResult(container, '✅ 工具状态获取正确', 'pass');
                } else {
                    addResult(container, '❌ 工具状态获取错误', 'fail');
                }
                
            } catch (error) {
                addResult(container, `❌ 工具测试失败: ${error.message}`, 'fail');
            }
        }

        // 对象操作测试
        function testObjects() {
            const container = 'objects-results';
            
            try {
                if (!window.testEditor || !testEditor.initialized) {
                    addResult(container, '❌ 请先运行编辑器初始化测试', 'fail');
                    return;
                }
                
                // 创建新关卡
                testEditor.createNewLevel();
                addResult(container, '✅ 新关卡创建成功', 'pass');
                
                // 添加对象
                const testObject = {
                    type: 'spark',
                    subType: 'normal',
                    x: 100,
                    y: 100,
                    properties: {}
                };
                
                testEditor.addObject(testObject);
                
                if (testEditor.currentLevel.objects.length > 0) {
                    addResult(container, '✅ 对象添加成功', 'pass');
                } else {
                    addResult(container, '❌ 对象添加失败', 'fail');
                }
                
                // 选择对象
                const addedObject = testEditor.currentLevel.objects[0];
                testEditor.selectObject(addedObject.id);
                
                if (testEditor.editorState.selectedObjects.includes(addedObject.id)) {
                    addResult(container, '✅ 对象选择成功', 'pass');
                } else {
                    addResult(container, '❌ 对象选择失败', 'fail');
                }
                
                // 删除对象
                testEditor.deleteObject(addedObject.id);
                
                if (testEditor.currentLevel.objects.length === 0) {
                    addResult(container, '✅ 对象删除成功', 'pass');
                } else {
                    addResult(container, '❌ 对象删除失败', 'fail');
                }
                
            } catch (error) {
                addResult(container, `❌ 对象操作测试失败: ${error.message}`, 'fail');
            }
        }

        // 保存加载测试
        async function testStorage() {
            const container = 'storage-results';
            
            try {
                if (!window.testEditor || !testEditor.initialized) {
                    addResult(container, '❌ 请先运行编辑器初始化测试', 'fail');
                    return;
                }
                
                // 创建测试关卡
                testEditor.createNewLevel();
                testEditor.currentLevel.name = '测试关卡';
                testEditor.currentLevel.description = '这是一个测试关卡';
                
                // 添加测试对象
                testEditor.addObject({
                    type: 'spark',
                    subType: 'normal',
                    x: 150,
                    y: 150,
                    properties: {}
                });
                
                // 保存关卡
                const saveResult = await testEditor.saveLevel();
                if (saveResult) {
                    addResult(container, '✅ 关卡保存成功', 'pass');
                } else {
                    addResult(container, '❌ 关卡保存失败', 'fail');
                    return;
                }
                
                const savedLevelId = testEditor.currentLevel.id;
                
                // 创建新关卡（清空当前状态）
                testEditor.createNewLevel();
                
                // 加载关卡
                const loadResult = await testEditor.loadLevel(savedLevelId);
                if (loadResult) {
                    addResult(container, '✅ 关卡加载成功', 'pass');
                    
                    // 验证加载的数据
                    if (testEditor.currentLevel.name === '测试关卡') {
                        addResult(container, '✅ 关卡数据验证成功', 'pass');
                    } else {
                        addResult(container, '❌ 关卡数据验证失败', 'fail');
                    }
                } else {
                    addResult(container, '❌ 关卡加载失败', 'fail');
                }
                
            } catch (error) {
                addResult(container, `❌ 保存加载测试失败: ${error.message}`, 'fail');
            }
        }

        // 国际化测试
        function testI18n() {
            const container = 'i18n-results';
            
            try {
                if (typeof i18nService === 'undefined') {
                    addResult(container, '❌ I18nService 未初始化', 'fail');
                    return;
                }
                
                // 测试中文翻译
                const chineseText = i18nService.t('editor.title');
                if (chineseText && chineseText !== 'editor.title') {
                    addResult(container, `✅ 中文翻译正常: ${chineseText}`, 'pass');
                } else {
                    addResult(container, '❌ 中文翻译失败', 'fail');
                }
                
                // 切换到英文
                i18nService.setLanguage('en');
                const englishText = i18nService.t('editor.title');
                if (englishText && englishText !== chineseText) {
                    addResult(container, `✅ 英文翻译正常: ${englishText}`, 'pass');
                } else {
                    addResult(container, '❌ 英文翻译失败', 'fail');
                }
                
                // 切换回中文
                i18nService.setLanguage('zh');
                addResult(container, '✅ 语言切换功能正常', 'pass');
                
            } catch (error) {
                addResult(container, `❌ 国际化测试失败: ${error.message}`, 'fail');
            }
        }

        // 运行所有测试
        async function runAllTests() {
            clearResults();
            
            addResult('env-results', '🚀 开始运行所有测试...', 'info');
            
            await checkEnvironment();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testEditorInit();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testTools();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testObjects();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testStorage();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testI18n();
            
            addResult('env-results', '✅ 所有测试完成！', 'pass');
        }

        // 页面加载完成后自动检查环境
        window.addEventListener('load', () => {
            setTimeout(checkEnvironment, 1000);
        });
    </script>
</body>
</html>
