/**
 * Split-Second Spark - 国际化服务
 * 提供多语言支持和本地化功能
 */

class I18nService {
    constructor() {
        this.currentLanguage = 'zh-CN';
        this.fallbackLanguage = 'zh-CN';
        this.translations = {};
        this.isInitialized = false;
        
        // 初始化翻译数据
        this.initTranslations();
    }

    /**
     * 初始化翻译数据
     */
    initTranslations() {
        // 中文翻译
        this.translations['zh-CN'] = {
            // 加载界面
            'loading.subtitle': '捕捉决定性瞬间，引燃无限可能',
            'loading.initializing': '正在初始化...',
            'loading.loading_games': '正在加载游戏...',
            'loading.preparing': '正在准备...',
            'loading.complete': '加载完成',

            // 主界面
            'main.subtitle': '捕捉决定性瞬间，引燃无限可能',
            
            // 控制按钮
            'controls.language': '中文',
            'controls.settings': '设置',
            
            // 游戏选择
            'games.title': '选择游戏',
            'games.description': '每个游戏都有独特的玩法和挑战，选择您感兴趣的开始体验',
            
            // 时空织梦者
            'games.temporal.title': '时空织梦者',
            'games.temporal.subtitle': 'Temporal Dream Weaver',
            'games.temporal.description': '通过操控时间流动来编织梦境，解决复杂的时空谜题。注重策略思考和时间管理的创新解谜游戏。',
            'games.temporal.feature1': '时间操控',
            'games.temporal.feature2': '策略解谜',
            'games.temporal.feature3': '梦境编织',
            
            // 瞬光捕手
            'games.spark.title': '瞬光捕手',
            'games.spark.subtitle': 'Split-Second Spark',
            'games.spark.description': '考验反应速度和时机把握的休闲游戏。在光点达到最佳时机时精准点击，获得高分并解锁更多关卡。',
            'games.spark.feature1': '精准时机',
            'games.spark.feature2': '连击系统',
            'games.spark.feature3': '反应挑战',
            
            // 量子共鸣者
            'games.quantum.title': '量子共鸣者',
            'games.quantum.subtitle': 'Quantum Resonance',
            'games.quantum.description': '音乐节奏与物理模拟结合的创新游戏。通过控制量子粒子的共鸣频率创建连锁反应，体验独特的量子物理机制。',
            'games.quantum.feature1': '量子共鸣',
            'games.quantum.feature2': '音乐节奏',
            'games.quantum.feature3': '物理模拟',
            
            // 操作按钮
            'actions.play': '开始游戏',
            'actions.preview': '预览',
            'actions.close': '关闭',
            'actions.cancel': '取消',
            'actions.save': '保存',
            'actions.confirm': '确认',
            'actions.back': '返回',
            
            // 设置
            'settings.title': '设置',
            'settings.display': '显示设置',
            'settings.performance': '性能设置',
            'settings.theme': '主题',
            'settings.theme.dark': '深色',
            'settings.theme.light': '浅色',
            'settings.theme.auto': '自动',
            'settings.language': '语言',
            'settings.effects': '视觉效果',
            'settings.effects.high': '高',
            'settings.effects.medium': '中',
            'settings.effects.low': '低',
            'settings.fps': '帧率限制',
            'settings.fps.auto': '自动',
            
            // 底部信息
            'footer.copyright': '© 2024 Split-Second Spark Team. All rights reserved.',
            'footer.version': '版本 1.0.0',
            'footer.help': '帮助',
            'footer.about': '关于',
            'footer.feedback': '反馈',
            
            // 错误信息
            'error.game_not_found': '游戏未找到',
            'error.loading_failed': '加载失败',
            'error.network_error': '网络错误',
            'error.unknown_error': '未知错误',
            
            // 预览内容
            'preview.loading': '正在加载预览...',
            'preview.no_preview': '暂无预览内容',
            'preview.features': '游戏特色',
            'preview.controls': '操作说明',
            'preview.requirements': '系统要求'
        };

        // 英文翻译
        this.translations['en-US'] = {
            // Loading screen
            'loading.subtitle': 'Capture decisive moments, ignite infinite possibilities',
            'loading.initializing': 'Initializing...',
            'loading.loading_games': 'Loading games...',
            'loading.preparing': 'Preparing...',
            'loading.complete': 'Loading complete',

            // Main interface
            'main.subtitle': 'Capture decisive moments, ignite infinite possibilities',
            
            // Control buttons
            'controls.language': 'English',
            'controls.settings': 'Settings',
            
            // Game selection
            'games.title': 'Choose Game',
            'games.description': 'Each game offers unique gameplay and challenges. Select one that interests you to begin your experience',
            
            // Temporal Dream Weaver
            'games.temporal.title': 'Temporal Dream Weaver',
            'games.temporal.subtitle': '时空织梦者',
            'games.temporal.description': 'Weave dreams by manipulating time flow and solve complex temporal puzzles. An innovative puzzle game focused on strategic thinking and time management.',
            'games.temporal.feature1': 'Time Control',
            'games.temporal.feature2': 'Strategic Puzzle',
            'games.temporal.feature3': 'Dream Weaving',
            
            // Split-Second Spark
            'games.spark.title': 'Split-Second Spark',
            'games.spark.subtitle': '瞬光捕手',
            'games.spark.description': 'A casual game that tests reaction speed and timing. Click precisely when light points reach optimal moments to score high and unlock more levels.',
            'games.spark.feature1': 'Precise Timing',
            'games.spark.feature2': 'Combo System',
            'games.spark.feature3': 'Reaction Challenge',
            
            // Quantum Resonance
            'games.quantum.title': 'Quantum Resonance',
            'games.quantum.subtitle': '量子共鸣者',
            'games.quantum.description': 'An innovative game combining music rhythm with physics simulation. Control quantum particle resonance frequencies to create chain reactions and experience unique quantum physics mechanisms.',
            'games.quantum.feature1': 'Quantum Resonance',
            'games.quantum.feature2': 'Music Rhythm',
            'games.quantum.feature3': 'Physics Simulation',
            
            // Action buttons
            'actions.play': 'Play Game',
            'actions.preview': 'Preview',
            'actions.close': 'Close',
            'actions.cancel': 'Cancel',
            'actions.save': 'Save',
            'actions.confirm': 'Confirm',
            'actions.back': 'Back',
            
            // Settings
            'settings.title': 'Settings',
            'settings.display': 'Display Settings',
            'settings.performance': 'Performance Settings',
            'settings.theme': 'Theme',
            'settings.theme.dark': 'Dark',
            'settings.theme.light': 'Light',
            'settings.theme.auto': 'Auto',
            'settings.language': 'Language',
            'settings.effects': 'Visual Effects',
            'settings.effects.high': 'High',
            'settings.effects.medium': 'Medium',
            'settings.effects.low': 'Low',
            'settings.fps': 'FPS Limit',
            'settings.fps.auto': 'Auto',
            
            // Footer
            'footer.copyright': '© 2024 Split-Second Spark Team. All rights reserved.',
            'footer.version': 'Version 1.0.0',
            'footer.help': 'Help',
            'footer.about': 'About',
            'footer.feedback': 'Feedback',
            
            // Error messages
            'error.game_not_found': 'Game not found',
            'error.loading_failed': 'Loading failed',
            'error.network_error': 'Network error',
            'error.unknown_error': 'Unknown error',
            
            // Preview content
            'preview.loading': 'Loading preview...',
            'preview.no_preview': 'No preview available',
            'preview.features': 'Game Features',
            'preview.controls': 'Controls',
            'preview.requirements': 'System Requirements'
        };
    }

    /**
     * 初始化国际化服务
     */
    async init() {
        try {
            console.log('🌍 初始化国际化服务...');
            
            // 从存储中读取用户语言设置
            if (typeof storageService !== 'undefined') {
                const savedLanguage = await storageService.get('user_language');
                if (savedLanguage && this.translations[savedLanguage]) {
                    this.currentLanguage = savedLanguage;
                }
            }
            
            // 如果没有保存的语言设置，尝试检测浏览器语言
            if (!this.currentLanguage || this.currentLanguage === 'zh-CN') {
                this.currentLanguage = this.detectBrowserLanguage();
            }
            
            this.isInitialized = true;
            console.log(`✅ 国际化服务初始化完成，当前语言: ${this.currentLanguage}`);
            
            // 应用翻译
            this.applyTranslations();
            
        } catch (error) {
            console.error('❌ 国际化服务初始化失败:', error);
            this.currentLanguage = this.fallbackLanguage;
            this.isInitialized = true;
        }
    }

    /**
     * 检测浏览器语言
     */
    detectBrowserLanguage() {
        const browserLang = navigator.language || navigator.userLanguage || 'zh-CN';
        
        // 检查是否有完全匹配的语言
        if (this.translations[browserLang]) {
            return browserLang;
        }
        
        // 检查是否有语言代码匹配（如 en-US -> en）
        const langCode = browserLang.split('-')[0];
        for (const lang in this.translations) {
            if (lang.startsWith(langCode)) {
                return lang;
            }
        }
        
        return this.fallbackLanguage;
    }

    /**
     * 获取翻译文本
     * @param {string} key - 翻译键
     * @param {Object} params - 参数对象
     * @returns {string} 翻译后的文本
     */
    t(key, params = {}) {
        const translation = this.translations[this.currentLanguage];
        let text = translation && translation[key] ? translation[key] : key;
        
        // 如果当前语言没有翻译，尝试使用回退语言
        if (text === key && this.currentLanguage !== this.fallbackLanguage) {
            const fallbackTranslation = this.translations[this.fallbackLanguage];
            text = fallbackTranslation && fallbackTranslation[key] ? fallbackTranslation[key] : key;
        }
        
        // 替换参数
        Object.keys(params).forEach(param => {
            text = text.replace(new RegExp(`{${param}}`, 'g'), params[param]);
        });
        
        return text;
    }

    /**
     * 设置语言
     * @param {string} language - 语言代码
     */
    async setLanguage(language) {
        if (!this.translations[language]) {
            console.warn(`⚠️ 不支持的语言: ${language}`);
            return false;
        }
        
        this.currentLanguage = language;
        
        // 保存到存储
        if (typeof storageService !== 'undefined') {
            try {
                await storageService.put('user_language', language);
            } catch (error) {
                console.warn('⚠️ 保存语言设置失败:', error);
            }
        }
        
        // 应用翻译
        this.applyTranslations();
        
        // 更新HTML语言属性
        document.documentElement.lang = language;
        
        console.log(`🌍 语言已切换为: ${language}`);
        return true;
    }

    /**
     * 应用翻译到页面元素
     */
    applyTranslations() {
        const elements = document.querySelectorAll('[data-i18n]');
        elements.forEach(element => {
            const key = element.getAttribute('data-i18n');
            const translation = this.t(key);
            
            if (element.tagName === 'INPUT' && element.type === 'text') {
                element.placeholder = translation;
            } else {
                element.textContent = translation;
            }
        });
        
        // 触发语言变更事件
        window.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language: this.currentLanguage }
        }));
    }

    /**
     * 获取当前语言
     */
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    /**
     * 获取支持的语言列表
     */
    getSupportedLanguages() {
        return Object.keys(this.translations);
    }

    /**
     * 检查是否支持指定语言
     */
    isLanguageSupported(language) {
        return !!this.translations[language];
    }

    /**
     * 添加翻译
     * @param {string} language - 语言代码
     * @param {Object} translations - 翻译对象
     */
    addTranslations(language, translations) {
        if (!this.translations[language]) {
            this.translations[language] = {};
        }
        
        Object.assign(this.translations[language], translations);
        console.log(`📝 已添加 ${language} 语言翻译`);
    }

    /**
     * 切换语言（在当前支持的语言间循环）
     */
    async toggleLanguage() {
        const languages = this.getSupportedLanguages();
        const currentIndex = languages.indexOf(this.currentLanguage);
        const nextIndex = (currentIndex + 1) % languages.length;
        const nextLanguage = languages[nextIndex];
        
        await this.setLanguage(nextLanguage);
        return nextLanguage;
    }
}

// 创建全局国际化服务实例
const i18nService = new I18nService();

// 导出服务
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { I18nService, i18nService };
} else {
    window.i18nService = i18nService;
}
