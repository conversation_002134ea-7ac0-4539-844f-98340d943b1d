/**
 * Split-Second Spark - 游戏启动器
 * 负责游戏的启动、预览和管理功能
 */

class GameLauncher {
    constructor() {
        this.games = new Map();
        this.currentGame = null;
        this.isInitialized = false;
        
        // 初始化游戏配置
        this.initGameConfigs();
    }

    /**
     * 初始化游戏配置
     */
    initGameConfigs() {
        // 时空织梦者配置
        this.games.set('temporal', {
            id: 'temporal',
            name: 'games.temporal.title',
            subtitle: 'games.temporal.subtitle',
            description: 'games.temporal.description',
            features: ['games.temporal.feature1', 'games.temporal.feature2', 'games.temporal.feature3'],
            path: '时空织梦者/index.html',
            icon: '⏰',
            gradient: 'temporal-gradient',
            animationType: 'particles',
            category: 'puzzle',
            difficulty: 'medium',
            estimatedTime: '30-60分钟',
            requirements: {
                browser: 'Chrome 80+, Firefox 75+, Safari 13+',
                memory: '512MB',
                storage: '50MB'
            },
            controls: {
                'zh-CN': [
                    '鼠标左键：选择/确认',
                    '鼠标右键：取消/返回',
                    '空格键：暂停/继续',
                    'R键：重置当前关卡',
                    'ESC键：返回菜单'
                ],
                'en-US': [
                    'Left Click: Select/Confirm',
                    'Right Click: Cancel/Back',
                    'Space: Pause/Resume',
                    'R Key: Reset Level',
                    'ESC Key: Return to Menu'
                ]
            }
        });

        // 瞬光捕手配置
        this.games.set('spark', {
            id: 'spark',
            name: 'games.spark.title',
            subtitle: 'games.spark.subtitle',
            description: 'games.spark.description',
            features: ['games.spark.feature1', 'games.spark.feature2', 'games.spark.feature3'],
            path: '瞬光捕手/index.html',
            icon: '⚡',
            gradient: 'spark-gradient',
            animationType: 'sparks',
            category: 'action',
            difficulty: 'easy',
            estimatedTime: '10-30分钟',
            requirements: {
                browser: 'Chrome 70+, Firefox 70+, Safari 12+',
                memory: '256MB',
                storage: '20MB'
            },
            controls: {
                'zh-CN': [
                    '鼠标左键：点击捕捉',
                    '空格键：暂停游戏',
                    'R键：重新开始',
                    'ESC键：返回菜单'
                ],
                'en-US': [
                    'Left Click: Click to Catch',
                    'Space: Pause Game',
                    'R Key: Restart',
                    'ESC Key: Return to Menu'
                ]
            }
        });

        // 量子共鸣者配置
        this.games.set('quantum', {
            id: 'quantum',
            name: 'games.quantum.title',
            subtitle: 'games.quantum.subtitle',
            description: 'games.quantum.description',
            features: ['games.quantum.feature1', 'games.quantum.feature2', 'games.quantum.feature3'],
            path: '量子共鸣者/index.html',
            icon: '🌀',
            gradient: 'quantum-gradient',
            animationType: 'quantum',
            category: 'rhythm',
            difficulty: 'hard',
            estimatedTime: '20-45分钟',
            requirements: {
                browser: 'Chrome 85+, Firefox 80+, Safari 14+',
                memory: '1GB',
                storage: '100MB'
            },
            controls: {
                'zh-CN': [
                    '鼠标移动：控制频率',
                    '鼠标左键：激活共鸣',
                    '鼠标右键：停止共鸣',
                    '空格键：暂停/继续',
                    'ESC键：返回菜单'
                ],
                'en-US': [
                    'Mouse Move: Control Frequency',
                    'Left Click: Activate Resonance',
                    'Right Click: Stop Resonance',
                    'Space: Pause/Resume',
                    'ESC Key: Return to Menu'
                ]
            }
        });
    }

    /**
     * 初始化游戏启动器
     */
    async init() {
        try {
            console.log('🎮 初始化游戏启动器...');
            
            // 检查游戏文件是否存在
            await this.validateGameFiles();
            
            this.isInitialized = true;
            console.log('✅ 游戏启动器初始化完成');
            
        } catch (error) {
            console.error('❌ 游戏启动器初始化失败:', error);
            this.isInitialized = true; // 即使失败也标记为已初始化，避免重复初始化
        }
    }

    /**
     * 验证游戏文件是否存在
     */
    async validateGameFiles() {
        const validationPromises = Array.from(this.games.values()).map(async (game) => {
            try {
                const response = await fetch(game.path, { method: 'HEAD' });
                game.available = response.ok;
                if (!response.ok) {
                    console.warn(`⚠️ 游戏文件不存在: ${game.path}`);
                }
            } catch (error) {
                console.warn(`⚠️ 无法验证游戏文件: ${game.path}`, error);
                game.available = false;
            }
        });
        
        await Promise.all(validationPromises);
    }

    /**
     * 启动游戏
     * @param {string} gameId - 游戏ID
     */
    async launchGame(gameId) {
        if (!this.isInitialized) {
            await this.init();
        }

        const game = this.games.get(gameId);
        if (!game) {
            throw new Error(`游戏不存在: ${gameId}`);
        }

        if (game.available === false) {
            throw new Error(`游戏文件不可用: ${gameId}`);
        }

        try {
            console.log(`🚀 启动游戏: ${gameId}`);

            // 记录游戏启动
            await this.recordGameLaunch(gameId);

            // 设置当前游戏
            this.currentGame = gameId;

            // 创建游戏容器并在iframe中启动游戏
            this.launchGameInContainer(game);

        } catch (error) {
            console.error(`❌ 启动游戏失败 [${gameId}]:`, error);
            throw error;
        }
    }

    /**
     * 在容器中启动游戏
     * @param {Object} game - 游戏配置对象
     */
    launchGameInContainer(game) {
        // 隐藏主界面
        const mainScreen = document.getElementById('main-screen');
        if (mainScreen) {
            mainScreen.style.display = 'none';
        }

        // 创建游戏容器
        const gameContainer = this.createGameContainer(game);
        document.body.appendChild(gameContainer);

        // 创建iframe加载游戏
        const iframe = this.createGameIframe(game);
        gameContainer.appendChild(iframe);

        // 设置返回按钮事件
        this.setupReturnButton(gameContainer);

        // 监听来自游戏的消息
        this.setupGameMessageListener();
    }

    /**
     * 创建游戏容器
     * @param {Object} game - 游戏配置对象
     * @returns {HTMLElement} 游戏容器元素
     */
    createGameContainer(game) {
        const container = document.createElement('div');
        container.id = 'game-container';
        container.className = 'game-container active';
        container.innerHTML = `
            <div class="game-header">
                <button class="return-btn" id="return-to-menu-btn" title="返回游戏选择">
                    <span class="btn-icon">🏠</span>
                    <span class="btn-text">返回菜单</span>
                </button>
                <h2 class="game-title">${this.getGameDisplayName(game.id)}</h2>
                <div class="game-controls">
                    <button class="control-btn fullscreen-btn" id="fullscreen-btn" title="全屏">
                        <span class="btn-icon">⛶</span>
                    </button>
                </div>
            </div>
            <div class="game-content" id="game-content">
                <!-- 游戏iframe将在这里加载 -->
            </div>
        `;
        return container;
    }

    /**
     * 创建游戏iframe
     * @param {Object} game - 游戏配置对象
     * @returns {HTMLElement} iframe元素
     */
    createGameIframe(game) {
        const iframe = document.createElement('iframe');
        iframe.id = 'game-iframe';
        iframe.className = 'game-iframe';
        iframe.src = game.path;
        iframe.frameBorder = '0';
        iframe.allowFullscreen = true;
        iframe.allow = 'autoplay; fullscreen; gamepad; microphone';

        // 添加加载事件监听
        iframe.addEventListener('load', () => {
            console.log(`✅ 游戏加载完成: ${game.id}`);
            this.hideLoadingIndicator();
        });

        iframe.addEventListener('error', (error) => {
            console.error(`❌ 游戏加载失败: ${game.id}`, error);
            this.showGameLoadError(game);
        });

        return iframe;
    }

    /**
     * 设置返回按钮事件
     * @param {HTMLElement} container - 游戏容器
     */
    setupReturnButton(container) {
        const returnBtn = container.querySelector('#return-to-menu-btn');
        if (returnBtn) {
            returnBtn.addEventListener('click', () => {
                this.returnToGameSelector();
            });
        }

        const fullscreenBtn = container.querySelector('#fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', () => {
                this.toggleGameFullscreen();
            });
        }
    }

    /**
     * 设置游戏消息监听器
     */
    setupGameMessageListener() {
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'RETURN_TO_GAME_SELECTOR') {
                console.log('🏠 收到游戏返回请求');
                this.returnToGameSelector();
            }
        });
    }

    /**
     * 返回游戏选择器
     */
    returnToGameSelector() {
        console.log('🏠 返回游戏选择器');

        // 移除游戏容器
        const gameContainer = document.getElementById('game-container');
        if (gameContainer) {
            gameContainer.remove();
        }

        // 显示主界面
        const mainScreen = document.getElementById('main-screen');
        if (mainScreen) {
            mainScreen.style.display = 'block';
        }

        // 清除当前游戏
        this.currentGame = null;

        // 触发返回事件
        window.dispatchEvent(new CustomEvent('gameExited'));
    }

    /**
     * 切换游戏全屏
     */
    toggleGameFullscreen() {
        const iframe = document.getElementById('game-iframe');
        if (!iframe) return;

        if (!document.fullscreenElement) {
            iframe.requestFullscreen().catch(err => {
                console.warn('⚠️ 无法进入全屏模式:', err);
            });
        } else {
            document.exitFullscreen().catch(err => {
                console.warn('⚠️ 无法退出全屏模式:', err);
            });
        }
    }

    /**
     * 获取游戏显示名称
     * @param {string} gameId - 游戏ID
     * @returns {string} 显示名称
     */
    getGameDisplayName(gameId) {
        const game = this.games.get(gameId);
        if (!game) return gameId;

        // 根据游戏ID返回中文名称
        const displayNames = {
            'temporal': '时空织梦者',
            'spark': '瞬光捕手',
            'quantum': '量子共鸣者'
        };

        return displayNames[gameId] || game.name;
    }

    /**
     * 隐藏加载指示器
     */
    hideLoadingIndicator() {
        // 可以在这里添加加载完成的UI反馈
        console.log('🎮 游戏加载完成');
    }

    /**
     * 显示游戏加载错误
     * @param {Object} game - 游戏配置对象
     */
    showGameLoadError(game) {
        const gameContent = document.getElementById('game-content');
        if (gameContent) {
            gameContent.innerHTML = `
                <div class="game-error">
                    <div class="error-icon">❌</div>
                    <h3>游戏加载失败</h3>
                    <p>无法加载游戏：${this.getGameDisplayName(game.id)}</p>
                    <p>请检查网络连接或稍后重试</p>
                    <button class="retry-btn" onclick="location.reload()">重新加载</button>
                </div>
            `;
        }
    }

    /**
     * 记录游戏启动
     * @param {string} gameId - 游戏ID
     */
    async recordGameLaunch(gameId) {
        try {
            if (typeof storageService !== 'undefined') {
                // 记录启动时间
                const launchRecord = {
                    gameId,
                    timestamp: Date.now(),
                    date: new Date().toISOString()
                };
                
                await storageService.put(`launch_${gameId}_${Date.now()}`, launchRecord);
                
                // 更新游戏统计
                const stats = await this.getGameStats(gameId);
                stats.launchCount = (stats.launchCount || 0) + 1;
                stats.lastLaunch = Date.now();
                
                await storageService.put(`stats_${gameId}`, stats);
            }
        } catch (error) {
            console.warn('⚠️ 记录游戏启动失败:', error);
        }
    }

    /**
     * 获取游戏统计信息
     * @param {string} gameId - 游戏ID
     */
    async getGameStats(gameId) {
        try {
            if (typeof storageService !== 'undefined') {
                const stats = await storageService.get(`stats_${gameId}`);
                return stats || {
                    gameId,
                    launchCount: 0,
                    lastLaunch: null,
                    totalPlayTime: 0
                };
            }
        } catch (error) {
            console.warn('⚠️ 获取游戏统计失败:', error);
        }
        
        return {
            gameId,
            launchCount: 0,
            lastLaunch: null,
            totalPlayTime: 0
        };
    }

    /**
     * 获取游戏信息
     * @param {string} gameId - 游戏ID
     */
    getGameInfo(gameId) {
        return this.games.get(gameId);
    }

    /**
     * 获取所有游戏列表
     */
    getAllGames() {
        return Array.from(this.games.values());
    }

    /**
     * 获取可用游戏列表
     */
    getAvailableGames() {
        return Array.from(this.games.values()).filter(game => game.available !== false);
    }

    /**
     * 生成游戏预览内容
     * @param {string} gameId - 游戏ID
     * @param {string} language - 语言代码
     */
    generatePreviewContent(gameId, language = 'zh-CN') {
        const game = this.games.get(gameId);
        if (!game) {
            return '<p>游戏不存在</p>';
        }

        const t = (key) => {
            if (typeof i18nService !== 'undefined') {
                return i18nService.t(key);
            }
            return key;
        };

        const controls = game.controls[language] || game.controls['zh-CN'] || [];
        const controlsHtml = controls.map(control => `<li>${control}</li>`).join('');

        return `
            <div class="preview-content">
                <div class="preview-section">
                    <h4>${t('preview.features')}</h4>
                    <ul class="feature-list">
                        ${game.features.map(feature => `<li>${t(feature)}</li>`).join('')}
                    </ul>
                </div>
                
                <div class="preview-section">
                    <h4>${t('preview.controls')}</h4>
                    <ul class="controls-list">
                        ${controlsHtml}
                    </ul>
                </div>
                
                <div class="preview-section">
                    <h4>${t('preview.requirements')}</h4>
                    <div class="requirements">
                        <p><strong>浏览器:</strong> ${game.requirements.browser}</p>
                        <p><strong>内存:</strong> ${game.requirements.memory}</p>
                        <p><strong>存储:</strong> ${game.requirements.storage}</p>
                        <p><strong>预计游戏时间:</strong> ${game.estimatedTime}</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 检查游戏兼容性
     * @param {string} gameId - 游戏ID
     */
    checkGameCompatibility(gameId) {
        const game = this.games.get(gameId);
        if (!game) {
            return { compatible: false, reason: '游戏不存在' };
        }

        // 检查浏览器兼容性
        const userAgent = navigator.userAgent;
        const isChrome = /Chrome/.test(userAgent);
        const isFirefox = /Firefox/.test(userAgent);
        const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);

        if (!isChrome && !isFirefox && !isSafari) {
            return { 
                compatible: false, 
                reason: '不支持的浏览器，建议使用 Chrome、Firefox 或 Safari' 
            };
        }

        // 检查特殊要求
        if (gameId === 'quantum') {
            if (!window.AudioContext && !window.webkitAudioContext) {
                return { 
                    compatible: false, 
                    reason: '浏览器不支持 Web Audio API，无法运行量子共鸣者' 
                };
            }
        }

        return { compatible: true };
    }

    /**
     * 获取推荐游戏
     * @param {string} excludeGameId - 要排除的游戏ID
     */
    async getRecommendedGames(excludeGameId = null) {
        const availableGames = this.getAvailableGames().filter(game => 
            game.id !== excludeGameId
        );

        // 简单的推荐算法：基于启动次数和最后启动时间
        const gamesWithStats = await Promise.all(
            availableGames.map(async (game) => {
                const stats = await this.getGameStats(game.id);
                return { ...game, stats };
            })
        );

        // 按推荐度排序
        gamesWithStats.sort((a, b) => {
            const scoreA = (a.stats.launchCount || 0) * 0.7 + 
                          (a.stats.lastLaunch ? (Date.now() - a.stats.lastLaunch) / (1000 * 60 * 60 * 24) : 30) * 0.3;
            const scoreB = (b.stats.launchCount || 0) * 0.7 + 
                          (b.stats.lastLaunch ? (Date.now() - b.stats.lastLaunch) / (1000 * 60 * 60 * 24) : 30) * 0.3;
            return scoreB - scoreA;
        });

        return gamesWithStats.slice(0, 2);
    }
}

// 创建全局游戏启动器实例
const gameLauncher = new GameLauncher();

// 导出服务
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GameLauncher, gameLauncher };
} else {
    window.gameLauncher = gameLauncher;
}
