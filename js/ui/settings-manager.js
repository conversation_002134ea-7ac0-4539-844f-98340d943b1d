/**
 * Split-Second Spark - 设置管理器
 * 负责用户设置的管理和持久化
 */

class SettingsManager {
    constructor() {
        this.settings = new Map();
        this.defaultSettings = new Map();
        this.isInitialized = false;
        this.changeListeners = new Map();
        
        // 初始化默认设置
        this.initDefaultSettings();
    }

    /**
     * 初始化默认设置
     */
    initDefaultSettings() {
        this.defaultSettings.set('user_theme', 'dark');
        this.defaultSettings.set('user_language', 'zh-CN');
        this.defaultSettings.set('visual_effects', 'high');
        this.defaultSettings.set('fps_limit', '60');
        this.defaultSettings.set('sound_enabled', true);
        this.defaultSettings.set('sound_volume', 0.8);
        this.defaultSettings.set('music_enabled', true);
        this.defaultSettings.set('music_volume', 0.6);
        this.defaultSettings.set('auto_save', true);
        this.defaultSettings.set('show_fps', false);
        this.defaultSettings.set('reduce_motion', false);
        this.defaultSettings.set('high_contrast', false);
        this.defaultSettings.set('font_size', 'medium');
        this.defaultSettings.set('game_difficulty', 'normal');
        this.defaultSettings.set('tutorial_completed', false);
        this.defaultSettings.set('analytics_enabled', true);
    }

    /**
     * 初始化设置管理器
     */
    async init() {
        if (this.isInitialized) return;
        
        try {
            console.log('⚙️ 初始化设置管理器...');
            
            // 加载所有设置
            await this.loadAllSettings();
            
            // 应用设置
            await this.applyAllSettings();
            
            this.isInitialized = true;
            console.log('✅ 设置管理器初始化完成');
            
        } catch (error) {
            console.error('❌ 设置管理器初始化失败:', error);
            // 使用默认设置
            this.useDefaultSettings();
            this.isInitialized = true;
        }
    }

    /**
     * 加载所有设置
     */
    async loadAllSettings() {
        if (typeof storageService === 'undefined') {
            this.useDefaultSettings();
            return;
        }

        try {
            // 获取所有设置键
            const settingKeys = await storageService.list('setting_');
            
            // 加载每个设置
            for (const key of settingKeys) {
                const settingName = key.replace('setting_', '');
                const value = await storageService.get(key);
                if (value !== null) {
                    this.settings.set(settingName, value);
                }
            }
            
            // 为没有保存值的设置使用默认值
            for (const [key, defaultValue] of this.defaultSettings) {
                if (!this.settings.has(key)) {
                    this.settings.set(key, defaultValue);
                }
            }
            
            console.log(`📋 已加载 ${this.settings.size} 个设置项`);
            
        } catch (error) {
            console.warn('⚠️ 加载设置失败，使用默认设置:', error);
            this.useDefaultSettings();
        }
    }

    /**
     * 使用默认设置
     */
    useDefaultSettings() {
        this.settings.clear();
        for (const [key, value] of this.defaultSettings) {
            this.settings.set(key, value);
        }
    }

    /**
     * 应用所有设置
     */
    async applyAllSettings() {
        // 应用主题
        const theme = this.get('user_theme');
        this.applyTheme(theme);
        
        // 应用语言
        const language = this.get('user_language');
        if (typeof i18nService !== 'undefined') {
            await i18nService.setLanguage(language);
        }
        
        // 应用视觉效果
        const effects = this.get('visual_effects');
        this.applyVisualEffects(effects);
        
        // 应用帧率限制
        const fps = this.get('fps_limit');
        this.applyFpsLimit(fps);
        
        // 应用辅助功能设置
        const reduceMotion = this.get('reduce_motion');
        const highContrast = this.get('high_contrast');
        const fontSize = this.get('font_size');
        
        this.applyAccessibilitySettings({
            reduceMotion,
            highContrast,
            fontSize
        });
        
        // 应用调试设置
        const showFps = this.get('show_fps');
        this.applyDebugSettings({ showFps });
    }

    /**
     * 获取设置值
     * @param {string} key - 设置键
     * @returns {any} 设置值
     */
    get(key) {
        return this.settings.has(key) ? this.settings.get(key) : this.defaultSettings.get(key);
    }

    /**
     * 设置值
     * @param {string} key - 设置键
     * @param {any} value - 设置值
     * @param {boolean} persist - 是否持久化
     */
    async set(key, value, persist = true) {
        const oldValue = this.get(key);
        this.settings.set(key, value);
        
        // 持久化到存储
        if (persist && typeof storageService !== 'undefined') {
            try {
                await storageService.put(`setting_${key}`, value);
            } catch (error) {
                console.warn(`⚠️ 保存设置失败 [${key}]:`, error);
            }
        }
        
        // 触发变更事件
        this.notifyChange(key, value, oldValue);
        
        // 应用设置
        await this.applySetting(key, value);
    }

    /**
     * 批量设置
     * @param {Object} settings - 设置对象
     * @param {boolean} persist - 是否持久化
     */
    async setMultiple(settings, persist = true) {
        const changes = [];
        
        for (const [key, value] of Object.entries(settings)) {
            const oldValue = this.get(key);
            this.settings.set(key, value);
            changes.push({ key, value, oldValue });
            
            // 持久化到存储
            if (persist && typeof storageService !== 'undefined') {
                try {
                    await storageService.put(`setting_${key}`, value);
                } catch (error) {
                    console.warn(`⚠️ 保存设置失败 [${key}]:`, error);
                }
            }
        }
        
        // 批量触发变更事件
        for (const change of changes) {
            this.notifyChange(change.key, change.value, change.oldValue);
            await this.applySetting(change.key, change.value);
        }
    }

    /**
     * 重置设置
     * @param {string} key - 设置键，如果不提供则重置所有设置
     */
    async reset(key = null) {
        if (key) {
            // 重置单个设置
            const defaultValue = this.defaultSettings.get(key);
            if (defaultValue !== undefined) {
                await this.set(key, defaultValue);
            }
        } else {
            // 重置所有设置
            const defaultSettings = {};
            for (const [k, v] of this.defaultSettings) {
                defaultSettings[k] = v;
            }
            await this.setMultiple(defaultSettings);
        }
    }

    /**
     * 应用单个设置
     * @param {string} key - 设置键
     * @param {any} value - 设置值
     */
    async applySetting(key, value) {
        switch (key) {
            case 'user_theme':
                this.applyTheme(value);
                break;
            case 'user_language':
                if (typeof i18nService !== 'undefined') {
                    await i18nService.setLanguage(value);
                }
                break;
            case 'visual_effects':
                this.applyVisualEffects(value);
                break;
            case 'fps_limit':
                this.applyFpsLimit(value);
                break;
            case 'reduce_motion':
            case 'high_contrast':
            case 'font_size':
                this.applyAccessibilitySettings({
                    reduceMotion: this.get('reduce_motion'),
                    highContrast: this.get('high_contrast'),
                    fontSize: this.get('font_size')
                });
                break;
            case 'show_fps':
                this.applyDebugSettings({ showFps: value });
                break;
        }
    }

    /**
     * 应用主题
     * @param {string} theme - 主题名称
     */
    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        
        // 更新主题色彩
        if (theme === 'light') {
            document.documentElement.style.setProperty('--bg-primary', '#ffffff');
            document.documentElement.style.setProperty('--bg-secondary', '#f8f9fa');
            document.documentElement.style.setProperty('--text-primary', '#212529');
            document.documentElement.style.setProperty('--text-secondary', '#6c757d');
        } else if (theme === 'dark') {
            document.documentElement.style.setProperty('--bg-primary', '#0f0f23');
            document.documentElement.style.setProperty('--bg-secondary', '#1a1a2e');
            document.documentElement.style.setProperty('--text-primary', '#ffffff');
            document.documentElement.style.setProperty('--text-secondary', '#b8b8d1');
        }
        
        console.log(`🎨 应用主题: ${theme}`);
    }

    /**
     * 应用视觉效果
     * @param {string} level - 效果级别
     */
    applyVisualEffects(level) {
        document.documentElement.setAttribute('data-effects', level);
        
        // 根据效果级别调整动画
        const root = document.documentElement;
        switch (level) {
            case 'low':
                root.style.setProperty('--transition-fast', '0s');
                root.style.setProperty('--transition-normal', '0s');
                root.style.setProperty('--transition-slow', '0s');
                break;
            case 'medium':
                root.style.setProperty('--transition-fast', '0.1s');
                root.style.setProperty('--transition-normal', '0.2s');
                root.style.setProperty('--transition-slow', '0.3s');
                break;
            case 'high':
            default:
                root.style.setProperty('--transition-fast', '0.2s');
                root.style.setProperty('--transition-normal', '0.3s');
                root.style.setProperty('--transition-slow', '0.5s');
                break;
        }
        
        console.log(`✨ 应用视觉效果: ${level}`);
    }

    /**
     * 应用帧率限制
     * @param {string} fps - 帧率限制
     */
    applyFpsLimit(fps) {
        document.documentElement.setAttribute('data-fps', fps);
        
        // 设置全局帧率限制变量
        window.FPS_LIMIT = fps === 'auto' ? null : parseInt(fps);
        
        console.log(`🎯 应用帧率限制: ${fps}`);
    }

    /**
     * 应用辅助功能设置
     * @param {Object} options - 辅助功能选项
     */
    applyAccessibilitySettings(options) {
        const { reduceMotion, highContrast, fontSize } = options;
        
        // 减少动画
        if (reduceMotion) {
            document.documentElement.style.setProperty('--transition-fast', '0s');
            document.documentElement.style.setProperty('--transition-normal', '0s');
            document.documentElement.style.setProperty('--transition-slow', '0s');
        }
        
        // 高对比度
        if (highContrast) {
            document.documentElement.setAttribute('data-high-contrast', 'true');
        } else {
            document.documentElement.removeAttribute('data-high-contrast');
        }
        
        // 字体大小
        const fontSizeMap = {
            'small': '14px',
            'medium': '16px',
            'large': '18px',
            'extra-large': '20px'
        };
        
        if (fontSizeMap[fontSize]) {
            document.documentElement.style.fontSize = fontSizeMap[fontSize];
        }
        
        console.log(`♿ 应用辅助功能设置:`, options);
    }

    /**
     * 应用调试设置
     * @param {Object} options - 调试选项
     */
    applyDebugSettings(options) {
        const { showFps } = options;
        
        // 显示FPS
        if (showFps) {
            this.createFpsCounter();
        } else {
            this.removeFpsCounter();
        }
        
        console.log(`🐛 应用调试设置:`, options);
    }

    /**
     * 创建FPS计数器
     */
    createFpsCounter() {
        if (document.getElementById('fps-counter')) return;
        
        const fpsCounter = document.createElement('div');
        fpsCounter.id = 'fps-counter';
        fpsCounter.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: #00ff00;
            padding: 5px 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            pointer-events: none;
        `;
        
        document.body.appendChild(fpsCounter);
        
        // 启动FPS计算
        this.startFpsCounter();
    }

    /**
     * 移除FPS计数器
     */
    removeFpsCounter() {
        const fpsCounter = document.getElementById('fps-counter');
        if (fpsCounter) {
            fpsCounter.remove();
        }
        
        if (this.fpsCounterInterval) {
            clearInterval(this.fpsCounterInterval);
            this.fpsCounterInterval = null;
        }
    }

    /**
     * 启动FPS计数器
     */
    startFpsCounter() {
        let lastTime = performance.now();
        let frameCount = 0;
        let fps = 0;
        
        const updateFps = () => {
            const currentTime = performance.now();
            frameCount++;
            
            if (currentTime - lastTime >= 1000) {
                fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                frameCount = 0;
                lastTime = currentTime;
                
                const fpsCounter = document.getElementById('fps-counter');
                if (fpsCounter) {
                    fpsCounter.textContent = `FPS: ${fps}`;
                }
            }
            
            requestAnimationFrame(updateFps);
        };
        
        updateFps();
    }

    /**
     * 添加设置变更监听器
     * @param {string} key - 设置键
     * @param {Function} callback - 回调函数
     */
    addChangeListener(key, callback) {
        if (!this.changeListeners.has(key)) {
            this.changeListeners.set(key, []);
        }
        this.changeListeners.get(key).push(callback);
    }

    /**
     * 移除设置变更监听器
     * @param {string} key - 设置键
     * @param {Function} callback - 回调函数
     */
    removeChangeListener(key, callback) {
        if (this.changeListeners.has(key)) {
            const listeners = this.changeListeners.get(key);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * 通知设置变更
     * @param {string} key - 设置键
     * @param {any} newValue - 新值
     * @param {any} oldValue - 旧值
     */
    notifyChange(key, newValue, oldValue) {
        if (this.changeListeners.has(key)) {
            const listeners = this.changeListeners.get(key);
            listeners.forEach(callback => {
                try {
                    callback(newValue, oldValue, key);
                } catch (error) {
                    console.error('❌ 设置变更监听器执行失败:', error);
                }
            });
        }
        
        // 触发全局事件
        window.dispatchEvent(new CustomEvent('settingChanged', {
            detail: { key, newValue, oldValue }
        }));
    }

    /**
     * 获取所有设置
     */
    getAllSettings() {
        const result = {};
        for (const [key, value] of this.settings) {
            result[key] = value;
        }
        return result;
    }

    /**
     * 导出设置
     */
    exportSettings() {
        return JSON.stringify(this.getAllSettings(), null, 2);
    }

    /**
     * 导入设置
     * @param {string} settingsJson - 设置JSON字符串
     */
    async importSettings(settingsJson) {
        try {
            const settings = JSON.parse(settingsJson);
            await this.setMultiple(settings);
            console.log('✅ 设置导入成功');
        } catch (error) {
            console.error('❌ 设置导入失败:', error);
            throw error;
        }
    }
}

// 创建全局设置管理器实例
const settingsManager = new SettingsManager();

// 导出服务
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { SettingsManager, settingsManager };
} else {
    window.settingsManager = settingsManager;
}
