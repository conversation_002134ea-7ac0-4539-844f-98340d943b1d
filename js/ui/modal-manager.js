/**
 * Split-Second Spark - 模态框管理器
 * 负责模态框的显示、隐藏和交互管理
 */

class ModalManager {
    constructor() {
        this.activeModal = null;
        this.modalStack = [];
        this.isInitialized = false;
        
        // 绑定键盘事件
        this.handleKeyDown = this.handleKeyDown.bind(this);
    }

    /**
     * 初始化模态框管理器
     */
    init() {
        if (this.isInitialized) return;
        
        console.log('📱 初始化模态框管理器...');
        
        // 绑定全局键盘事件
        document.addEventListener('keydown', this.handleKeyDown);
        
        // 初始化所有模态框
        this.initModals();
        
        this.isInitialized = true;
        console.log('✅ 模态框管理器初始化完成');
    }

    /**
     * 初始化所有模态框
     */
    initModals() {
        // 预览模态框
        this.initPreviewModal();
        
        // 设置模态框
        this.initSettingsModal();
    }

    /**
     * 初始化预览模态框
     */
    initPreviewModal() {
        const modal = document.getElementById('preview-modal');
        if (!modal) return;

        const overlay = modal.querySelector('.modal-overlay');
        const closeBtn = modal.querySelector('.modal-close');
        const cancelBtn = modal.querySelector('#preview-close');
        const playBtn = modal.querySelector('#preview-play');

        // 绑定关闭事件
        if (overlay) {
            overlay.addEventListener('click', () => this.hideModal('preview-modal'));
        }
        
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.hideModal('preview-modal'));
        }
        
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.hideModal('preview-modal'));
        }

        // 绑定播放按钮事件
        if (playBtn) {
            playBtn.addEventListener('click', () => {
                const gameId = playBtn.getAttribute('data-game-id');
                if (gameId) {
                    this.hideModal('preview-modal');
                    this.launchGame(gameId);
                }
            });
        }
    }

    /**
     * 初始化设置模态框
     */
    initSettingsModal() {
        const modal = document.getElementById('settings-modal');
        if (!modal) return;

        const overlay = modal.querySelector('.modal-overlay');
        const closeBtn = modal.querySelector('.modal-close');
        const cancelBtn = modal.querySelector('[data-i18n="actions.cancel"]');
        const saveBtn = modal.querySelector('[data-i18n="actions.save"]');

        // 绑定关闭事件
        if (overlay) {
            overlay.addEventListener('click', () => this.hideModal('settings-modal'));
        }
        
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.hideModal('settings-modal'));
        }
        
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.hideModal('settings-modal'));
        }

        // 绑定保存按钮事件
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveSettings());
        }

        // 初始化设置控件
        this.initSettingsControls();
    }

    /**
     * 初始化设置控件
     */
    initSettingsControls() {
        // 主题选择
        const themeSelect = document.getElementById('theme-select');
        if (themeSelect) {
            themeSelect.addEventListener('change', (e) => {
                this.applyTheme(e.target.value);
            });
        }

        // 语言选择
        const languageSelect = document.getElementById('language-select');
        if (languageSelect) {
            languageSelect.addEventListener('change', (e) => {
                if (typeof i18nService !== 'undefined') {
                    i18nService.setLanguage(e.target.value);
                }
            });
        }

        // 视觉效果选择
        const effectsSelect = document.getElementById('effects-select');
        if (effectsSelect) {
            effectsSelect.addEventListener('change', (e) => {
                this.applyEffectsLevel(e.target.value);
            });
        }

        // 帧率限制选择
        const fpsSelect = document.getElementById('fps-select');
        if (fpsSelect) {
            fpsSelect.addEventListener('change', (e) => {
                this.applyFpsLimit(e.target.value);
            });
        }
    }

    /**
     * 显示模态框
     * @param {string} modalId - 模态框ID
     * @param {Object} options - 选项
     */
    showModal(modalId, options = {}) {
        const modal = document.getElementById(modalId);
        if (!modal) {
            console.warn(`⚠️ 模态框不存在: ${modalId}`);
            return;
        }

        // 隐藏当前活动的模态框
        if (this.activeModal && this.activeModal !== modalId) {
            this.hideModal(this.activeModal, false);
        }

        // 添加到模态框栈
        if (!this.modalStack.includes(modalId)) {
            this.modalStack.push(modalId);
        }

        // 设置为活动模态框
        this.activeModal = modalId;

        // 显示模态框
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';

        // 应用选项
        if (options.title) {
            const titleElement = modal.querySelector('.modal-title');
            if (titleElement) {
                titleElement.textContent = options.title;
            }
        }

        if (options.content) {
            const contentElement = modal.querySelector('.modal-body');
            if (contentElement) {
                contentElement.innerHTML = options.content;
            }
        }

        // 触发显示事件
        window.dispatchEvent(new CustomEvent('modalShown', {
            detail: { modalId, options }
        }));

        console.log(`📱 显示模态框: ${modalId}`);
    }

    /**
     * 隐藏模态框
     * @param {string} modalId - 模态框ID
     * @param {boolean} removeFromStack - 是否从栈中移除
     */
    hideModal(modalId, removeFromStack = true) {
        const modal = document.getElementById(modalId);
        if (!modal) return;

        // 隐藏模态框
        modal.classList.remove('active');

        // 从栈中移除
        if (removeFromStack) {
            const index = this.modalStack.indexOf(modalId);
            if (index > -1) {
                this.modalStack.splice(index, 1);
            }
        }

        // 更新活动模态框
        if (this.activeModal === modalId) {
            this.activeModal = this.modalStack.length > 0 ? 
                this.modalStack[this.modalStack.length - 1] : null;
        }

        // 如果没有活动模态框，恢复页面滚动
        if (!this.activeModal) {
            document.body.style.overflow = '';
        }

        // 触发隐藏事件
        window.dispatchEvent(new CustomEvent('modalHidden', {
            detail: { modalId }
        }));

        console.log(`📱 隐藏模态框: ${modalId}`);
    }

    /**
     * 隐藏所有模态框
     */
    hideAllModals() {
        this.modalStack.forEach(modalId => {
            this.hideModal(modalId, false);
        });
        
        this.modalStack = [];
        this.activeModal = null;
        document.body.style.overflow = '';
    }

    /**
     * 显示游戏预览
     * @param {string} gameId - 游戏ID
     */
    showGamePreview(gameId) {
        if (typeof gameLauncher === 'undefined') {
            console.error('❌ 游戏启动器未初始化');
            return;
        }

        const game = gameLauncher.getGameInfo(gameId);
        if (!game) {
            console.error(`❌ 游戏不存在: ${gameId}`);
            return;
        }

        // 生成预览内容
        const content = gameLauncher.generatePreviewContent(gameId, 
            typeof i18nService !== 'undefined' ? i18nService.getCurrentLanguage() : 'zh-CN'
        );

        // 设置播放按钮的游戏ID
        const playBtn = document.querySelector('#preview-play');
        if (playBtn) {
            playBtn.setAttribute('data-game-id', gameId);
        }

        // 显示模态框
        this.showModal('preview-modal', {
            title: typeof i18nService !== 'undefined' ? 
                i18nService.t(game.name) : game.name,
            content: content
        });
    }

    /**
     * 显示设置界面
     */
    async showSettings() {
        // 加载当前设置
        await this.loadCurrentSettings();
        
        // 显示设置模态框
        this.showModal('settings-modal');
    }

    /**
     * 加载当前设置
     */
    async loadCurrentSettings() {
        try {
            if (typeof storageService === 'undefined') return;

            // 加载主题设置
            const theme = await storageService.get('user_theme') || 'dark';
            const themeSelect = document.getElementById('theme-select');
            if (themeSelect) {
                themeSelect.value = theme;
            }

            // 加载语言设置
            const language = typeof i18nService !== 'undefined' ? 
                i18nService.getCurrentLanguage() : 'zh-CN';
            const languageSelect = document.getElementById('language-select');
            if (languageSelect) {
                languageSelect.value = language;
            }

            // 加载视觉效果设置
            const effects = await storageService.get('visual_effects') || 'high';
            const effectsSelect = document.getElementById('effects-select');
            if (effectsSelect) {
                effectsSelect.value = effects;
            }

            // 加载帧率设置
            const fps = await storageService.get('fps_limit') || '60';
            const fpsSelect = document.getElementById('fps-select');
            if (fpsSelect) {
                fpsSelect.value = fps;
            }

        } catch (error) {
            console.warn('⚠️ 加载设置失败:', error);
        }
    }

    /**
     * 保存设置
     */
    async saveSettings() {
        try {
            if (typeof storageService === 'undefined') {
                this.hideModal('settings-modal');
                return;
            }

            // 保存主题设置
            const themeSelect = document.getElementById('theme-select');
            if (themeSelect) {
                await storageService.put('user_theme', themeSelect.value);
            }

            // 保存视觉效果设置
            const effectsSelect = document.getElementById('effects-select');
            if (effectsSelect) {
                await storageService.put('visual_effects', effectsSelect.value);
            }

            // 保存帧率设置
            const fpsSelect = document.getElementById('fps-select');
            if (fpsSelect) {
                await storageService.put('fps_limit', fpsSelect.value);
            }

            console.log('✅ 设置已保存');
            this.hideModal('settings-modal');

            // 显示保存成功提示
            this.showNotification('设置已保存', 'success');

        } catch (error) {
            console.error('❌ 保存设置失败:', error);
            this.showNotification('保存设置失败', 'error');
        }
    }

    /**
     * 应用主题
     * @param {string} theme - 主题名称
     */
    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        console.log(`🎨 应用主题: ${theme}`);
    }

    /**
     * 应用视觉效果级别
     * @param {string} level - 效果级别
     */
    applyEffectsLevel(level) {
        document.documentElement.setAttribute('data-effects', level);
        console.log(`✨ 应用视觉效果: ${level}`);
    }

    /**
     * 应用帧率限制
     * @param {string} fps - 帧率限制
     */
    applyFpsLimit(fps) {
        document.documentElement.setAttribute('data-fps', fps);
        console.log(`🎯 应用帧率限制: ${fps}`);
    }

    /**
     * 启动游戏
     * @param {string} gameId - 游戏ID
     */
    async launchGame(gameId) {
        try {
            if (typeof gameLauncher !== 'undefined') {
                await gameLauncher.launchGame(gameId);
            } else {
                console.error('❌ 游戏启动器未初始化');
            }
        } catch (error) {
            console.error('❌ 启动游戏失败:', error);
            this.showNotification('启动游戏失败', 'error');
        }
    }

    /**
     * 显示通知
     * @param {string} message - 消息内容
     * @param {string} type - 通知类型
     */
    showNotification(message, type = 'info') {
        // 简单的通知实现
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-primary);
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
        `;

        document.body.appendChild(notification);

        // 3秒后自动移除
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    /**
     * 处理键盘事件
     * @param {KeyboardEvent} event - 键盘事件
     */
    handleKeyDown(event) {
        if (event.key === 'Escape' && this.activeModal) {
            event.preventDefault();
            this.hideModal(this.activeModal);
        }
    }

    /**
     * 销毁模态框管理器
     */
    destroy() {
        document.removeEventListener('keydown', this.handleKeyDown);
        this.hideAllModals();
        this.isInitialized = false;
    }
}

// 创建全局模态框管理器实例
const modalManager = new ModalManager();

// 导出服务
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ModalManager, modalManager };
} else {
    window.modalManager = modalManager;
}
